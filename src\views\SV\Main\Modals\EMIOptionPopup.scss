.EMIOptionPopup {
  .MuiDialog-scrollPaper {
    justify-content: right;

  }

  .tabs {
    .MuiButtonBase-root {
      min-width: auto;
      margin-right: 35px;
      font: normal normal normal 14px/19px Roboto;
      color: #2e2e2e;
      font-weight: 600;
    }

    .Mui-selected {
      opacity: 1;
      border-bottom: 3px solid #0065ff;
      color: #0065ff;
      border-radius: 2px;
      font-weight: 600;
    }
  }

  .MuiDialog-paperWidthSm {
    width: 600px;
    max-height: 100%;
    height: 100%;
    border-radius: 0px;
    margin-right: 0px;

    .MuiDialogContent-root {
      padding:0px  10px !important;
    }

    th {

      font: normal normal 500 12px/22px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 1;
      padding: 5px 8px;
      border-bottom: 1px solid #DFE1E6;
    }

    td {
      font: normal normal 500 14px / 20px Roboto;
      letter-spacing: 0px;
      color: #253858;
      padding: 5px 8px;
      border-bottom: 1px solid #DFE1E6;

      span {
        text-align: left;
        font: normal normal normal 12px/20px Roboto;
        letter-spacing: 0px;
        color: #253858;
      }
    }

    .MuiFormGroup-root {
      margin-bottom: 17px;
    }

    .MuiFormControlLabel-root {
      margin-left: 0px;
    }

    .MuiTypography-root {
      opacity: 1;
      text-align: left;
      font: normal normal 18px/24px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      font-weight: 600;
      padding-left: 14px;
    }

    .checkEmiBtn {
      background: #0065ff;
      border-radius: 8px;
      letter-spacing: 0.17px;
      padding: 8px;
      width: 160px;
      border: none;
      color: #ffffff;
      font: normal normal 600 12px/21px Roboto;
      cursor: pointer;
      outline: none;
      margin-top: 25px;
    }

    .disabledInput {
      background: #ddd;
      opacity: 0.5;
      border-radius: 8px;
      letter-spacing: 0.17px;
      padding: 8px;
      width: 160px;
      border: none;
      color: #ffffff;
      font: normal normal 600 12px/21px Roboto;
      cursor: pointer;
      outline: none;
      margin-top: 25px;
    }

    h5 {
      text-align: left;
      font: normal normal 500 14px/26px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 1;
      margin-bottom: 10px;

      .noEligble {
        background-color: #ffbcbc59;
        color: #c11d1d;
      }

      label {
        background: #E3FCEF 0% 0% no-repeat padding-box;
        border-radius: 4px;
        padding: 5px 6px;
        text-align: center;
        font: normal normal 500 12px/14px Roboto;
        letter-spacing: 0px;
        color: #36B37E;
        text-transform: uppercase;
      }

    }

    .MuiPaper-rounded {
      box-shadow: 0px 3px 8px rgba(0, 101, 255, 0.1607843137);
      border-radius: 8px;
      margin-bottom: 17px;

      &::before {
        display: none;
      }

      .RadioBtn {
        .Mui-checked {

          svg {
            color: #0065FF;
          }
        }
      }
    }

    .MuiAccordionSummary-content {
      font: normal normal 500 14px/26px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 1;
      align-items: center;
    }
  }

  h4 {
    font: normal normal 600 16px/24px Roboto;
    margin-bottom: 5px;
    margin-top: 10px;
    color: #253858;
  }

  p {
    letter-spacing: 0.17px;
    color: #808080;
    font: normal normal 12px/21px Roboto;
    margin-bottom: 15px;
  }

  .tabs {
    padding: 5px;

    .MuiTabs-indicator {
      display: none;
    }

    h6 {
      font: normal normal 18px/24px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      font-weight: 600;
    }

    .MuiTab-textColorInherit {
      min-width: auto;
      text-transform: capitalize;
      margin-right: 35px;
      font: normal normal normal 14px/19px Roboto;
      color: #2e2e2e;
      font-weight: 600;
    }

    .MuiTab-textColorInherit.Mui-selected {
      opacity: 1;
      border-bottom: 3px solid #0065ff;
      color: #0065ff;
      border-radius: 2px;
      font-weight: 600;
    }
  }

  .useExitMobileNo {
    position: relative;
    top: -16px;
    margin-top: 30px;
    margin-left: 16px;

    .MuiFormControlLabel-label {
      color: #253858;
      font-family: Roboto;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }

    svg {
      font-size: 24px;
      color: #BBBBBB;
    }

    .Mui-checked {
      svg {
        color: #0065FF;
      }
    }
  }

  .MuiButtonBase-root {
    margin-right: -5px;
  }

  .timerBtn {
    font: normal normal 14px Roboto;
    letter-spacing: 0.22px;
    color: #253858;
    padding: 2px 8px;
    font-weight: 600;
    display: inline-block;
    background: #eaeaea 0% 0% no-repeat padding-box;
    border: 1px solid #cecece;
    border-radius: 8px;
    opacity: 1;
    margin-top: 5px;

    svg {
      font-size: 18px;
      margin-right: 4px;
    }

    p {
      margin-bottom: 0px;
      float: left;
      padding: 3px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .timerBtn.disabled {
    color: #cccccc;
  }
}