import React, { useEffect } from "react";

// import RightBlock from "./RightBlock";
import { Grid } from "@mui/material";
// import { makeStyles } from "@mui/material/styles";
import { makeStyles, useTheme } from '@mui/styles';
import { useMediaQuery } from '@mui/material';
import ErrorBoundary from "../../hoc/ErrorBoundary/ErrorBoundary";
// import Footer from "./Footer/Footer";
import { connect, useSelector } from "react-redux";
// import CallPickPopup from "./Main/Modals/CallPickPopup";
import { setRefreshLead,setRefreshCustomerId } from "../../store/actions/SalesView/SalesView";
import LeadList from "../SV/Main/LeadList";
import RightBar from "../SV/RightBar";
import Comments from "../SV/Main/Comments";
import rootScopeService from "../../services/rootScopeService";
import Header from "../SV/Header/Header";
import User from "../../services/user.service";
import TopBarCustomerAccess from "../SV/Main/TopBarCustomerAccess";
import { useState } from "react";
import { getScriptURLs, IsApptfeedbackLead } from "../../services/Common";
import { SmeLeadStatusContainer } from "../SV/RightBlock/SmeLeadStatusContainer";
import Main from "../SV/Main/Main";
import RightBlock from "../SV/RightBlock/RightBlock";
import NoLeadToWork from "../SV/Main/NoLeadToWork";
import { useScript } from "../../hooks/useScript";
import AppointmentFeedback from "../SV/Main/AppointmentFeedback";
import CallBackDetails from "../SV/RightBlock/CallBackDetails";

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    width: "95%",
    backgroundColor: theme.palette.background.paper,
  },
}));


function AppointmentView(props) {
  const classes = useStyles();
  const theme = useTheme();

  const isDesktop = useMediaQuery(theme.breakpoints.up('md'), {
    defaultMatches: true
  });
  const isLargeDesktop = useMediaQuery(theme.breakpoints.up('lg'), {
    defaultMatches: true
  });
  const isTablet = useMediaQuery(theme.breakpoints.up('sm'), {
    defaultMatches: true
  });
  const isMobile = useMediaQuery(theme.breakpoints.up('xs'), {
    defaultMatches: true
  });
  let [ParentLeadId, RefreshLead, noOfLeadCardsToShow,RefreshCustomerId] = useSelector(state => {
    let { parentLeadId, RefreshLead, noOfLeadCardsToShow,RefreshCustomerId } = state.salesview;
    return [parentLeadId, RefreshLead, noOfLeadCardsToShow,RefreshCustomerId]
  });
  const allleads = useSelector(state => state.salesview.allLeads);
  let Ispriority = rootScopeService.getPriority();
  const [urls, setUrls] = useState([]);

  const parentLead = allleads.filter((item) => (item.LeadID === ParentLeadId));
  let rejectedLead = false;
  if(parentLead){
    if(parentLead && Array.isArray(parentLead) && parentLead.length > 0 && parentLead[0]['StatusMode'] == "N"){
      rejectedLead = true;
    }
  }

  let maxLeadCardsToShow = () => {
    switch (true) {
      case isLargeDesktop:
        return 2;
      case isDesktop:
        return 2;
      case isTablet:
        return 2;
      case isMobile:
        return 1;
      default:
        return 1;
    }
  }
  // const [urls, setUrls] = useState([]);  
  // let Ispriority = rootScopeService.getPriority();
  useEffect(() => {
    // lead refresh on browser back
    window.addEventListener('popstate', function (event) {
      props.setRefreshLeadToRedux(true);
    })
    
    setUrls(getScriptURLs());

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useScript(urls);
  const getLeadListCol = (breakpoint) => {
    if (noOfLeadCardsToShow === 0) return false;
    const _noOfLeadCardsToShow = Math.min(noOfLeadCardsToShow, maxLeadCardsToShow())

    // return isDesktop
    //     ? Math.min(noOfLeadCardsToShow * 4, 12)
    //     : Math.min(noOfLeadCardsToShow * 3, 9);

    switch (breakpoint) {
      case 'lg':
        return Math.min(_noOfLeadCardsToShow * 4, 12);
      case 'md':
        return Math.min(_noOfLeadCardsToShow * 6, 12);
      case 'sm':
        return Math.min(_noOfLeadCardsToShow * 6, 12);
      case 'xs':
        return 12;
      default:
        return 12;
    }
  }
   let isNoLeadToWorkPage = !(RefreshLead || rootScopeService.getCustomerId());
  return (
    <>
      <div className={`${classes.root} wrapper leadview customeraccessview`}>

        <Grid container spacing={1}>

          <Grid item sm={12} md={12} xs={12}>
            {(User.RoleId === 13 && Ispriority) ?
              <ErrorBoundary name="Header">
                <Header></Header>
              </ErrorBoundary>
              : null
            }
          </Grid>         
         
           <div className="AppointMentView">
          <Grid container spacing={2}>
            <TopBarCustomerAccess />
          </Grid> 
          </div>
          {IsApptfeedbackLead() &&
            <Grid item sm={3} md={3} xs={12}>
              <ErrorBoundary name="CallBackDetails ">
                  <CallBackDetails/>                 
              </ErrorBoundary>
            </Grid>
          }
          <Grid item sm={(isDesktop || isNoLeadToWorkPage) ? 9 : 12} xs={12}>
            <ErrorBoundary name="Main">
              {!isNoLeadToWorkPage ? '': <NoLeadToWork />}
            </ErrorBoundary>
          </Grid>
          {!isNoLeadToWorkPage && <>
          <Grid item container spacing={2} xs={12}>
            <ErrorBoundary name="Lead Detail">
              { IsApptfeedbackLead() && !rejectedLead && 
                <Grid item sm={12} md={12} xs={12}>
                  <AppointmentFeedback/>
                </Grid>
              }
              <Grid item sm={getLeadListCol('sm')} md={getLeadListCol('md')} lg={getLeadListCol('lg')} xs={getLeadListCol('xs')}>
                <ErrorBoundary name="Leads">
                  <div className="leadview">
                    <LeadList leadViewOnly={rootScopeService.getProductId() == 219} leadCardToShow={Math.min(noOfLeadCardsToShow, maxLeadCardsToShow())} />
                  </div>
                </ErrorBoundary>
              </Grid>
              <ErrorBoundary name="Comments">
              <Grid xs={4} md={4} >
                <Comments className="MarginTopLeft" />
              </Grid>
              </ErrorBoundary>
              {!IsApptfeedbackLead() &&
                <ErrorBoundary name="SmeLeadStatus">
                <Grid xs={4} md={4}>
                  <SmeLeadStatusContainer className="MarginTopLeft"/>
                </Grid>
                </ErrorBoundary> 
              }
            </ErrorBoundary>
          </Grid>

         
          </>}
        </Grid>
        
      </div>
          
      <ErrorBoundary name="RightBar">
        <RightBar leadViewOnly={true} />
      </ErrorBoundary>

    </>
  );
}

const mapDispatchToProps = (dispatch) => {
  return {
    setRefreshLeadToRedux: (value) =>
      dispatch(setRefreshLead({ RefreshLead: value })),
      setRefreshCustomerIdToRedux: (value) => dispatch(setRefreshCustomerId({ RefreshCustomerId: value })),
  };
};
export default connect(() => ({}), mapDispatchToProps)(AppointmentView);