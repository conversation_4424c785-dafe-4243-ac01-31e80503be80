import React, { useEffect, useState } from "react";
import { Grid } from "@mui/material";
import Attempts from "./Dialogs/Attempts";
import AddMoreLeads from "./Dialogs/AddMoreLeads";
import Button from '@mui/material/Button';
import { connect, useDispatch, useSelector } from "react-redux";
import rootScopeService from "../../../services/rootScopeService";
import { CALL_API } from "../../../services";
import { GetAgentAllLeadsService, getCheckForNoCallingLead, GetVirtualNumberListService, IsCustomerAccess, IsLeadContent, IsRemoveConnectCallSF } from "../../../services/Common";
import User from "../../../services/user.service";
import { useSnackbar } from "notistack";
import ErrorBoundary from "../../../hoc/ErrorBoundary/ErrorBoundary";
import { clearRedux, setIsAnswered, setIsAutoDone, setLstAgentLeads, setRefreshAgentStats, setRefreshLead, updateStateInRedux } from "../../../store/actions/SalesView/SalesView";
import Common from "../../../services/Common";
import { GetOneLeadService } from "../../../services/Common";
import { useInterval } from "../Main/helpers/useInterval";
import CommonHelper from "../Main/helpers/Common";
import AddDetailsContainer from "./Dialogs/AddDetailsContainer";
import { markVideoCallAgentIdle } from "../../../helpers/commonHelper";
import { SV_CONFIG } from "../../../appconfig";
import SMEVirtualNumbers from "../Main/SMEVirtualNumbers";
import PerLifeRaterSME from "../Main/PerLifeRaterSME";
import SelectCallingNumberPopup from "../RightBlock/Modals/SelectCallingNumberPopup";


const ActionButtons = (props) => {
    const [openModal, setOpenModal] = useState(''); // '', 'attempts', 'addDetails', 'addMoreLead'
    const parentLeadId = useSelector(state => state.salesview.parentLeadId) || 0;
    const IsAnswered = useSelector(state => state.salesview.IsAnswered) || false;
    const IsAutoDone = useSelector(state => state.salesview.IsAutoDone);
    const IsRenewal = useSelector(state => state.salesview.IsRenewal);
    const StampingPanel = useSelector(state => state.salesview.StampingPanel);
    const RenewalStampingFlag = useSelector(state => state.salesview.RenewalStampingFlag);
    const next5leadsfromRedux = useSelector(state => state.salesview.next5leads);
    const callableVirtualNumber = useSelector(state => state.salesview.callableVirtualNumber);
    const LeadAssignedManagerId = useSelector(state=> state.salesview.LeadAssignedManagerId);
    const LeadAssignedAgentId = useSelector(state=> state.salesview.LeadAssignedAgentId);
    const ProductId = rootScopeService.getProductId();
    const [UnAnsweredSummary, setUnAnsweredSummary] = useState([]);
    const [ConnectCallSF, setConnectCallSF] = useState({});
    const { enqueueSnackbar } = useSnackbar();
    const dispatch = useDispatch()
    const handleClose = () => {
        setOpenModal('');
        dispatch(updateStateInRedux({ key: "callableVirtualNumber", value: { OpenCallableNumbers: false } }));
    };
    const [VirtualNumberList, setVirtualNumberList] = useState(false);
    const isSMEVNPopUp = useSelector(({salesview }) => salesview.isSMEVNPopUp);
    const AgentStats = useSelector(state => state.salesview.AgentStats);
    const agentdata = Array.isArray(AgentStats) && AgentStats.length > 0 ? AgentStats[0] : null;
    const [LstAgentCallableLeads, setLstAgentCallableLeads] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [openByVN, setOpenByVN] = useState(false);
    const [ShowVNOption, setShowVNOption] = useState(false);
    var leadCollection = useSelector(state => state.salesview.allLeads);
    let UserProd = 0;
    let Ispriority;
    let isOneLead = window.localStorage.getItem('isOneLead')
    if (isOneLead != null && isOneLead == "true") {
        Ispriority = true;
    }
    useInterval(() => {
        var CallInfo = window.localStorage.getItem("ConnectCallSF");
        var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;
        if (onCall) {
            CallInfo = JSON.parse(CallInfo);
            setConnectCallSF(CallInfo);
        }
        else {
            setConnectCallSF(null);
        }
    }, 1000);

    const ShowUnAnsweredSummary = () => {

        const input = {
            url: `onelead/api/LeadPrioritization/GetUnAnsweredSummary/${parentLeadId}/${ProductId}`,
            method: 'GET', service: 'MatrixCoreAPI',
        }
        CALL_API(input).then((result) => {
            if (result) {
                setUnAnsweredSummary(result);
            }
        });
    }

    // const OpenAddDetailsInfo = () => {
    //     GetCustomerEmail();
    // }

    //Add more leads
    let PriorityReasonId = 1;

    const GetAgentAllLeads = function () {
        if (Ispriority) {
            GetAgentAllLeadsService(false).then((resultData) => {
                if (Array.isArray(resultData) && resultData.length > 0) {
                    // setLstAgentLeads(resultData);
                    //setIsLoading(false);
                    let leadObj = JSON.stringify(resultData);
                    window.localStorage.setItem('searchObj', leadObj);
                    dispatch(setLstAgentLeads({ LstAgentLeads: resultData }))
                }
            }).catch(err => {
                console.error("ERROR AT GetAgentAllLeads", err);
            });

        }
    };

    const GetAgentCallableAllLeads = function () {
        if (Ispriority) {
            setIsLoading(true);
            GetAgentAllLeadsService(true).then((resultData) => {
                if (resultData != null) {
                    setLstAgentCallableLeads(resultData);
                    setIsLoading(false);
                    // let leadObj = JSON.stringify(resultData);
                    // window.localStorage.setItem('searchObj', leadObj);
                    //Show Subproduct for smeLead agent //Todo
                    if (UserProd === 131 && LstAgentCallableLeads !== undefined) {

                        // topNavService.GetSMESubPrdoductData().then(function (resultData) {
                        //     SMESubPrdoductData = resultData.data;
                        //     if (SMESubPrdoductData.SubProducts != undefined && SMESubPrdoductData.SubProducts != null) {
                        //         ShowSubProduct = true;
                        //         LstAgentCallableLeads.forEach((elem1, index) => {
                        //             LstAgentCallableLeads[index].InvestmentType = SMESubPrdoductData.SubProducts[elem1.InvestmentTypeId] ? SMESubPrdoductData.SubProducts[elem1.InvestmentTypeId] : null;

                        //         });
                        //     }
                        // });
                    }
                }
                setIsLoading(false);
            }).catch((err) => {
                console.error(err);
            })
        }
    };

    const GetOneLead = (UserId, productId) => {
        // window.sessionStorage.setItem("reloadNext5Lead", true);
        // var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;

        // if (onCall) {
        //     return new Promise((resolve, reject) => {
        //         resolve({});
        //     });
        // }
        // console.log("Flow1: get 1lead called", new Date());

        return GetOneLeadService(UserId, productId).then(function (resultData) {
            // console.log("Flow1: get 1lead  response", new Date(), resultData)
            props.setRefreshAgentStatsToRedux(true);
            // window.sessionStorage.setItem("reloadNext5Lead", true);
            window.localStorage.removeItem('IsEmailOnlyTime');
            window.localStorage.removeItem('IsEmailOnly');
            window.localStorage.removeItem('IsPrimaryMobile');

            return resultData;
        });
    }

    const ShowAllLeads = () => {
        //if (!AllLeadmodalInstanceOpened) {
        //  AddLeadsToQueueNoLeads = [];
        GetAgentCallableAllLeads();
        setOpenModal('addMoreLead')
        //}

        // AllLeadmodalInstance.opened.then(function () {
        //     AllLeadmodalInstanceOpened = true;
        //     $rootScope.popupopened = true;
        // });

        // we want to update state whether the modal closed or was dismissed,
        // so use finally to handle both resolved and rejected promises.
        // AllLeadmodalInstance.result.finally(function (selectedItem) {
        //     AllLeadmodalInstanceOpened = false;
        //     $rootScope.popupopened = false;
        // });
    };



    const ValidateLeadClose = function (skip) {
        // console.log("Flow1: ValidateLeadClose Start: ", new Date());

        props.setIsAnsweredToRedux(false);
        //localStorage.setItem("ActiveSchdular", "0");

        if (agentdata && agentdata.status && agentdata.status.toUpperCase() === "VIDEOMEET") {
            markVideoCallAgentIdle(agentdata, 3);
        }

        if (
            rootScopeService.getPriority()
            // true
            // User.IsProgressive === true
        ) {

            props.setIsAutoDoneToRedux(0); // to reset timer
            console.log("connectCallSF -> removed from localstorage inside ValidateLeadClose at 203", new Date());
            if(IsRemoveConnectCallSF())
                {
                    window.localStorage.removeItem('ConnectCallSF');
                }

            window.localStorage.removeItem("onCall");
            window.localStorage.removeItem("InSchedular");
            GetOneLead(User.UserId, rootScopeService.getProductId())
                .then((result) => {
                    if (!(next5leadsfromRedux && next5leadsfromRedux.length === 0)) {
                        enqueueSnackbar("Lead is Marked done", { variant: 'success', autoHideDuration: 2000 })
                    }
                    if (result && Object.entries(result).length > 0) {
                        let leadid = result.LeadID;
                        let custId = result.CustID;
                        let Productid = result.ProductID;
                        let ReasonToCome = result.Reason;
                        let ReasonId = result.LeadCategory;
                        if (!User.IsProgressive && leadid && custId && ProductId) {
                            OpenSalesView(custId, Productid, leadid, 'o', ReasonToCome, ReasonId);
                            props.setRefreshLeadToRedux(true);
                        }
                    }
                });

            if (next5leadsfromRedux && next5leadsfromRedux.length === 0) {
                ShowAllLeads();
                setOpenModal('addMoreLead')
            }
            //queueLogin();
        } else {
            // if (!(isOneLead == "true" && User.IsProgressive)) {
            //     //topNavControllerOneLead
            //     // voiceemitStop(); // To stop Recording
            // }
            //Todo redux
            if (RenewalStampingFlag == 0 && (IsRenewal == 1) && (StampingPanel == true) &&
                (User.RoleId === 13) && ([2, 118, 130, 106].includes(rootScopeService.getProductId()))) {

                var data = leadCollection.filter((item) =>
                    (item.StatusMode === 'P' && item.LeadSourceId === 6));

                if (data.length > 0) {
                    let validate = false;
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].StatusId < 13) {
                            validate = true;
                            break;
                        }
                    }
                    if (validate) {
                        enqueueSnackbar("Please select a Renewal SubStatus!!", { variant: 'error', autoHideDuration: 3000, });
                        return false;
                    }
                }
            }
            LeadClose(skip);
        }
        window.localStorage.removeItem('AnswerCallTime');
        window.localStorage.removeItem('onCall');
        GetAgentAllLeads();
    };

    const LeadClose = (skip) => {
        CallleadClose(skip);
    };
    const GetVirtualNumberList = () => {
        try{
            GetVirtualNumberListService(parentLeadId).then((Result) => {
                if(Result && Result.length ==0 )
                {
                    enqueueSnackbar("No Virtual Number mapping exists in the system", {
                        variant: 'error',
                        autoHideDuration: 2000,
                    });
                }
                else if (Result && Result[0] && Result[0].UnansweredAttempt) {
                    enqueueSnackbar("Unanswered attempts are less than 2", {
                        variant: 'error',
                        autoHideDuration: 2000,
                    });
                }
                else
                {
                    setVirtualNumberList(Result);
                    dispatch(updateStateInRedux({ key: "isSMEVNPopUp", value: true }));
                    setOpenByVN(true);  
                }
            });
        }
        catch{

        } 
    }

    const handleChange = (e) => {
        if (e.target.name == "VirtualNo") {

            if (getCheckForNoCallingLead(leadCollection, parentLeadId)) {
                return;
            }
            GetVirtualNumberList();
        }
    }


    const CallleadClose = (skip) => {

        /*notification*/
        let reqData = {
            parentID: parentLeadId,
            UserId: User.UserId,
            action: 9
        };
        Common.SetNotificationAction(reqData);
        let requestData = {
            reqData: {
                EventTime: Date.parse(new Date()),
                LeadId: rootScopeService.getLeadId(),
                UserId: User.UserId,
                EventType: 4,
                ParentId: parentLeadId
            }
        }
        // Common.InsertUpdateAgentProductivity(requestData)
        //     .then((response) => {
                let IsMarkAutoStatus = false;
                if (rootScopeService.getProductId() === 7) {
                    leadCollection.forEach((vdata, key) => {
                        if (vdata.StatusId < 4) {
                            IsMarkAutoStatus = true;
                        }
                    });
                }
                if (IsMarkAutoStatus) {
                    MarkAutoStatus();
                }
                // localStorage.removeItem(User.UserId);
                // props.clearRedux();
                if (IsMarkAutoStatus === false && !rootScopeService.getPriority()) {
                    // window.self.close();
                } else if (rootScopeService.getPriority()) {
                    //nextleadflag = true;
                    //callclick = 0;
                    window.sessionStorage.setItem('oneleadflags', JSON.stringify({
                        leadid: parentLeadId,
                        callclick: 0
                    }));
                    if (skip != 1)
                        enqueueSnackbar("Lead is marked done!", { variant: 'success', autoHideDuration: 3000, });

                    let pause = window.localStorage.getItem("IsPause");
                    if (pause != 1) {
                        OpenOneLead();
                    }
                }
            // });
    };

    const OpenOneLead = () => {
        // todo

        GetOneLead(User.UserId, rootScopeService.getProductId())
            .then((resultData) => {
                let leadid, custId, Productid, ReasonToCome, ReasonId;
                if (resultData != null && resultData != undefined && Object.entries(resultData).length > 0) {
                    leadid = resultData.LeadID;
                    custId = resultData.CustID;
                    Productid = rootScopeService.getProductId();
                    ReasonToCome = resultData.Reason;
                    ReasonId = resultData.LeadCategory;
                    if (leadid == null || leadid == undefined || leadid == 0) {
                        ShowAllLeads();
                        // callclick = 1;
                        // if (User.IsProgressive == true) {
                        //     rootScopeService.setshowDoneButton(true);
                        //     rootScopeService.setShowDone(1);
                        // }
                        return;
                    }
                    //todo : setLogout, showProgressivePlayPause
                    // if (User.IsProgressive && setLogout && window.AgentCall.popwin.oSipSessionCall == null) {
                    //     masterService.updateagentstatus(User.EmployeeId, "LOGOUT");
                    //     //logoutCallWindow("LogoutQueue");
                    //     //var obj = { msg: 'PlayPause', pause: false };
                    //     //var iframe = document.getElementsByClassName('new_record_5lead')[0];
                    //     //iframe.contentWindow.postMessage(obj, '*');

                    //     }
                    // }
                    var url = "../Leads/BlankLogout.aspx?logoutSV=1";
                    setTimeout(function () {
                        window.location.href = url;
                    }, 1000);
                } else {
                    OpenSalesView(custId, Productid, leadid, 'o', ReasonToCome, ReasonId);
                }
            });
    }

    const MarkAutoStatus = () => {
        var reqData = {
            leadID: parentLeadId,
            UserId: User.UserId,
            ProductId: rootScopeService.getProductId()
        }
        const _input = {
            url: `LeadDetails/MarkAutoStatus`,
            method: 'POST',
            service: 'core',
            requestData: { reqData }
        }
        CALL_API(_input).then((response) => {
            // if (PriorityUser.indexOf(User.userid) == -1)
            //     window.self.close();
            // else
            OpenOneLead();

        }).catch(function (reason) {
            // if (PriorityUser.indexOf(User.userid) == -1)
            //     window.self.close();
            // else
            OpenOneLead();
        });
    }

    const OpenSalesView = function (custId, Productid, leadid, openSource, ReasonToCome, ReasonId) {
        // if ($rootScope.loadpage) {
        // if (openSource == 's' && IsCustOnCallWithPb) {
        //     window.alert("You are already on Call with other Customer!")
        //     return;
        // }
        CommonHelper.OpenSalesView(custId, Productid, leadid, openSource, ReasonToCome, ReasonId)
        // var encrytedvalue = btoa(custId + '/' + Productid + '/' + leadid + '/' + openSource);
        // var url = "SalesView.htm#/Sales/" + encrytedvalue;
        // window.location.href = url + "?reason=" + ReasonToCome + "&ReasonId=" + ReasonId;

        //  }
    }

    const ConvertToJSONDate = (date) => {
        var dt = new Date(date);
        var newDate = new Date(Date.UTC(dt.getFullYear(), dt.getMonth(), dt.getDate(), dt.getHours(), dt.getMinutes(), dt.getSeconds(), dt.getMilliseconds()));
        return '/Date(' + newDate.getTime() + ')/';
    }
    // const voiceemitStop = function () {
    //     try {
    //         if (IsCallRecordingUser == true) {
    //             var iframe = document.getElementById('iframecallrecord');
    //             var leadid = parentLeadId;
    //             var data = {
    //                 event: 'stoprecording',
    //                 leadid: leadid
    //             }
    //             iframe.contentWindow.postMessage(data, '*');
    //         }
    //     } catch (exception) {

    //     }
    // }
    const showPerLifeRater = () => {
        if (ProductId === 131 && Array.isArray(leadCollection) && leadCollection.length > 0 && leadCollection[0]?.SubProductTypeId === 1) {
            return true;
        }
        return false;
    }
    const CallByVNVisible = () => {
        if (ProductId == 131) {
            if (User && User.RoleId == 12 && LeadAssignedManagerId && (LeadAssignedManagerId == User.UserId)) {
                setShowVNOption(true);
            }
            else if (User && User.RoleId == 13 && LeadAssignedAgentId && (LeadAssignedAgentId == User.UserId)) {
                let groupList = User.UserGroupList;
                let SmeVNVisibleGrps = SV_CONFIG.SmeVNVisibleGrps;
                if (Array.isArray(SmeVNVisibleGrps) && Array.isArray(groupList)) {
                    for (var i = 0, len = groupList.length; i < len; i++) {
                        if (SmeVNVisibleGrps.indexOf(groupList[i].GroupId) > -1) {
                            setShowVNOption(true);
                            break;
                        }
                    }
                }
            }
            else if (User && [12, 13].indexOf(User.RoleId) == -1) {
                setShowVNOption(true);
            }
        }
    }

    useEffect(() => {
        try {
            if ((LeadAssignedAgentId && LeadAssignedAgentId > 0) || (LeadAssignedManagerId && LeadAssignedManagerId > 0)) {
                CallByVNVisible();
            }
        }
        catch {
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [LeadAssignedAgentId, LeadAssignedManagerId])
    
    useEffect(() => {
        if (IsAutoDone) {
            console.log("connectCallSF -> removed from localstorage inside ValidateLeadClose at 203", new Date());
            const onCall = window.localStorage.getItem("onCall") === "true" ? true : false;
            if (SV_CONFIG.skipAutoDoneWhenOnCall && onCall) {
                props.setIsAutoDoneToRedux(false);
                return;
            }
            ValidateLeadClose();
            props.setIsAutoDoneToRedux(false);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [IsAutoDone])

    return (<>
        <Grid item sm={6} md={12} xs={12}>
            <div className="topbarRight">
                {/* <ButtonGroup color="primary" aria-label="outlined primary button group"> */}
                {Boolean(parentLeadId) && !IsCustomerAccess() && <Button onClick={() => { ShowUnAnsweredSummary(); setOpenModal('attempts') }}>Attempts</Button>}
                {Boolean(parentLeadId) && !IsCustomerAccess() && <Button onClick={() => { setOpenModal('addDetails') }}>Add Details</Button>}
                {/* {(isShowDoneBtn || !ConnectCallSF) && <Button id="btnDone" onClick={() => { ValidateLeadClose(); }}>Done</Button>} */}
                {(!ConnectCallSF || IsAnswered) && !IsLeadContent() && User && parseInt(User.RoleId) === 13 && <Button id="btnDone" onClick={() => { ValidateLeadClose(); }}>Done</Button>}
                {/* </ButtonGroup> */}
                {showPerLifeRater() && <Button onClick={() => { setOpenModal('perliferater') }}>Per Life Rater</Button>}
            </div>
        </Grid>
        {/* Modals & Dialogs */}
        <ErrorBoundary name="attempts"><Attempts open={openModal === 'attempts'} handleClose={handleClose} UnAnsweredSummary={UnAnsweredSummary} /></ErrorBoundary>
        <ErrorBoundary name="addDetails">
            <AddDetailsContainer show={openModal === 'addDetails'} handleClose={handleClose} openModal />
        </ErrorBoundary>
        <ErrorBoundary name="addMoreLead"><AddMoreLeads LstAgentCallableLeads={LstAgentCallableLeads} show={openModal === 'addMoreLead'} handleClose={handleClose} isLoading={isLoading} /></ErrorBoundary>
        <Grid item sm={12} md={12} xs={12} className="topbarRight">
            {ShowVNOption &&
                <Button name="VirtualNo" className="VirtualNoBtn" onClick={handleChange}>Call By Virtual Number</Button>
            }
            {isSMEVNPopUp && <ErrorBoundary name="Virtual Numbers">
                <SMEVirtualNumbers open={isSMEVNPopUp} VirtualNumberList={VirtualNumberList} ParentLeadId={parentLeadId}></SMEVirtualNumbers>
            </ErrorBoundary>}
            {
                callableVirtualNumber && callableVirtualNumber.OpenCallableNumbers &&
                <SelectCallingNumberPopup open={callableVirtualNumber.OpenCallableNumbers} handleClose={handleClose} openByVN={openByVN}></SelectCallingNumberPopup>
            }
        </Grid>
        <ErrorBoundary name="perliferater">
            <PerLifeRaterSME
                show={openModal === "perliferater"}
                handleClose={handleClose}
                ParentLeadId={parentLeadId}
                leadCollection={leadCollection}
            />
        </ErrorBoundary>       
    </>
    )
}


const mapStateToProps = state => {
    return {

    };
};

const mapDispatchToProps = dispatch => {
    return {
        setRefreshAgentStatsToRedux: (value) => dispatch(setRefreshAgentStats({ RefreshAgentStats: value })),
        setIsAutoDoneToRedux: (value) => dispatch(setIsAutoDone({ IsAutoDone: value })),
        setIsAnsweredToRedux: (value) => dispatch(setIsAnswered({ IsAnswered: value })),
        clearRedux: (value) => dispatch(clearRedux()),
        setRefreshLeadToRedux: (value) => dispatch(setRefreshLead({ RefreshLead: value })),
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(ActionButtons);

