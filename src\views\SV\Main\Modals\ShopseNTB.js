import { Accordion, AccordionDetails, AccordionSummary, Checkbox, FormControlLabel, Grid, Radio, RadioGroup, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';
import { TextInput } from '../../../../components';
import React, { useEffect, useState } from "react";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { GetEncryptedMobileNo, SendShopseNTB, GetEncryptedEmailID, FetchShopseFormDetails, GetShopseQuickEligibility } from "../../../../services/Common";
import { useSnackbar } from "notistack";
import rootScopeService from "../../../../services/rootScopeService";
import SelectDropdown from "../../../../components/SelectDropdown";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from "dayjs";
import './EMIOptionPopup.scss';
import { useSelector } from "react-redux";
import { useInterval } from "../helpers/useInterval";
import AccessTimeIcon from '@mui/icons-material/AccessTime';


const GenderOptions = [
    { "Id": "Male", "Name": "Male" }
    , { "Id": "Female", "Name": "Female" }
    , { "Id": "Others", "Name": "Others" }
];

const EmploymentTypeOptions = [
    { "Id": "Salaried", "Name": "Salaried" },
    { "Id": "Self Employed", "Name": "Self Employed" },
    { "Id": "Student", "Name": "Student" },
    { "Id": "Others", "Name": "Others" }
];

const MaritalStatusOptions = [
    { "Id": "Single", "Name": "Single" },
    { "Id": "Married", "Name": "Married" },
    { "Id": "Divorced", "Name": "Divorced" },
    { "Id": "Widow", "Name": "Widow" },
    { "Id": "Other", "Name": "Other" }
];

let InitialDetails = {
    Firstname: "",
    Lastname: "",
    PANcard: "",
    Dateofbirth: "",
    Gender: "",
    Pincode: "",
    MaritalStatus: "",
    MonthlyIncome: "",
    EmploymentType: "",
    InputAmount: 0,
    Emailaddress: "",
    InputMobile: "",
    EncMobile: "",
    EncEmailaddress: ""
};


export const ShopseNTB = (props) => {
    let [ShopseNTBDetails, setShopseNTBDetails] = useState(InitialDetails);
    let [ShopseEligibilityId, setShopseEligibilityId] = useState(null);
    let [ShopseEligibilityDetails, setShopseEligibilityDetails] = useState(null);
    let [expanded, setExpanded] = React.useState(0);
    let [IsExistingEmailID, setIsExistingEmailID] = useState(true);
    let [IsExistMobileNo, setIsExistMobileNo] = useState(true);

    const { enqueueSnackbar } = useSnackbar();
    const LeadID = rootScopeService.getLeadId();
    let [timer, setTimer] = useState(60);
    let [RenewalLead, setRenewalLead] = useState(0);
    const [allLeads] = useSelector(state => {
        let { allLeads } = state.salesview;
        return [allLeads];
    });



    useInterval(function () {
        if (ShopseEligibilityId != "" && timer > 0) {
            if (timer > 0) {
                setTimer(timer => timer - 1);
                if (timer <= 0) {
                    setTimer(0);
                }
            }

        }
    }, 1000);


    const visibleLeads = [];
    for (let key in allLeads) {
        if (allLeads[key].LeadSource == "Renewal" && [1, 2, 3, 4, 11].indexOf(allLeads[key].StatusId) !== -1) {
            visibleLeads.push(allLeads[key].LeadID)
        }
    }

    // Validation utilities
    const isValidEmail = (email) => {
        const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        return emailRegex.test(email);
    };

    const isValidPAN = (pan) => {
        const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
        return panRegex.test(pan);
    };

    const isValidDOB = (dob) => {
        // Date format YYYY-MM-DD
        const dobRegex = /^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$/;
        if (!dobRegex.test(dob)) return false;

        const [year, month, day] = dob.split("-").map(Number);
        const date = new Date(year, month - 1, day);
        const today = new Date();
        const age = today.getFullYear() - date.getFullYear();

        // Check if valid date and age is between 18 and 65
        return date.getDate() === day &&
            date.getMonth() === month - 1 &&
            date.getFullYear() === year &&
            age >= 21 && age <= 59;
    };

    const handleChange = (e) => {
        let { name, value, type } = e.target;

        switch (name) {
            case "RenewalLead":
                setRenewalLead(value);
                setShopseEligibilityDetails(null);
                break;
            case "Mobilenumber":
                setShopseNTBDetails({ ...ShopseNTBDetails, InputMobile: value });
                break;
            case "Amount":
                setShopseNTBDetails({ ...ShopseNTBDetails, InputAmount: value });
                break;
            case "IsExistMobileNo":
                value = e.target.checked;
                setIsExistMobileNo(value);
                if (e.target.checked == false) { setShopseNTBDetails({ ...ShopseNTBDetails, InputMobile: "" }) }
                else { setShopseNTBDetails({ ...ShopseNTBDetails, InputMobile: ShopseNTBDetails.EncMobile }) }
                break;
            case "Firstname":
                setShopseNTBDetails({ ...ShopseNTBDetails, Firstname: value });
                break;
            case "Lastname":
                setShopseNTBDetails({ ...ShopseNTBDetails, Lastname: value });
                break;
            case "Emailaddress":
                setShopseNTBDetails({ ...ShopseNTBDetails, Emailaddress: value });
                break;
            case "IsExistingEmailID":
                value = e.target.checked;
                setIsExistingEmailID(value);
                if (e.target.checked == false) { setShopseNTBDetails({ ...ShopseNTBDetails, Emailaddress: "" }) }
                else { setShopseNTBDetails({ ...ShopseNTBDetails, Emailaddress: ShopseNTBDetails.EncEmailaddress }) }
                break;
            case "PANcard":
                value = value.toUpperCase();
                setShopseNTBDetails({ ...ShopseNTBDetails, PANcard: value });
                break;
            case "Dateofbirth":
                setShopseNTBDetails({ ...ShopseNTBDetails, Dateofbirth: value });
                break;
            case "Gender":
                setShopseNTBDetails({ ...ShopseNTBDetails, Gender: value });
                break;
            case "Pincode":
                setShopseNTBDetails({ ...ShopseNTBDetails, Pincode: value });
                break;
            case "MaritalStatus":
                setShopseNTBDetails({ ...ShopseNTBDetails, MaritalStatus: value });
                break;
            case "monthlyIncome":
                setShopseNTBDetails({ ...ShopseNTBDetails, MonthlyIncome: value });
                break;
            case "EmploymentType":
                setShopseNTBDetails({ ...ShopseNTBDetails, EmploymentType: value });
                break;
        }
    }

    const IsValid = () => {
        const regx = /^[0-9\b]+$/;
        let isValid = true;
        let errorMessage = "";

        // Mobile number validation
        if (!(IsExistMobileNo) && ((ShopseNTBDetails.InputMobile == "" || ShopseNTBDetails.InputMobile == undefined) || !((ShopseNTBDetails.InputMobile.toString()).length == 10))) {
            errorMessage = "Please enter valid 10 digit Mobile Number";
            isValid = false;
        }

        else if (!(IsExistMobileNo) && !regx.test(ShopseNTBDetails.InputMobile)) {
            errorMessage = "Mobile number should contain only numbers";
            isValid = false;
        }   

        else if (!(IsExistingEmailID) && !ShopseNTBDetails.Emailaddress) {
            errorMessage = "Email address is required";
            isValid = false;
        }
        // Email validation
        
        else if (!(IsExistingEmailID) && !isValidEmail(ShopseNTBDetails.Emailaddress)) {
            errorMessage = "Please enter a valid email address";
            isValid = false;
        }
        // Gender validation
        else if (!ShopseNTBDetails.Gender) {
            errorMessage = "Gender is required";
            isValid = false;
        }
        // Pincode validation
        else if (!ShopseNTBDetails.Pincode) {
            errorMessage = "Pincode is required";
            isValid = false;
        }
        else if (!regx.test(ShopseNTBDetails.Pincode)) {
            errorMessage = "Pincode should contain only numbers";
            isValid = false;
        }
        else if (ShopseNTBDetails.Pincode.length !== 6) {
            errorMessage = "Pincode should be 6 digits";
            isValid = false;
        }
        // Date of birth validation
        else if (!ShopseNTBDetails.Dateofbirth) {
            errorMessage = "Date of birth is required";
            isValid = false;
        }
        else if (!isValidDOB(ShopseNTBDetails.Dateofbirth)) {
            errorMessage = "Please enter a valid date of birth (DD-MM-YYYY). Age should be between 21 and 59 years";
            isValid = false;
        }
        // PAN validation
        else if (!ShopseNTBDetails.PANcard) {
            errorMessage = "PAN card number is required";
            isValid = false;
        }
        else if (!isValidPAN(ShopseNTBDetails.PANcard)) {
            errorMessage = "Please enter a valid PAN card number";
            isValid = false;
        }
        // Marital Status validation
        else if (!ShopseNTBDetails.MaritalStatus) {
            errorMessage = "Marital Status is required";
            isValid = false;
        }
        // Monthly Income validation
        else if (!ShopseNTBDetails.MonthlyIncome) {
            errorMessage = "Monthly Income is required";
            isValid = false;
        }
        else if (!regx.test(ShopseNTBDetails.MonthlyIncome)) {
            errorMessage = "Monthly Income should contain only numbers";
            isValid = false;
        }
        // Employment Type validation
        else if (!ShopseNTBDetails.EmploymentType) {
            errorMessage = "Employment Type is required";
            isValid = false;
        }
        // Amount validation
        else if (!ShopseNTBDetails.InputAmount) {
            errorMessage = "Amount is required";
            isValid = false;
        }
        else if (ShopseNTBDetails.InputAmount == 0 || ShopseNTBDetails.InputAmount < 1000) {
            errorMessage = "Please enter valid Amount (minimum ₹1000)";
            isValid = false;
        }
        else if (!regx.test(ShopseNTBDetails.InputAmount)) {
            errorMessage = "Amount should contain only numbers";
            isValid = false;
        }

        if (!isValid) {
            enqueueSnackbar(errorMessage, {
                variant: 'error',
                autoHideDuration: 3000,
            });
        }

        return isValid;
    }


    const FetchDetails = () => {
        if (IsValid()) {
            let requestData = {
                "leadID": RenewalLead,
                "firstName": ShopseNTBDetails.Firstname,
                "lastName": ShopseNTBDetails.Lastname,
                "mobileNumber": IsExistMobileNo == true ? ShopseNTBDetails.EncMobile : parseInt(ShopseNTBDetails.InputMobile),
                "isExistingMobileNumber": IsExistMobileNo,
                "IsExistingEmailID": IsExistingEmailID,
                "emailAddress": IsExistingEmailID == true ? ShopseNTBDetails.EncEmailaddress : ShopseNTBDetails.Emailaddress,
                "gender": ShopseNTBDetails.Gender,
                "pincode": ShopseNTBDetails.Pincode,
                "dateOfBirth": ShopseNTBDetails.Dateofbirth.split('-').reverse().join('-'),
                "panCard": ShopseNTBDetails.PANcard,
                "maritalStatus": ShopseNTBDetails.MaritalStatus,
                "monthlyIncome": ShopseNTBDetails.MonthlyIncome ? parseInt(ShopseNTBDetails.MonthlyIncome) : "",
                "employmentType": ShopseNTBDetails.EmploymentType,
                "amount": parseInt(ShopseNTBDetails.InputAmount)
            }
            console.log(requestData)
            SendShopseNTB(requestData).then((result) => {
                if (result && result.data && result.data.checkEligibilityId) {
                    setTimer(60)
                    setShopseEligibilityId(result.data.checkEligibilityId)
                }
                else {
                    enqueueSnackbar("Failed to fetch eligibility", {
                        variant: 'error',
                        autoHideDuration: 3000,
                    });
                }
            }).catch((e) => {
                console.log(e);
            })
        }

    }

    const FetchEligibility = () => {
        GetShopseQuickEligibility(ShopseEligibilityId).then((result) => {
            if (result) {
                setShopseEligibilityDetails(result)
                if(result.status == "processing")
                    setTimer(60)
            }
        }).catch((e) => {
            console.log(e);
        })
    }

    const GetMobileNo = () =>{
        GetEncryptedMobileNo(RenewalLead).then((mobileResult) => {
            if (mobileResult) {
                // Find the selected lead
                const selectedLead = allLeads.find(lead => lead.LeadID === RenewalLead);
                if (selectedLead) {
                    const names = selectedLead.Name ? selectedLead.Name.split(' ') : ['', ''];
                    let formattedDOB = '';

                    if (selectedLead.Age && typeof selectedLead.Age === 'string') {
                        const dobMatch = selectedLead.Age.match(/(\d{2})-(\d{2})-(\d{4})/);
                        if (dobMatch) {
                            const [_, day, month, year] = dobMatch;
                            formattedDOB = `${year}-${month}-${day}`;
                        }
                    }

                    // Set all state in one update
                    setShopseNTBDetails(prevState => ({
                        ...InitialDetails, // Reset to initial state first
                        ...prevState,
                        Firstname: names[0] || '',
                        Lastname: names.slice(1).join(' ') || '',
                        Pincode: (selectedLead.Pincode && selectedLead.Pincode !== "0") ? selectedLead.Pincode : '',
                        InputAmount: selectedLead.NoticePremium || 0,
                        Gender: selectedLead.Gender || '',
                        Dateofbirth: formattedDOB,
                        InputMobile: mobileResult,
                        EncMobile: mobileResult
                    }));
                    setIsExistMobileNo(true)
                    // Get encrypted email after mobile is set
                    GetEncryptedEmailID(RenewalLead).then((emailResult) => {
                        if (emailResult) {
                            setShopseNTBDetails(prevState => ({
                                ...prevState,
                                Emailaddress: emailResult,
                                EncEmailaddress: emailResult
                            }));
                            setIsExistingEmailID(true)
                        }
                    }).catch((e) => {
                        console.log(e);
                    });
                }
            }
        }).catch((e) => {
            console.log(e);
        });
    }

    useEffect(() => {
        if (RenewalLead > 0) {
            setTimer(60)
            FetchShopseFormDetails(RenewalLead).then((result) => {
                if (result) {
                    setShopseEligibilityId(result.ShopseEligibilityId);
                    if (result.ShopseEligibilityId == "") {
                        GetMobileNo()
                    }
                }
            }).catch((e) => {
                console.log(e);
            })
        }
    }, [RenewalLead, allLeads]);

    const handleBankChange = (bankname) => {
        if (expanded == bankname) {
            setExpanded(0);
        }
        else {
            setExpanded(bankname);
        }
    }
    return (<>
        <Grid container spacing={2}>
            <SelectDropdown
                name="RenewalLead"
                label="Renewal Lead"
                value={RenewalLead}
                options={visibleLeads}
                labelKeyInOptions='_all'
                valueKeyInOptions="_all"
                handleChange={handleChange}
                sm={12} md={12} xs={12}
            />
        </Grid>
        <br />
        {RenewalLead > 0 && ShopseEligibilityId == "" && <>
            <Grid container spacing={2}>

                <TextInput
                    name="Firstname"
                    label="First name"
                    sm={6} md={6} xs={12}
                    handleChange={handleChange}
                    value={ShopseNTBDetails.Firstname}
                />
                <TextInput
                    name="Lastname"
                    label="Last name"
                    sm={6} md={6} xs={12}
                    handleChange={handleChange}
                    value={ShopseNTBDetails.Lastname}
                />
                <TextInput
                    name="Mobilenumber"
                    label="Mobile number"
                    sm={6} md={6} xs={12}
                    handleChange={handleChange}
                    value={ShopseNTBDetails.InputMobile}
                    pattern="[0-9]"
                    maxLength="12"
                    disabled={IsExistMobileNo}
                />
                <FormControlLabel className="useExitMobileNo"
                    name="IsExistMobileNo"
                    control={<Checkbox color="primary" />}
                    label="Use existing mobile number"
                    value={IsExistMobileNo}
                    onChange={handleChange}
                    checked={IsExistMobileNo}
                />
                <TextInput
                    name="Emailaddress"
                    label="Email address"
                    sm={6} md={6} xs={12}
                    handleChange={handleChange}
                    value={ShopseNTBDetails.Emailaddress}
                    disabled={IsExistingEmailID}
                />
                <FormControlLabel className="useExitMobileNo"
                    name="IsExistingEmailID"
                    control={<Checkbox color="primary" />}
                    label="Use existing email ID"
                    value={IsExistingEmailID}
                    onChange={handleChange}
                    checked={IsExistingEmailID == true}
                />
                <SelectDropdown
                    name="Gender"
                    label="Gender"
                    value={ShopseNTBDetails.Gender}
                    options={GenderOptions}
                    labelKeyInOptions='Name'
                    valueKeyInOptions="Id"
                    handleChange={handleChange}
                    sm={6} md={6} xs={12}
                    required={true}
                />

                <TextInput
                    name="Pincode"
                    label="Pincode"
                    sm={6} md={6} xs={12}
                    handleChange={handleChange}
                    value={ShopseNTBDetails.Pincode}
                    pattern="[0-9]"
                    maxLength="6"
                    required={true}
                />
                <Grid item sm={6} md={6} xs={12}>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <DatePicker
                            label="Date of birth"
                            value={ShopseNTBDetails.Dateofbirth ? dayjs(ShopseNTBDetails.Dateofbirth, "YYYY-MM-DD") : null}
                            onChange={(newValue) => {
                                const formattedDate = newValue ? newValue.format("YYYY-MM-DD") : "";
                                handleChange({ target: { name: "Dateofbirth", value: formattedDate } });
                            }}
                            slotProps={{
                                textField: {
                                    fullWidth: true,
                                    required: true,
                                    error: ShopseNTBDetails.Dateofbirth && !isValidDOB(ShopseNTBDetails.Dateofbirth),
                                    helperText: ShopseNTBDetails.Dateofbirth && !isValidDOB(ShopseNTBDetails.Dateofbirth) ?
                                        "Please enter a valid date. Age should be between 21 and 65 years" : ""
                                }
                            }}
                        />
                    </LocalizationProvider>
                </Grid>
                <TextInput
                    name="PANcard"
                    label="PAN card"
                    sm={6} md={6} xs={12}
                    handleChange={handleChange}
                    value={ShopseNTBDetails.PANcard}
                    required={true}
                />
                <SelectDropdown
                    name="MaritalStatus"
                    label="Marital Status"
                    value={ShopseNTBDetails.MaritalStatus}
                    options={MaritalStatusOptions}
                    labelKeyInOptions='Name'
                    valueKeyInOptions="Id"
                    handleChange={handleChange}
                    sm={6} md={6} xs={12}
                    required={true}
                />

                <TextInput
                    name="monthlyIncome"
                    label="Monthly Income"
                    value={ShopseNTBDetails.MonthlyIncome}
                    handleChange={handleChange}
                    sm={6} md={6} xs={12}
                    required={true}
                />

                <SelectDropdown
                    name="EmploymentType"
                    label="Employment Type"
                    value={ShopseNTBDetails.EmploymentType}
                    options={EmploymentTypeOptions}
                    labelKeyInOptions='Name'
                    valueKeyInOptions="Id"
                    handleChange={handleChange}
                    sm={6} md={6} xs={12}
                    required={true}
                />


                <TextInput
                    name="Amount"
                    label="Amount"
                    sm={6} md={6} xs={12}
                    handleChange={handleChange}
                    value={ShopseNTBDetails.InputAmount}
                    pattern="[0-9]"
                    required={true}
                />
            </Grid>
            <div className="Text-Center">
                <button className="checkEmiBtn" onClick={FetchDetails}>Check EMI eligibility</button>
            </div>
        </>}
        {RenewalLead > 0 && ShopseEligibilityId != "" && <>
            {timer > 0 &&
                <>
                    We are fetching the details...
                    <div className="timerBtn">
                        <p><AccessTimeIcon />
                            {timer}s
                        </p>
                    </div>
                </>
            }
            {timer <= 0 && !ShopseEligibilityDetails && <>
                <div className="Text-Center">
                    <button className="checkEmiBtn" onClick={() => FetchEligibility()}> Check EMI eligibility</button>
                </div>
            </>}
            {timer <= 0 && ShopseEligibilityDetails && ShopseEligibilityDetails.status == "processing" && <>
                <div className="Text-Center">
                    <button className="checkEmiBtn" onClick={() => FetchEligibility()}> Check EMI eligibility</button>
                </div>
            </>}
            {ShopseEligibilityDetails && ShopseEligibilityDetails.data && ShopseEligibilityDetails.data.ntbEligibility == false && <>
                <h5>  <label className="noEligble">NOT ELIGIBLE</label></h5>
            </>}
            {ShopseEligibilityDetails && ShopseEligibilityDetails.data && ShopseEligibilityDetails.data.ntbEligibility == true && <>
                <h5>  <label>ELIGIBLE</label></h5>
            </>}
            
            {ShopseEligibilityDetails && ShopseEligibilityDetails.status == "processing"  && <>
                <h5>  <label>Processing...</label></h5>
            </>}

            {ShopseEligibilityDetails && ShopseEligibilityDetails.status && ShopseEligibilityDetails.status == "error" && <>
                <h5>  <label className="noEligble">Error Occured</label></h5>
            </>}
            {ShopseEligibilityDetails && ShopseEligibilityDetails.data && ShopseEligibilityDetails.data.ntbEligibility == true && ShopseEligibilityDetails.data.eligibilityDetails.map((details, index) => (
                <Accordion name="bankname" expanded={expanded == details.bankCode} onChange={() => { handleBankChange(details.bankCode) }} >
                    <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        aria-controls="panel1-content"
                        id="panel1-header"
                    >
                        {details.bankCode}
                    </AccordionSummary>
                    <AccordionDetails>
                        {
                            (details.bandInfo.minEligibleAmount > 0 && details.bandInfo.maxEligibleAmount > 0 &&
                                <>
                                    Amout  &#8377;{details.bandInfo.minEligibleAmount}-&#8377;{details.bandInfo.maxEligibleAmount}
                                </>
                            )
                            ||
                            (details.bandInfo.minEligibleAmount == 0 &&
                                <>
                                    Amount up to &#8377;{details.bandInfo.maxEligibleAmount}
                                </>
                            )
                            ||
                            (details.bandInfo.maxEligibleAmount == 0 &&
                                <>
                                    Amount starting from &#8377;{details.bandInfo.minEligibleAmount}
                                </>
                            )
                        }
                    </AccordionDetails>
                </Accordion>
            ))}




            <div className="Text-Center">
                <button className="checkEmiBtn" onClick={() => { setShopseEligibilityId(""); setShopseEligibilityDetails(null); GetMobileNo() }}>Raise New Request</button>
            </div>
        </>}
    </>
    );
}
