/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useRef } from "react";
import LeadCard from './LeadCard';
import { CALL_API } from '../../../services/api.service';
import rootScopeService from '../../../services/rootScopeService';
import User from "../../../services/user.service";
import { connect, useDispatch, useSelector } from 'react-redux'
import { set_allleads, set_ManualStamping, set_Renewal, setRefreshLead, setNoOfLeadCardsToShow, updateStateInRedux, setRefreshCustomerId, set_PaymentOverDueCount } from '../../../store/actions/SalesView/SalesView';
import { CONFIG, SV_CONFIG } from "../../../appconfig";
import { Grid, useMediaQuery, useTheme } from "@mui/material";
import Slider from "react-slick";
import { useSnackbar } from "notistack";
import CloseIcon from '@mui/icons-material/Close';
import { API_STATUS, GetCustomerAppointmentData, GetCustomerPitchedIntentService, GetFOSChurnLogicMsgService, IsCustomerAccess, SetCustomerComment, SetRealTimeLeadStatusService, isShowCustomerPitch, IsCustomerIntentFOS, checkUserGroup, GetSmeAnualOpenLead, InvAdvisorVerifyService, MotorAdvisorVerifyService } from "../../../services/Common";
import { GetOfflineCitiesService ,getPrefCommService} from "../../Features/FosHelpers/fosServices";
import { getDistinctCitiesAppointmentMapping } from "../../Features/FosHelpers/fosCommonHelper";
import { useInterval } from "./helpers/useInterval";
import { GetProposalDetailsService } from './helpers/GetProposalDetailsService';
import CovidInvPopup from "./CovidInvPopup";

import { GetCallDetails } from "../../../services/Common";

import FOSEligibleCityPopup from "./Modals/FOSEligibleCityPopup";
import { ChkIsRenewal, GetStatus, gaEventTracker, isFOSChurnAgent } from "../../../helpers";
import { IsHealthRenewalAgent, IsPaymentOverdueVisible, IsRenewalAgent } from "../../../../src/layouts/SV/components/Sidebar/helper/sidebarHelper";
import dayjs from "dayjs";
import CustomerRequirementsCard from "./CustomerRequirementsCard";
import { getSearchObjFromLS, isChatOrIBAgent } from "../../../helpers/commonHelper";
import { ReferralLeadPopup } from "./Modals/ReferralLeadPopup";
import { localStorageCache } from "../../../utils/utility";
import Common from "./helpers/Common";
import { forEach } from "lodash";
import ErrorBoundary from "../../../hoc/ErrorBoundary/ErrorBoundary";
import { PotentLeadsPanel } from "./Modals/PotentLeadsPanel";
import CallIntentByAgentPopup from "../../../layouts/SV/components/Sidebar/components/Modals/CallIntentByAgentPopup";
import ModalPopup from "../../../components/Dialogs/ModalPopup";
import masterService from "../../../services/masterService";
import { checkPolicyExpiry, CheckExpectedDeliveryDate } from "../../Features/FosHelpers/fosCommonHelper";
import PotentialRepeatBuyerPopUp from "./Modals/PotentialRepeatBuyerPopUp";
import { PitchRecommendationPopup } from "./Modals/PitchRecommendationPopup";

const LeadList = (props) => {
  let [parentLeadId, LstAgentLeads] = useSelector(({ salesview }) => [salesview.parentLeadId, salesview.LstAgentLeads]);
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();
  const [leads, setLeads] = useState([]);
  const [groupedLeads, setGroupedLeads] = useState([]);
  let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
  let [RefreshCustomerId] = useSelector(({ salesview }) => [salesview.RefreshCustomerId]);
  const [IsValidInitialNoOfLives, setIsValidInitialNoOfLives] = useState(false);
  let noOfLeadCardsToShow = useSelector(({ salesview }) => salesview.noOfLeadCardsToShow);
  let ObjRestrictCall_errorMsg = useSelector(({ salesview }) => salesview.ObjRestrictCall_errorMsg);
  const CustomerIntentsList = useSelector(({ salesview }) => salesview.CustomerIntentsList);
  const RefreshCustomerPitchedIntent = useSelector(({ salesview }) => salesview.RefreshCustomerPitchedIntent);
  const RefreshFosApptSummary = useSelector(({ salesview }) => salesview.RefreshFosApptSummary);
  const CallType = useSelector(state => state.salesview.CallType);
  const hideCustomerIntentsCard = useSelector(({ salesview }) => salesview.hideCustomerIntentsCard);
  const NavigateLeadId = useSelector(({ salesview }) => salesview.NavigateLeadId);
  let [IsRenewalLead, setIsRenewalLead] = useState(0);
  let [IsRenewalParent, setIsRenewalParent] = useState(0);
  const showCallIntentPopup = useSelector(({ salesview }) => salesview.showCallIntentPopup);
  const PotentialRepeatBuyer = useSelector(({ salesview }) => salesview.PotentialRepeatBuyer);
  const ShowSmeCustomerType = useSelector(state => state.salesview.ShowSmeCustomerType);
  const allleads = useSelector(state => state.salesview.allLeads);

  let [DNCSnackbar, setDNCSnackbar] = useState(0);

  let sliderRef = useRef(null);
  if (ObjRestrictCall_errorMsg) {
    enqueueSnackbar(ObjRestrictCall_errorMsg, {
      variant: 'error',
      anchorOrigin: {
        vertical: 'top',
        horizontal: 'center',
      },
      preventDuplicate: true,
      persist: true
    })
  }

  const [showFOSAlert, setshowFOSAlert] = useState(false);
  const [IsFosCity, setIsFosCity] = useState(false);
  const [SavingPension, setSavingPension] = useState(false);
  const [showSavingPension, setshowSavingPension] = useState(false);
  const [showIsFosCity, setshowIsFosCity] = useState(false);
  const [showQABanner, setshowQABanner] = useState(false);
  const [leadexpiryRenewal, setleadexpiryRenewal] = useState(false);
  const [isInvCovid, setisInvCovid] = useState(false);
  const [showInvCovid, setshowInvCovid] = useState(false);
  const [showPitchRecommendation, setshowPitchRecommendation] = useState(false);
  const [FOSCity, setFOSCity] = useState('');
  const [savingUtm_campaign, setsavingUtm_campaign] = useState('');
  let [PODHistoryTalktime, PODTodayTalkTime, IsShowPODTransfer, IsPODDurationCount, IsPODDoneShow, IsShowPODlink, IsbookedLeadSet, IsRejectedLeadSet] = useSelector(({ salesview }) => [salesview.PODHistoryTalktime, salesview.PODTodayTalkTime, salesview.IsShowPODTransfer, salesview.IsPODDurationCount, salesview.IsPODDoneShow, salesview.IsShowPODlink, salesview.IsbookedLeadSet, salesview.IsRejectedLeadSet]);

  const [UtmCampaign, setUtmCampaign] = useState('');
  const [FOSPopupEligiblecity, setFOSPopupEligiblecity] = useState(false);
  const [PitchShowPopupCount, setPitchShowPopupCount] = useState(0);
  const [OpenReferralLeadPopUp, setOpenReferralLeadPopUp] = useState(false);
  const [IsCustomerNotOptedInWhatsapp,setIsCustomerNotOptedInWhatsapp]=useState(false);


  const theme = useTheme();
  const dispatch = useDispatch();

  let objRestrictCall = {
    CallAttempetID: 0,
    Credit: 0,
    LastCallDate: "",
    _errorMsg: "",
    _makeCall: false
  };
  const [oRestrictCall, setoRestrictCall] = useState(objRestrictCall);
  const DurationPlanPitchShow = [
    {
      "Duration": 180,
      "ProductId": 7
    },
    {
      "Duration": 60,
      "ProductId": 115
    }
  ];
  const closePopup = () => {
    setFOSPopupEligiblecity(false);
  }
  const isLargeDesktop = useMediaQuery(theme.breakpoints.up('lg'), {
    defaultMatches: true
  });
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'), {
    defaultMatches: true
  });
  const isTablet = useMediaQuery(theme.breakpoints.up('sm'), {
    defaultMatches: true
  });
  const isMobile = useMediaQuery(theme.breakpoints.up('xs'), {
    defaultMatches: true
  });

  const getPaymentOverduecasesCount = () => {
    const input = {
      url: "api/SalesView/GetPaymentFailedCasesCount", method: 'GET', service: 'MatrixCoreAPI'
    };
    CALL_API(input).then((result) => {
      Number.isInteger(result) ? props.setPaymentOverDueCount(result) : props.setPaymentOverDueCount(0);
    });
  }

  const GetSMEOccupationList = () => {
    try {
      if (rootScopeService.getProductId() === 131 && visibleLeads[0].SubProductTypeId === 117) {
        masterService.getSMEOccupationList().then(function (Occupations) {
          let UASTypeList = Occupations.filter((e) => e.SubProductTypeId && e.SubProductTypeId == "117" && e.ParentCategoryId == "0");
          let DroneModelList = Occupations.filter((e) => e.SubProductTypeId && e.SubProductTypeId == "117" && e.ParentCategoryId && parseInt(e.ParentCategoryId) > 0);
          dispatch(updateStateInRedux({ key: "UASTypeList", value: UASTypeList }));
          dispatch(updateStateInRedux({ key: "DroneModelList", value: DroneModelList }));
        });
      }
    }
    catch { }
  }

  const getLeads = () => {
    let LeadId = 0;
    let ProductId = 0;
    let CustomerId = 0;
    let input = '';

    if (props.LeadOnlyViewNew && props.LeadOnlyViewNew == true) {//For leadOnly view
      let reqIndex = CONFIG.PUBLIC_URL ? 4 : 2;
      let URL = window.location.pathname.split('/')[reqIndex];
      input = {
        url: 'coremrs/api/LeadDetails/ValidateLeadOnlyURL',
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: { 'URL': URL }
      }
    }
    else {
      LeadId = rootScopeService.getLeadId();
      ProductId = rootScopeService.getProductId();
      CustomerId = rootScopeService.getCustomerId();
      let UserId = User.UserId;
      let url = "";

      if ((SV_CONFIG["IsNewGetLeadDetails"] && SV_CONFIG["IsNewGetLeadDetails"] == true)) {
        url = "coremrs/api/LeadDetails/GetLeadDetailsByCustIDAndProdID";
      } else {
        url = "api/SalesView/GetLeadDetailsByCustIDAndProdID";
      }

      input = {
        url: url,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: { LeadId, ProductId, CustomerId, UserId, roleId: 0 }
      }
    }

    dispatch(updateStateInRedux({ key: 'ShowAssignCriticalComponents', value: null }));
    CALL_API(input).then((result) => {

      if (result && result.GetLeadDetailsByCustIDAndProdIDResult && result.GetLeadDetailsByCustIDAndProdIDResult.Data) {
        let res = result.GetLeadDetailsByCustIDAndProdIDResult.Data;

        let ManualStamping = false;
        let StampingPanel = false;
        let IsHotLead = false;
        let IsPreferredInsurer = false;
        let Leadsexpiry = "";
        let parentLead = null;
        let Renewal = 0;
        let IsClaimOpted = false;

        setisInvCovid(false);
        setIsRenewalLead(0);
        if (Array.isArray(res)) {
          res.forEach((lead) => {
            if (props.LeadOnlyViewNew == true) {
              props.handleRightBarShow();
              if (lead.LeadID === lead.ParentID) {
                dispatch(updateStateInRedux({ key: "LeadOnlyCustId", value: lead.CustomerID }));
                dispatch(updateStateInRedux({ key: "LeadOnlyProductId", value: lead.ProductID }));
              }
            }
            if (lead.LeadID === lead.ParentID) {
              parentLead = lead;
              dispatch(updateStateInRedux({ key: "parentLead", value: parentLead }))
              dispatch(updateStateInRedux({ key: "ReferralLeadId", value: lead.ReferralID }))
              setIsRenewalParent(lead.LeadSourceId === 6 ? 1 : 0);
            }
            if (IsFosCityHomeVisitPoster(lead)) {
              setIsFosCity(true);
            }
            if ([115].indexOf(ProductId) !== -1 && lead.LeadSource == 'Reopen' && lead.Utm_source && (lead.Utm_source == 'Buy_the_Dip_Cust_NRI' || lead.Utm_source == 'Buy_the_Dip_Cust_Dom') && lead.Utm_campaign && lead.Utm_campaign.toLowerCase().includes('cust_bought_in_covid_period_')) {
              setisInvCovid(true);
              setsavingUtm_campaign(lead.Utm_campaign)
            }
            if ([115].indexOf(ProductId) !== -1 && lead.LeadSource == 'Reopen' && lead.Utm_source && lead.Utm_source == 'Pension_Upsell' && lead.Utm_campaign && lead.Utm_campaign == 'Upsell_Pension_on_Non-Issued_New_Inv_Cust') {
              setSavingPension(true);
            }
            // if (lead.SupplierId == 5 && ["300", "3002", "4002", "5002", "6002"].indexOf(String(lead.PaymentSTATUS)) != -1)
            // rootScopeService.setIsIPruLead(true);
            if (lead.LeadID == lead.ParentID && lead.Utm_source == "OfflineAffiliate" && [2, 106, 118, 130].indexOf(ProductId) == -1) {
              // rootScopeService.setIsAffiliatelead(true);
              dispatch(updateStateInRedux({ key: "IsAffiliatelead", value: true }));
            }

            if (ProductId == 117 && lead.PolicyType && lead.PolicyType == 'New' && lead.IsBrandNewCar && lead.LeadID == lead.ParentID) {
              dispatch(updateStateInRedux({ key: "IsBrandNewCar", value: true }));

            }

            if (([2, 106, 117, 130, 118].indexOf(ProductId) != -1) && lead.IsClaimOpted && lead.IsClaimOpted == true) {
              IsClaimOpted = true;
            }
            dispatch(updateStateInRedux({ key: "IsClaimOpted", value: IsClaimOpted }));

            if (lead.PolicyExpiryDate != -62135616600000 && dayjs(lead.PolicyExpiryDate) < dayjs().startOf('month') && !IsHotLead && [1, 2, 3, 4, 11].includes(lead.StatusId)) {
              IsHotLead = true;
            }
            if (lead.PreferredInsurer && !IsPreferredInsurer) {
              IsPreferredInsurer = true;
            }
            if (lead.PolicyExpiryDate && lead.PolicyExpiryDate != -62135616600000 && dayjs(lead.PolicyExpiryDate) >= dayjs().subtract(40, 'day') && dayjs(lead.PolicyExpiryDate) <= dayjs().add(7, 'day') && [1, 2, 3, 4, 11].includes(lead.StatusId)) {
              Leadsexpiry = Leadsexpiry + "," + lead.LeadID;
            }
            //else if (lead.LeadID == lead.ParentID && ([2, 106, 118, 130].indexOf($scope.ProductId) != -1) && lead.LeadSourceId == 6) {
            //Enable Manual Stamping Panel Even if Parent is Fresh
            else if (([2, 106, 118, 130].indexOf(ProductId) !== -1)) {
              //if (lead.LeadSourceId == 6) {
              ManualStamping = true;
              StampingPanel = true;
              //}                                
            }
            if (lead.LeadSourceId === 6) {
              Renewal = 1;
              setIsRenewalLead(1);
            }
            if ([101, 131, 139, 158, 159, 160, 165, 148, 155, 163, 170, 171, 151].indexOf(ProductId) !== -1)
              StampingPanel = true;

            if (rootScopeService.getProductId() === 3) {
              var requestData = { "LeadId": lead.LeadID, "ProductId": rootScopeService.getProductId() }

              GetProposalDetailsService(requestData).then(function (data) {
                if (Array.isArray(data.ProposalDetails) && data.ProposalDetails.length > 0) {
                  data.ProposalDetails.forEach(function (proposal, key) {
                    proposal.Profile.CoverDetails.forEach(function (member, key) {
                      lead.TravelStartDate = member.TravelStartDate
                      lead.TravelEndDate = member.TravelEndDate
                    });
                  });
                }

              }, function () {
              });
            }
          });


          if ([2, 7, 1000, 101, 131, 115].indexOf(rootScopeService.getProductId()) !== -1) {
            if (parentLead) {
              getUtmCampaign(parentLead);
            }
          }
          setLeads(res);
          groupLeads(res);

        }
        props.SetLeadsToRedux(res);

        props.setManualStampingToRedux(ManualStamping);
        dispatch(updateStateInRedux({ key: 'StampingPanel', value: StampingPanel }));
        dispatch(updateStateInRedux({ key: 'IsHotLead', value: IsHotLead }));
        dispatch(updateStateInRedux({ key: 'IsPreferredInsurer', value: IsPreferredInsurer }));
        dispatch(updateStateInRedux({ key: 'Leadsexpiry', value: Leadsexpiry }));
        props.setRenewalToRedux(Renewal);

        isLeadAgentAssigned(parentLead, res);

      }
    })
  }
  const isLeadAgentAssigned = (parentLead, _leads) => {
    let _isLeadAgentAssigned = false;
    let allowedRenewalProducts = SV_CONFIG.ShowAllComponentRenewalProds || [117];
    const stopAgentRestrictionPrdcts = SV_CONFIG['StopAgentRestrictionPrdcts'] || [];

    if (
      SV_CONFIG['StopAgentRestriction']
      || (Array.isArray(User.ProductList) && User.ProductList.some(product => stopAgentRestrictionPrdcts.includes(product.ProductId)))
      || User.RoleId !== 13
    ) {
      _isLeadAgentAssigned = true;
    }
    else if (checkIBcallType(parentLead)) {
      _isLeadAgentAssigned = true;
    }
    else if (parentLead.LeadAssignedUser === User.UserId) {
      _isLeadAgentAssigned = true;
    }
    else if (Array.isArray(User.ProductList) && User.ProductList.length > 0) {
      if (
        User.IsChat || User.IsInbound
        || (rootScopeService.getProductId() === 2 && ChkIsRenewal(_leads, false) && User.IsRenewal && rootScopeService.getUserForCriticalAc() === User.UserId)
        || (allowedRenewalProducts.includes(rootScopeService.getProductId()) && User.IsRenewal)
        || (rootScopeService.getProductId() === 131)
      ) {
        if (User.ProductList.some(product => product.ProductId == rootScopeService.getProductId())) {
          _isLeadAgentAssigned = true;
        }
      }
    }

    dispatch(updateStateInRedux({ key: 'ShowAssignCriticalComponents', value: _isLeadAgentAssigned }));
  }
  const checkIBcallType = (parentLead) => {
    let isIbCallType = false;
    let connectedLead = -1;
    let callType = CallType || localStorage.getItem("calltype");
    let connectCallSF = Common.getConnectCallSFFromLS();
    if (connectCallSF) {
      connectedLead = connectCallSF.LeadID;
    }
    // if opened lead is connected on Predictive/IB, 
    // these lead might get open on salesview before assignment
    if (
      parentLead.LeadID === connectedLead && (
        callType && (callType.indexOf("IB")
          || callType.indexOf("C2C")
          || callType.indexOf("POD")
          || callType.indexOf("PDOB"))
      )
    ) {
      isIbCallType = true;
    }
    return isIbCallType;
  }
  const getUniqueKeyForGroupingLeads = (lead) => {
    let productId = rootScopeService.getProductId();

    switch (productId) {
      case 7:
        return lead.Age;
      case 115:
        return lead.Age + '-' + lead.SubProductTypeId;
      case 117:
        return lead.Make + '-' + lead.Model;
      default:
        return lead.LeadID
    }
  }

  const groupLeads = (leads) => {
    // returns object of arrays
    let result = {};

    for (let i = 0; i < leads.length; i++) {
      let key = getUniqueKeyForGroupingLeads(leads[i]);
      if (!result[key]) {
        result[key] = [];
      }
      result[key].push(leads[i]);
    }
    setGroupedLeads(result);
  }
  const toggleExpandFn = (key) => () => {
    setGroupedLeads({
      ...groupedLeads,
      [key]: groupedLeads[key].map((l) => {
        l.show = !l.show;
        return l;
      })
    })
  }
  const visibleLeads = []

  if (typeof CustomerIntentsList === 'object' && CustomerIntentsList !== null && !hideCustomerIntentsCard) {
    const leadsummary = CustomerIntentsList.lead_summary;
    if (leadsummary &&
      Object.keys(leadsummary).filter((title) => {
        return (Array.isArray(leadsummary[title]) && leadsummary[title].length > 0)
      }).length > 0) {
      visibleLeads.push({ isPitchCard: true, data: CustomerIntentsList });
    }
  }

  for (let key in groupedLeads) {
    let grpLength = groupedLeads[key].length;
    let _currGrpFirstLead = {
      ...groupedLeads[key][0],
      showExpandMinimize: (grpLength),
      toggleExpandFn: toggleExpandFn(key)
    };

    groupedLeads[key][0].show
      ? visibleLeads.push(_currGrpFirstLead, ...(groupedLeads[key].slice(1)))
      : visibleLeads.push(_currGrpFirstLead);
  }

  const getUtmCampaign = (parentLead) => {
    let _utmCampaign = '';

    if (parentLead.DisplayUtmCampaign) {
      _utmCampaign = parentLead.DisplayUtmCampaign
    }
    setUtmCampaign(_utmCampaign);
  }

  // $scope.PODAPIcount = 0;

  const IsPODTransferTrue = function () {
    /* Get Talktime details and POD eligiblity */
    // var IsbookedLead=false;
    // var IsRejectedLead=false;
    let PODTodayTalkTime = 0;
    let PODHistoryTalktime = 0;

    GetCallDetails(parentLeadId).then(function (resultData) {
      if (resultData != null && !resultData.isError) {
        PODTodayTalkTime = parseInt(resultData.TodayTalkTime) || 0;
        PODHistoryTalktime = parseInt(resultData.HistoryTalktime) || 0;
      }
      dispatch(updateStateInRedux({ key: "PODTodayTalkTime", value: PODTodayTalkTime }));
      dispatch(updateStateInRedux({ key: "PODHistoryTalktime", value: PODHistoryTalktime }));

    });

  }
  const SetRealTimeLeadStatusId = function (RealTimeLeadStatus) {
    const UpdatedallLeads = JSON.parse(JSON.stringify(leads));
    UpdatedallLeads.forEach(lead => {
      if (lead.LeadID === lead.ParentID) {
        lead.StatusId = RealTimeLeadStatus;
        lead.StatusName = GetStatus(RealTimeLeadStatus);
      }
    });
    setLeads(UpdatedallLeads);
    groupLeads(UpdatedallLeads);
    props.SetLeadsToRedux(UpdatedallLeads);
  }
  const GetFOSChurnLogicMsg = () => {
    const PLead=leads.filter(lead=>lead.LeadID==parentLeadId);

    console.log("at line 481",PLead);
    if (isFOSChurnAgent(User) || IsCustomerIntentFOS(leads) ||PLead?.[0]?.PitchMessageDisplay) {
      GetFOSChurnLogicMsgService(parentLeadId)
        .then((response) => {
          dispatch(updateStateInRedux({ key: 'FOSLeadChurnReason', value: response }));
          if (response && (response.message == SV_CONFIG.FOSCustIntentServiceableMessage))
            dispatch(updateStateInRedux({ key: 'ISFOSIntentFlag', value: true }));
        })
        .catch(() => {
          dispatch(updateStateInRedux({ key: 'FOSLeadChurnReason', value: null }));
          dispatch(updateStateInRedux({ key: 'ISFOSIntentFlag', value: false }));
        })

    }
    else {
      dispatch(updateStateInRedux({ key: 'FOSLeadChurnReason', value: null }));
      dispatch(updateStateInRedux({ key: 'ISFOSIntentFlag', value: false }));
      return;
    }
  }
  const CheckIsRebootersGrp = () => {
    let usergrp = User.UserGroupList || [];
    if (usergrp.length > 0) {
      let IsRebooter = Array.isArray(usergrp) ? usergrp.filter((item) => item.GroupId == 2561) : [];
      if (IsRebooter.length > 0) { return true; }
      else { return false; }
    }
    return false;
  }

  const GetCustomerPitchedIntent = () => {

    if (isShowCustomerPitch()) {
      dispatch(updateStateInRedux({ key: "hideCustomerIntentsCard", value: false }));
      let leadforSummary = parentLeadId;
      allleads.forEach(lead => {
        if (rootScopeService.getProductId() == 7 &&
          lead.LeadID == lead.ParentID && ["Special_Cust_Retarget", "Special_NRI_Cust_Retarget", "Term_Wife_upsell"].includes(lead.Utm_source) && lead.ReferralID > 0
        ) {
          leadforSummary = lead.ReferralID;
        }
      })

      GetCustomerPitchedIntentService(leadforSummary).then(response => {
        if (response && typeof response === 'object' && response !== null) {
          dispatch(updateStateInRedux({ key: 'CustomerIntentsList', value: response }));
          if (response.lead_summary !== null) {
            gaEventTracker('AIIntentCard_init', User.EmployeeId, rootScopeService.getLeadId());
          }
        }
        else {
          dispatch(updateStateInRedux({ key: 'CustomerIntentsList', value: null }));
        }
      }).catch(err => {
        dispatch(updateStateInRedux({ key: 'CustomerIntentsList', value: null }));
      })
    } else {
      dispatch(updateStateInRedux({ key: 'CustomerIntentsList', value: null }));
    }
  }

  const GetIsValidInitialNoOfLives = () => {
    if (rootScopeService.getProductId() === 131 && User.RoleId === 13) {
      setIsValidInitialNoOfLives(false);
      setTimeout(function () {
        if (Array.isArray(leads) && leads.length > 0) {
          for (let i = 0; i < leads.length; i++) {
            let data = leads[i];
            if (data && data.InitialNoOfLives && data.SubProductTypeId && data.SubProductTypeId === 13 && data.InitialNoOfLives === 1) {
              setIsValidInitialNoOfLives(true);
              dispatch(updateStateInRedux({ key: "IsValidInitialNoOfLives", value: true }));
              break;
            }
          }
        }
      }, 5000);
    }
  }

  const GetSmeMarineAnualOpenLead = () => {
    try {
      if (rootScopeService.getProductId() === 131 && visibleLeads[0].SubProductTypeId === 13) {
        GetSmeAnualOpenLead(rootScopeService.getCustomerId()).then((result) => {
          if (result && result.length > 0) {
            dispatch(updateStateInRedux({ key: "MarineAnualOpenLeadData", value: result }));
          }
        })
      }
    }
    catch { }
  }

  const IsFosCityHomeVisitPoster = (lead) => {
    try {
      const utm_mediums = [
        'growth_app_health_fos_intent_city',
        'growth_app_health_fos_ns',
        'growth_app_health_fos_ser',
        'growth_app_health_fos_intent',
        'growth_app_health_fos_intent_c',
        'growth_app_health_fos_intent_parent'
      ];
      const UTM_Medium = (lead.UTM_Medium).toLowerCase();
      const isutm = utm_mediums.some(substring => UTM_Medium.includes(substring));
      if (lead && lead.ProductID === 2 && lead.IsFos && (lead.LeadSource).toLowerCase() === 'pbmobileapp' && isutm) {
        return true;
      }
      if (lead && lead.ProductID === 2 && lead.IsFos && (lead.LeadSource).toLowerCase() === 'pb' && lead.UTM_Medium.toLowerCase() === 'home_visit') {
        return true;
      }
      if (lead && lead.ProductID === 2 && lead.IsFos && lead.HealthCommunicationFOS) {
        return true;
      }

    }
    catch {
      return false;
    }

    return false;

  }
  const GetFosAppointmentSummary = () => {
    if (IsCustomerAccess()) {
      dispatch(updateStateInRedux({ key: 'CustomerAppointmentSummary', value: null }));
      dispatch(updateStateInRedux({ key: 'GetCustAppointSummaryAPIStatus', value: API_STATUS.LOADING }));

      GetCustomerAppointmentData(parentLeadId, 3).then(response => {
        if (response && Array.isArray(response) && response.length > 0) {
          dispatch(updateStateInRedux({ key: 'CustomerAppointmentSummary', value: response }));
          dispatch(updateStateInRedux({ key: 'GetCustAppointSummaryAPIStatus', value: API_STATUS.SUCCESS }));
        }
        else {
          dispatch(updateStateInRedux({ key: 'CustomerAppointmentSummary', value: null }));
          dispatch(updateStateInRedux({ key: 'GetCustAppointSummaryAPIStatus', value: API_STATUS.SUCCESS }));
        }
      }).catch(err => {
        dispatch(updateStateInRedux({ key: 'CustomerAppointmentSummary', value: null }));
        dispatch(updateStateInRedux({ key: 'GetCustAppointSummaryAPIStatus', value: API_STATUS.FAIL }));
      })
    }
  }
  const SetShowCustomerValue = () => {
    try {
      if (Array.isArray(leads) && leads.length > 0 && rootScopeService.getProductId() == 131 && leads[0].SubProductTypeId && leads[0].SubProductTypeId == 13) {
        for (let i = 0; i < leads.length; i++) {
          if ([13].indexOf(leads[i].SubProductTypeId) !== -1) {
            GetIsPotentialBuyer().then((res) => {
              if (res) {
                dispatch(updateStateInRedux({ key: "ShowSmeCustomerType", value: true }));
              }
            })
            break;
          }
        }
      }
    }
    catch {

    }

  }
  const GetIsPotentialBuyer = () => {
    const input = {
      url: "api/SalesView/GetIsPotentialBuyer?CustomerId=" + rootScopeService.getCustomerId(),
      method: 'GET',
      service: 'MatrixCoreAPI'
    };
    return CALL_API(input);
  }

  useEffect(() => {
    try {
      if (NavigateLeadId && visibleLeads && visibleLeads.length > 0) {
        for (let i = 0; i < visibleLeads.length; i++) {
          if (visibleLeads[i].LeadID == NavigateLeadId) {
            sliderRef.current.slickGoTo(i);
            break;
          }
        }
      }
      dispatch(updateStateInRedux({ key: "NavigateLeadId", value: null }));
    }
    catch {
      //Do nothing
    }
  }, [NavigateLeadId])

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    dispatch(updateStateInRedux({ key: 'FOSLeadChurnReason', value: null }));
    dispatch(updateStateInRedux({ key: 'TotalCallTT', value: 0 }));
    dispatch(updateStateInRedux({ key: 'FOSHealthRenewalTTEligibility', value: false }));
    //IsCustomerAccess() && dispatch(updateStateInRedux({ key: 'GetCustAppointSummaryAPIStatus', value: API_STATUS.LOADING }));
    dispatch(updateStateInRedux({ key: "IsValidInitialNoOfLives", value: false }));
    dispatch(updateStateInRedux({ key: "MarineAnualOpenLeadData", value: null }));

    if (parentLeadId) {
      // RestrictCall();
      IsPODTransferTrue();
      showFosCityPopup();
      GetFOSChurnLogicMsg();
      GetPrefCommData();
      GetCustomerPitchedIntent()
      showReferralLeadPopup();
      GetFosAppointmentSummary();
      GetIsValidInitialNoOfLives();
      GetSmeMarineAnualOpenLead();
      GetSMEOccupationList();
      dispatch(updateStateInRedux({ key: "ShowSmeCustomerType", value: false }))
      SetShowCustomerValue();
    }
  }, [parentLeadId]);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (RefreshCustomerPitchedIntent) {
      GetCustomerPitchedIntent();
    }
    dispatch(updateStateInRedux({ key: 'RefreshCustomerPitchedIntent', value: false }));
  }, [RefreshCustomerPitchedIntent])
  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (parentLeadId && LstAgentLeads && !SV_CONFIG.HideDNCMessage) {
      GetDNCDetailsV2();
    }
  }, [parentLeadId, JSON.stringify(LstAgentLeads)]);

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    dispatch(setNoOfLeadCardsToShow(visibleLeads.length))
  }, [visibleLeads.length])
  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    dispatch(updateStateInRedux({ key: 'NonPaymentReasonPopUp', value: false }));
    dispatch(updateStateInRedux({ key: 'showCallIntentPopup', value: false }));
    dispatch(updateStateInRedux({ key: 'bookingcancelreason', value: null }));
    getLeads();
    if (IsPaymentOverdueVisible() === true) {
      getPaymentOverduecasesCount();
    }
    localStorage.setItem("isShowDoneBtn", false);
    setPitchShowPopupCount(0);
  }, []);

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (RefreshFosApptSummary) {
      //IsCustomerAccess() && dispatch(updateStateInRedux({ key: 'GetCustAppointSummaryAPIStatus', value: API_STATUS.LOADING }));
      GetFosAppointmentSummary();
    }
    dispatch(updateStateInRedux({ key: 'RefreshFosApptSummary', value: false }));
  }, [RefreshFosApptSummary])


  // eslint-disable-next-line react-hooks/rules-of-hooks
  useInterval(function () {

    let ConnectCallSF = window.localStorage.getItem('ConnectCallSF');
    let ShowRiderAttachmentPopup = !window.localStorage.getItem('ShowRiderAttachmentPopup') ? "true" : window.localStorage.getItem('ShowRiderAttachmentPopup');
    let _podtransfer = false;
    let durationTime = 0;
    var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;
    if (ConnectCallSF) {


      ConnectCallSF = JSON.parse(ConnectCallSF);
      var CallinitateTime = ConnectCallSF.CallInitTime;

      if (CallinitateTime) {
        durationTime = (new Date() - new Date(CallinitateTime)) / 1000;
      }

      if (onCall && showFOSAlert && window.localStorage.getItem('callDisposition') != ""
        && parentLeadId == ConnectCallSF.LeadID
        && ((durationTime > 30 && durationTime < 33) || (durationTime > 90 && durationTime < 93))
        && localStorage.getItem('setcallTimer') != "") {

        setFOSPopupEligiblecity(true);

        // Auto-close the popup after 5 seconds
        setTimeout(() => {
          setFOSPopupEligiblecity(false);
        }, 5000);
      }

      //Poster - FOS//
      if (onCall && IsFosCity && (durationTime > 60 && durationTime < 63)) {
        setshowIsFosCity(true);
        // Auto-close the popup after 5 seconds
        setTimeout(() => {
          setshowIsFosCity(false);
        }, 5000);
      }

      if (onCall && isInvCovid && (durationTime > 5 && durationTime < 8)) {
        setshowInvCovid(true);
      }

      if (onCall && isInvCovid && (durationTime > 15 && durationTime < 18)) {
        setshowInvCovid(false);
      }

      if (onCall && SavingPension && (durationTime > 40 && durationTime < 43)) {
        setshowSavingPension(true);
      }

      if (onCall && [2].indexOf(rootScopeService.getProductId()) > -1 && IsRenewalParent === 1 && (durationTime > 120 && durationTime < 123)) {
        gaEventTracker("PitchRecommendationPopup_Auto","Health_Renewal", User.EmployeeId + "_" + parentLeadId)
        setshowPitchRecommendation(true);
      }


      //Poster - QA Banner//
      if (onCall && [2].indexOf(rootScopeService.getProductId()) > -1 && IsRenewalLead == 0 && (durationTime > 120 && durationTime < 123)) {
        if (!showQABanner) { // Ensure popup is not already visible
          setshowQABanner(true);
          // Auto-close the popup after 5 seconds
          setTimeout(() => {
            setshowQABanner(false);
          }, 5000);
        }
      }
      if (onCall && [2].indexOf(rootScopeService.getProductId()) > -1 && IsRenewalLead == 0 && (durationTime > 150 && durationTime < 153)) {
        setshowQABanner(false);
      }
      //---------------Pod changes----------------------------//
      if (onCall && User.IsPODUser && IsPODDoneShow && localStorage.getItem('setcallTimer') != "" && parentLeadId == ConnectCallSF.LeadID && !IsbookedLeadSet && !IsRejectedLeadSet) {
        //if ($scope.User.IsPODUser && rootScopeService.getParentLeadId() == ConnectCallSF.LeadID && $rootScope.IsPODDoneShow && localStorage.getItem('setcallTimer') != "") {

        if (IsPODDurationCount && durationTime > 40) {

          _podtransfer = true;
          if (!IsShowPODTransfer)
            dispatch(updateStateInRedux({ key: 'IsShowPODTransfer', value: _podtransfer }));
        }
        else if (PODHistoryTalktime > 180 && ((PODTodayTalkTime + durationTime) > 60) && durationTime > 40) {
          _podtransfer = true;
          if (!IsShowPODTransfer)
            dispatch(updateStateInRedux({ key: 'IsShowPODTransfer', value: _podtransfer }));
        }
        else if ((PODTodayTalkTime + durationTime) > 180 && durationTime > 40) {
          _podtransfer = true;
          if (!IsShowPODTransfer)
            dispatch(updateStateInRedux({ key: 'IsShowPODTransfer', value: _podtransfer }));
        }
        else if (durationTime > 180) {
          _podtransfer = true;
          if (!IsShowPODTransfer)
            dispatch(updateStateInRedux({ key: 'IsShowPODTransfer', value: _podtransfer }));
        }
        else {
          _podtransfer = false;
          if (IsShowPODTransfer)
            dispatch(updateStateInRedux({ key: 'IsShowPODTransfer', value: _podtransfer }));
        }
      }
      else {
        // localStorage.setItem('PODClickedLead', null);
        _podtransfer = false;
        if (IsShowPODTransfer)
          dispatch(updateStateInRedux({ key: 'IsShowPODTransfer', value: _podtransfer }));
      }

      //---------------------------VC PopUp-----------------------//
      if (onCall && User.IsEnableVC && window.localStorage.getItem('callDisposition') != ""
        && parentLeadId == ConnectCallSF.LeadID
        && localStorage.getItem('setcallTimer') != ""
        && durationTime > 120
        && (durationTime > 300 || (PODHistoryTalktime + PODTodayTalkTime + durationTime > 300))
      ) {
        dispatch(updateStateInRedux({ key: 'IsVcEligible', value: true }));

      }
      else {
        dispatch(updateStateInRedux({ key: 'IsVcEligible', value: false }));
      }
      // Show message to add comments when answered call is of more than 2 minutes
      if (durationTime >= 140 && [2].indexOf(rootScopeService.getProductId()) > -1) {
        dispatch(updateStateInRedux({ key: 'CommentsPopUp', value: { LeadID: ConnectCallSF.LeadID, show: true } }));
      }
      else {
        dispatch(updateStateInRedux({ key: 'CommentsPopUp', value: { LeadID: ConnectCallSF.LeadID, show: false } }));
      }
      // Show message to add substatus when answered call is of more than 1.5 minutes 
      if (durationTime >= 60 && [2].indexOf(rootScopeService.getProductId()) > -1 && leadexpiryRenewal) {
        dispatch(updateStateInRedux({ key: 'SubStatusPopUp', value: true }));
      }
      else {
        dispatch(updateStateInRedux({ key: 'SubStatusPopUp', value: false }));
      }
      //-----------Show Pop Up to agents for Pitching plans to customers -----------------//
      let durationNFO = ""
      if (Array.isArray(DurationPlanPitchShow) && DurationPlanPitchShow.length > 0) {
        let NFOMessageDuration = DurationPlanPitchShow.find(function (el) { return el.ProductId == rootScopeService.getProductId(); });
        durationNFO = (NFOMessageDuration && NFOMessageDuration.Duration) ? NFOMessageDuration.Duration : "";
      }
      if (CheckIsRebootersGrp() == false && durationTime >= parseInt(durationNFO) && [7, 115].indexOf(rootScopeService.getProductId()) > -1 && PitchShowPopupCount < 1) {
        //let ValidActiveLeadSet = Array.isArray(leads) ? leads.filter((lead) => lead.StatusMode == 'P') : [];
        if (Array.isArray(leads) && leads.length > 0) {
          let item = leads.filter(item => item.IsPlanPitchedtoCustomer == true);
          if (item.length == 0) {
            dispatch(updateStateInRedux({ key: 'PlanPitchPopup', value: true }));
            SavePitchAuditHistory('Pitch popup has been shown to Agent');
            setPitchShowPopupCount(1);
          }
          else {
            dispatch(updateStateInRedux({ key: 'PlanPitchPopup', value: false }));
          }
        }
        else {
          dispatch(updateStateInRedux({ key: 'PlanPitchPopup', value: false }));
        }
      }
      else {
        dispatch(updateStateInRedux({ key: 'PlanPitchPopup', value: false }));
      }

      //---------------------------Update Lead Status based on call duration-----------------------//
      if (User.IsProgressive == true && [7, 1000, 2, 115, 117, 131, 139, 106, 118, 130].indexOf(rootScopeService.getProductId()) > -1) {
        let leadsource = "";

        let statusId = 0;
        leads.forEach((vdata, key) => {
          if (parentLeadId == vdata.LeadID) {
            leadsource = vdata.LeadSource;
            statusId = vdata.StatusId;
          }
        });

        let NRILeads = leads.find(lead => lead.Country && ['392', '91', '999', 'INDIA', '0', 'NULL'].indexOf(lead.Country) == -1);
        let IsCustNRI = false;
        if (NRILeads) {
          IsCustNRI = true;
        }

        if (onCall && parentLeadId == ConnectCallSF.LeadID
          && ((durationTime > 60 && durationTime < 63)
            || (durationTime > 80 && durationTime < 83)
            || (durationTime > 140 && durationTime < 143)
            || (durationTime > 160 && durationTime < 163)
          )
          && statusId < 4 && [2, 106, 118, 130, 7, 115, 117, 131, 139].indexOf(rootScopeService.getProductId()) > -1
        ) {
          SetRealTimeLeadStatus(ConnectCallSF.LeadID, leadsource, rootScopeService.getProductId(), IsCustNRI, durationTime)
        }

        if (onCall && parentLeadId == ConnectCallSF.LeadID
          && ((durationTime > 180 && durationTime < 183) && rootScopeService.getProductId() === 131)) {
          showPotentialRepaetBuyerPopup();
        }
        
         //---------Motor Checklist Popup-------------------------------
      if (rootScopeService.getProductId() == 117 && ConnectCallSF && onCall && parentLeadId == parseInt(ConnectCallSF.LeadID) 
        && statusId < 13 && ![5, 6, 7, 12, 14].includes(statusId) && durationTime > 90 && durationTime < 93 && SV_CONFIG['MotorChecklistPopup'] === true) 
      {
        dispatch(updateStateInRedux({ key: 'MotorChecklistPopupTrigger', value: true }));
      }

      }

     

      //-------------- Rider Attachment Popup-------------------------------
      if (rootScopeService.getProductId() == 2 && IsRenewalLead == 0 && durationTime > 900 && onCall && ShowRiderAttachmentPopup == "true") {
        window.localStorage.setItem("ShowRiderAttachmentPopup", "false")
        let message = "You can pitch rider to this customer to increase your ATS!"
        enqueueSnackbar(message, {
          variant: 'success',
          persist: false,
          className: "commentsPopup",
          preventDuplicate: true,
          action,
          autoHideDuration: 15000,
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          }
        })

      }


      // ------------- appointment verification------------------------------
      let currentCalTalktime = 0;
      if (durationTime > 30) {
        currentCalTalktime = durationTime - 30
      }

      if (onCall && parentLeadId == ConnectCallSF.LeadID) {
        dispatch(updateStateInRedux({ key: 'TotalCallTT', value: (currentCalTalktime + PODHistoryTalktime + PODTodayTalkTime) }))
      } else {
        dispatch(updateStateInRedux({ key: 'TotalCallTT', value: (PODHistoryTalktime + PODTodayTalkTime) }))
      }
    }
    else {

      _podtransfer = false;
      dispatch(updateStateInRedux({ key: 'TotalCallTT', value: (PODHistoryTalktime + PODTodayTalkTime) }))
      dispatch(updateStateInRedux({ key: 'IsVcEligible', value: false }));
      if (IsShowPODTransfer)
        dispatch(updateStateInRedux({ key: 'IsShowPODTransfer', value: _podtransfer }));
    }

    //-----------------------------FOS Health Renewal-----------//
    if (ConnectCallSF && onCall && parentLeadId == parseInt(ConnectCallSF.LeadID) && (PODTodayTalkTime >= 60 || (PODTodayTalkTime + durationTime) >= 80)) {
      dispatch(updateStateInRedux({ key: 'FOSHealthRenewalTTEligibility', value: true }));
    }
    else {
      if (PODTodayTalkTime >= 60) {
        dispatch(updateStateInRedux({ key: 'FOSHealthRenewalTTEligibility', value: true }));
      }
    }

    // ********************** Analytics Dashboard Popup in every 0 and 30 minutes *******************************
    if (SV_CONFIG['ShowAnalyticsDashboardPopup']) {
      const date = new Date();
      const hours = date.getHours();
      const minutes = date.getMinutes();
      const seconds = date.getSeconds();
      let firstLoginTimestamp = localStorageCache.readFromCache('FirstLogin');
      if (firstLoginTimestamp != null) {
        const sixtyMinutes = 60 * 60 * 1000;  //now changed to 60 mins -- earlier it was 30 mins
        const currentTime = Date.now();
        const elapsedTime = currentTime - parseInt(firstLoginTimestamp, 10);

        // Check if 60 minutes have elapsed since the first login
        if (elapsedTime >= sixtyMinutes) {
          dispatch(updateStateInRedux({ key: 'ShowAnalyticsDashboardPopup', value: true }));
          localStorageCache.writeToCache('FirstLogin', Date.now(), 1 * 60 * 60 * 1000);
        }
      } else {
        localStorageCache.writeToCache('FirstLogin', Date.now(), 1 * 60 * 60 * 1000);
      }

    }

  }, 2000);


  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (RefreshLead) {
      dispatch(updateStateInRedux({ key: 'bookingcancelreason', value: null }));
      dispatch(updateStateInRedux({ key: 'FOSHealthRenewalTTEligibility', value: false }));
      props.setRefreshLeadToRedux(false);
      dispatch(updateStateInRedux({ key: 'ObjRestrictCall_errorMsg', value: '' }));
      dispatch(updateStateInRedux({ key: "AppointmentData", value: null }));
      getLeads();
      setPitchShowPopupCount(0);
    }
  }, [RefreshLead]);

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (RefreshCustomerId) {
      let CustomerId = rootScopeService.getCustomerId();
      props.setRefreshCustomerIdToRedux(CustomerId);
    }
  }, [RefreshCustomerId]);

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (oRestrictCall._errorMsg) {
      dispatch(updateStateInRedux({ key: 'ObjRestrictCall_errorMsg', value: oRestrictCall._errorMsg }));
    }
  }, [oRestrictCall._errorMsg]);

  const showReferralLeadPopup = function () {
    let flag = false;
    if ([2].indexOf(rootScopeService.getProductId()) != -1 && User.RoleId == 13) {

      leads.forEach(function (vl, key) {
        if (vl.LeadSourceId !== 6 && vl.ProductID == 2 && vl.StatusId >= 13 && vl.StatusId != 14) {
          var bookingTime = new Date((dayjs(vl.OfferCreatedON).format("YYYY-MM-DDThh:mm:ss")));
          var currentTime = new Date((dayjs(Date.now()).format("YYYY-MM-DDThh:mm:ss")));
          var diff = Math.floor((currentTime - bookingTime) / 1000 / 60);
          if (diff <= 15 && diff >= 0 && !OpenReferralLeadPopUp) {
            flag = true;
          }

        }
      });

      if (flag == true) {
        setOpenReferralLeadPopUp(true);

        // Set it back to false after 5 seconds
        setTimeout(() => {
          setOpenReferralLeadPopUp(false);
        }, 5000); // 5000 milliseconds = 5 seconds
      }

    }

  }

  const showFosCityPopup = function () {
    setshowFOSAlert(false);
    setFOSCity('');

    let IsRenewal = leads.filter(function (vl) {
      setleadexpiryRenewal(vl.LeadSourceId === 6 && vl.ProductID == 2);
      return (vl.LeadSourceId === 6 && vl.ProductID == 2)

    })

    if (IsRenewal.length === 0 && [2, 7, 115, 117, 139, 1000].indexOf(rootScopeService.getProductId()) != -1 && User.RoleId == 13) {
      let _IsFOSNotAllowed = false;
      let _showFOSAlert = false;
      let _IsRenewal = false;

      GetOfflineCitiesService(parentLeadId, 0, rootScopeService.getProductId()).then(function (response) {
        if (response) {
          response = response.filter((_city) => _city.AppointmentTypeId === 4);
          const { DistinctCities, CityMapping } = getDistinctCitiesAppointmentMapping(response);

          leads.forEach(function (vdata, key) {
            if (vdata.LeadSourceId == 6) {
              _IsRenewal = true;
            }
            if (vdata.StatusId > 3 && vdata.CityID && Array.isArray(DistinctCities) && DistinctCities.length > 0 && DistinctCities.some(obj => obj.CityId == vdata.CityID)) {
              _showFOSAlert = true;
              setFOSCity(vdata.City);

            }
            if (vdata.LeadID === vdata.ParentID) {
              if (rootScopeService.getProductId() == 117 && vdata.LeadSourceId != 3) {
                if ((vdata.PolicyType.toLocaleLowerCase() != 'new' && checkPolicyExpiry(vdata.PolicyExpiryDate)) || (vdata.PolicyType.toLocaleLowerCase() == 'new' && !CheckExpectedDeliveryDate(vdata.ExpectedDeliveryDate))) {
                  _IsFOSNotAllowed = true;
                }
              }
              else if (rootScopeService.getProductId() == 139 && vdata.LeadSourceId != 3) {
                if (checkPolicyExpiry(vdata.PolicyExpiryDate)) {
                  _IsFOSNotAllowed = true;
                }
              }
            }
          });
          if (_showFOSAlert && !_IsFOSNotAllowed && !([117, 139].indexOf(rootScopeService.getProductId()) != -1 && _IsRenewal)) {
            setshowFOSAlert(true);
          }
          else {
            setshowFOSAlert(false);
          }
        }
      })
    }
  }

  const action = key => (
    <>
      <CloseIcon onClick={() => { closeSnackbar(key) }} />
    </>
  );

  const showPotentialRepaetBuyerPopup = () => {
    try {
      if (Array.isArray(leads) && leads.length > 0 && leads[0].SubProductTypeId && leads[0].SubProductTypeId == 13 && !ShowSmeCustomerType) {
        dispatch(updateStateInRedux({ key: "PotentialRepeatBuyer", value: true }));
      }
    }
    catch {

    }
  }
  const SetRealTimeLeadStatus = (ParentId, LeadSource, productId, IsCustNRI, durationTime) => {
    SetRealTimeLeadStatusService(ParentId, LeadSource, productId).then((res) => {
      if (res && res > 0) {
        if (productId == 115 && durationTime > 140 && durationTime < 143) {
          InvAdvisorVerifyService(ParentId).then((res) => {

          });
        }
        if (productId == 117 && LeadSource && !["Renewal"].includes(LeadSource) && durationTime > 60 && durationTime < 63 && PODHistoryTalktime < 30 && PODTodayTalkTime < 30) {
          MotorAdvisorVerifyService(ParentId).then((res) => {

          });
        }
        let RealTimeLeadStatus = res;
        SetRealTimeLeadStatusId(RealTimeLeadStatus);
      }
    })
  }
  const GetDNCDetailsV2 = () => {
    if (DNCSnackbar) { closeSnackbar(DNCSnackbar) }

    let _LstAgentLeads = getSearchObjFromLS();

    let currentLead = _LstAgentLeads.find(lead => (lead && (lead.LeadID === parentLeadId)));

    if (currentLead && currentLead.DNC && currentLead.DNC.CoolingPeriod) {
      let message = `This customer is on cooling period for ${currentLead.DNC.CoolingPeriod} days.`;

      if (currentLead.DNC.CoolingPeriod === 30) {
        message = 'Customer has Blocked himself/herself from My Account';
      }

      let _id = enqueueSnackbar(message, {
        variant: 'success',
        persist: true,
        className: "multipleCallPopup",
        preventDuplicate: true,
        action,
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'center',
        }
      })

      setDNCSnackbar(_id);
    }
  }

   const GetPrefCommData=()=>
   { if (parentLeadId > 0) {
      const requestData = {
        CustomerId: rootScopeService.getCustomerId(),
        CommTypeId: 5,
        LeadId: parentLeadId,
        UserId: User.UserId,
        Source: "LeadCardSV",
      }
      getPrefCommService(requestData).then((response) => {
        if (response?.Status) {
          setIsCustomerNotOptedInWhatsapp(!response.Data)
        }
      });
    }

   }

  const SavePitchAuditHistory = (comment) => {
    let reqData = {
      CustomerId: rootScopeService.getCustomerId(),
      ProductId: rootScopeService.getProductId(),
      ParentLeadId: parentLeadId,
      PrimaryLeadId: parentLeadId,
      UserId: User.UserId,
      Comment: comment,
      EventType: 59
    };
    SetCustomerComment(reqData);
  }

  const leadlist = visibleLeads.map((leadDetails, index) => {
    if (leadDetails && leadDetails.isPitchCard) {
      return <CustomerRequirementsCard />
    }
    else {
      return <LeadCard
        key={index}
        lead={leadDetails}
        getLeads={getLeads}
        Utm_Campaign={UtmCampaign}
        leadViewOnly={props.leadViewOnly}
        LeadOnlyViewNew={props.LeadOnlyViewNew}
        IsCustomerNotOptedInWhatsapp={IsCustomerNotOptedInWhatsapp}
      />
    }
  });

  let maxLeadCardsToShow = () => {
    switch (true) {
      case isLargeDesktop:
        return 3;
      case isDesktop:
        return 2;
      case isTablet:
        return 2;
      case isMobile:
        return 1;
      default:
        return 1;
    }
  }
  let sliderSettings = {
    // dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: props.leadCardToShow ? props.leadCardToShow : (Math.min(noOfLeadCardsToShow, maxLeadCardsToShow()) || 1),
    slidesToScroll: props.leadCardToShow ? props.leadCardToShow : maxLeadCardsToShow()
  };

  return (
    <>
      <Grid item xs={12}>
        {/* <button onClick={props.SetRealTimeLeadStatusToRedux(4)}>Test</button> */}
        {/* <button onClick={() => SetRealTimeLeadStatusId(4)}>CTest</button> */}
        <Slider ref={sliderRef} {...sliderSettings} key={rootScopeService.getLeadId() / (noOfLeadCardsToShow + 1)}>
          {leadlist}
        </Slider>
      </Grid>
      {FOSPopupEligiblecity && <FOSEligibleCityPopup open={FOSPopupEligiblecity} handleClose={closePopup} city={FOSCity} />}
      {OpenReferralLeadPopUp &&
        <ReferralLeadPopup
          open={OpenReferralLeadPopUp}
          handleClose={() => { setOpenReferralLeadPopUp(false); }}
        />
      }
      {IsValidInitialNoOfLives &&
        <ErrorBoundary name="ShowInitialNoOfLivesPanel">
          <PotentLeadsPanel
            open={true}
            handleClose={() => { setIsValidInitialNoOfLives(false) }}
          />
        </ErrorBoundary>
      }
      {showCallIntentPopup &&
        <CallIntentByAgentPopup open={showCallIntentPopup}></CallIntentByAgentPopup>
      }
      <ModalPopup
        open={showIsFosCity}
        handleClose={() => setshowIsFosCity(false)} className="HomeVisitPopup" >
        {
          <img src={CONFIG.PUBLIC_URL + "/images/FOSCityPoster.jpg"} />
        }
      </ModalPopup>

      <ModalPopup
        open={showQABanner}
        handleClose={() => setshowQABanner(false)} className="HomeVisitPopup" >
        {
          <img src={CONFIG.PUBLIC_URL + "/images/FreshQABanner.jpg"} />
        }
      </ModalPopup>
      {showInvCovid &&
        <CovidInvPopup
          open={showInvCovid}
          handleClose={() => {
            setshowInvCovid(false);
          }}
          Utm_campaign={savingUtm_campaign}
        />}
      {showPitchRecommendation &&
        <PitchRecommendationPopup
          open={showPitchRecommendation}
          handleClose={() => setshowPitchRecommendation(false)}
          LeadID={parentLeadId}
        />
      }

      <ModalPopup
        open={showSavingPension}
        handleClose={() => setshowSavingPension(false)} className="HomeVisitPopup" >
        {
          <img src={CONFIG.PUBLIC_URL + "/images/salesview/Banners/savingpension.png"} />
        }
      </ModalPopup>
      {PotentialRepeatBuyer &&
        <ErrorBoundary name="PotentialRepeatBuyer">
          <PotentialRepeatBuyerPopUp open={PotentialRepeatBuyer}
          />
        </ErrorBoundary>
      }
    </>
  );

}
const mapStateToProps = () => {
  return {

  };
};

const mapDispatchToProps = dispatch => {
  return {
    SetLeadsToRedux: (_allLeads) => dispatch(set_allleads({ allLeads: _allLeads })),
    setManualStampingToRedux: (value) => dispatch(set_ManualStamping({ SetManualStamping: value })),
    setRenewalToRedux: (value) => dispatch(set_Renewal({ IsRenewal: value })),
    setRefreshLeadToRedux: (value) => dispatch(setRefreshLead({ RefreshLead: value })),
    setRefreshCustomerIdToRedux: (value) => dispatch(setRefreshCustomerId({ RefreshCustomerId: value })),
    setPaymentOverDueCount: (value) => dispatch(set_PaymentOverDueCount({ PaymentOverdueCount: value })),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(LeadList);
