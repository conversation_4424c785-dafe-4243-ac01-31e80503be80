﻿/**
 * @Action : conference | blindtransfer | transfer, 
 * in case of conference a popup should open to select,block and transfer call to agent
 * @ProductId : Array of prodId for which transfer to be visible
 * to show for all product, pass empty array or skip this field
*/


const TransferTypes = {
    "TransferType": [
        {
            Id: 1,
            ProductId: [101, 131],
            // GroupId: [123],
            NotAllowedProductId: [219],
            TransferName: 'TL Transfer',
            Queue: "TLCallTransfer",
            Transfertype: "tltransfer",
            Action: "conference",
            IsRenewable: false,
        },
        {
            Id: 2,
            ProductId: [],
            RoleId: 13,
            GroupId: [137, 521, 523, 1443, 1449, 1264],
            TransferName: 'Claim Transfer',
            Queue: "claim_Transfer",
            Transfertype: "sales_to_claims",
            Action: "blindtransfer",
            IsRenewable: false,
        },
        {
            Id: 32,
            ProductId: [2],
            TransferName: 'Renewal Regional Transfer',
            Transfertype: "renwal_regional_transfer",
            Action: "conference",
            AnyRenewal: true
        },
        {
            Id: 33,
            ProductId: [2],
            TransferName: 'Renewals To Fresh Referrals',
            Queue: "health",
            Transfertype: "renewaltofreshreferrals",
            Action: "conference",
            Allbooked: true
        },
        {
            Id: 3,
            ProductId: [106, 118, 130, 2],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Con Call Transfer',
            Queue: "claimtransfer",
            Transfertype: "serviceclaimconf",
            Action: "conference",
            IsRenewable: true

        },
        // {
        //     Id: 4,
        //     ProductId: [106, 118, 130, 2],
        //     RoleId: 13,
        //     GroupId: [],
        //     TransferName: 'Hot Call Transfer',
        //     Queue: "health",
        //     Transfertype: "transfer_noncore_verification",
        //     Action: "conference",
        //     IsRenewable: false

        // },
        // {
        //     Id: 5,
        //     ProductId: [117],
        //     RoleId: 13,
        //     GroupId: [1557],
        //     TransferName: 'Hot Call Transfer',
        //     Queue: "health",
        //     Transfertype: "transfer_noncore_verification",
        //     Action: "conference",
        //     IsRenewable: false
        // },
        // {
        //     Id: 12,
        //     ProductId: [2, 3, 7, 114, 115, 117, 139],
        //     RoleId: 13,
        //     GroupId: [],
        //     TransferName: 'Commercial Inbound',
        //     Queue: "comvchl",
        //     Transfertype: "blindTransferFlag",
        //     Action: "blindtransfer",
        //     isCreateLead: "yes"
        // },
        {
            Id: 13,
            ProductId: [2],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Health Renewal Inbound',
            Queue: "healthrenewib",
            Transfertype: "blindTransferFlag",
            Action: "blindtransfer",
            isCreateLead: "yes"
        },
        {
            Id: 15,
            ProductId: [2, 7, 115],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Claim Transfer',
            Queue: "claim_Transfer",
            Transfertype: "sales_to_claims",
            Action: "blindtransfer",
            IsRenewable: false,
        },
        {
            Id: 16,
            ProductId: [131],
            Queue: "",
            TransferName: 'Inter Team Transfer',
            Transfertype: "SmeInterTeamTransfer",
            Action: "conference",
            IsRenewable: false,
        },
        {
            Id: 17,
            ProductId: [2, 131],
            RoleId: 13,
            Queue: "Sales_to_RM",
            TransferName: 'RM Transfer',
            Transfertype: "Sales_to_RM",
            Action: "transfer", // to check
            IsRenewable: false,
        },
        {
            Id: 23,
            ProductId: [2, 106],
            TransferName: 'Regional Transfer',
            Transfertype: "regional_transfer",
            Action: "transfer",
            IsRenewable: false,
        },
        {
            Id: 24,
            GroupId: [2572, 52, 959, 2743],
            TransferName: 'Engage Insurer RM',
            Transfertype: "engageinsurerrm",
            Action: "conference",
            IsRenewable: false,
            Queue: "engageinsurerrm",
        },
        { // Term
            Id: 25,
            ProductId: [7],
            RoleId: 13,
            TransferName: 'Regional Transfer',
            Transfertype: "regional_transfer",
            Action: "transfer",
        },
        { // Savings
            Id: 26,
            ProductId: [115],
            //NotAllowedGroupId: [828,1363,1364,1647,1708,1712,1746,1976,2038,2039,2270,2688,2784,2771,959],
            RoleId: 13,
            TransferName: 'Regional Transfer',
            Transfertype: "regional_transfer",
            Action: "transfer",
        },
        {
            Id: 27,
            ProductId: [219],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Sales IVR Transfer',
            Queue: "fostoivr",
            Transfertype: "fostosalesivrtransfer",
            Action: "blindtransfer",
            IsRenewable: false,
        },
        {
            Id: 28,
            ProductId: [219],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Service IVR Transfer',
            Queue: "fostoivr",
            Transfertype: "fostoserviceivrtransfer",
            Action: "blindtransfer",
            IsRenewable: false,
        },
        {
            Id: 29,
            ProductId: [115],
            RoleId: 13,
            GroupId: [828, 1363, 1364, 1647, 1708, 1712, 1746, 1976, 2038, 2039, 2270, 2688, 2784, 2771],
            TransferName: 'NRI Malayalam Transfer',
            Queue: "invnrimalayalam",
            Transfertype: "sales_to_sales",
            Action: "transfer",
        },
        {
            Id: 30,
            ProductId: [131],
            Queue: "",
            TransferName: 'Renewal Lead Transfer',
            Transfertype: "SmeRenewalLeadTransfer",
            Action: "transfer",
            IsRenewalLead: true,
        },
        {
            Id: 34,
            ProductId: [117],
            Queue: "claim_Transfer",
            TransferName: 'Claim Transfer',
            Transfertype: "sales_to_claims",
            Action: "blindtransfer",
            AnyRenewal: true
        },
        {
            Id: 35,
            ProductId: [2],
            GroupId: [2536, 2548, 2642, 2728, 2767, 2958, 3326],
            Queue: "nripreissuance",
            TransferName: 'NRI Service Transfer',
            Transfertype: "sales_to_service",
            Action: "transfer"
        },
        {
            Id: 36,
            ProductId: [106],
            GroupId: [1610, 3156, 3160, 3161],
            Queue: "mmsupervisor",
            TransferName: 'Monthly Mode Supervisor Transfer',
            Transfertype: "sales_to_sales",
            Action: "transfer"
        },
        {
            Id: 37,
            ProductId: [7],
            Queue: "termsenadvisor",
            TransferName: 'Transfer to Sr Advisor',
            Transfertype: "transfertosradvisor",
            Action: "conference"
        },
        {
            Id: 40,
            ProductId: [2, 3, 7, 114, 115, 117, 139, 131],
            Queue: "",
            TransferName: 'Cross Product Transfer',
            Transfertype: "sales_to_sales",
            Action: "conference"
        },
        {
            Id: 41,
            ProductId: [2],
            Queue: "",
            TransferName: "Underwriters Transfer",
            Transfertype: "sales_to_sales",
            Action: "conference"
        },
        {
            Id: 42,
            ProductId: [2, 7, 115, 117],
            Queue: "unregistered",
            TransferName: "CRT Transfer",
            Transfertype: "sales_to_service",
            Action: "conference"
        },
        {
            Id: 43,
            ProductId: [115],
            Queue: "invnrimalayalam",
            TransferName: "NRI Malayalam Transfer",
            Transfertype: "sales_to_sales",
            Action: "Transfer",
        },
        {
            Id: 44,
            ProductId: [117],
            Queue: "tltransfer", // not a real queue
            TransferName: "TL Transfer",
            Transfertype: "tltransfer",
            Action: "Transfer",
            IsBrandNewCar: true
        }
    ],
    "RegionalTransfer": [ // health
        {
            Id: 1,
            TransferName: 'PED',
        },
        {
            Id: 2,
            TransferName: 'Non PED',
        },
        {
            Id: 3,
            TransferName: 'NRI Malayalam Transfer',
            ProductId: [2],
            RoleId: 13,
            GroupId: [2536],
            Queue: "healthnrimalayalam",
            Transfertype: "sales_to_sales",
            Action: "transfer",
            IsRenewalLead: false
        },
        {
            Id: 4,
            TransferName: 'Bengali Transfer',
            ProductId: [2],
            RoleId: 13,
            Queue: "healthbengali",
            Transfertype: "sales_to_sales",
            Action: "transfer",
            IsRenewalLead: false
        }
    ],
    "RegionalTransferPED": [
        {
            Id: 1,
            Queue: "teluguped",
            TransferName: 'Telugu PED',
        },
        {
            Id: 2,
            Queue: "tamilped",
            TransferName: 'Tamil PED',
        },
        {
            Id: 3,
            Queue: "marathiped",
            TransferName: 'Marathi PED',
        },
        {
            Id: 4,
            Queue: "kannadaped",
            TransferName: 'Kannada PED',
        },
        {
            Id: 5,
            Queue: "malayalamped",
            TransferName: 'Malayalam PED',
        },

    ],
    "RegionalTransferNPED": [
        {
            Id: 1,
            Queue: "telugu",
            TransferName: 'Telugu',
        },
        {
            Id: 2,
            Queue: "tamil",
            TransferName: 'Tamil',
        },
        {
            Id: 3,
            Queue: "marathi",
            TransferName: 'Marathi',
        },
        {
            Id: 4,
            Queue: "kannad",
            TransferName: 'Kannada',
        },
        {
            Id: 5,
            Queue: "malayalam",
            TransferName: 'Malayalam',
        },
    ],

    "RegionalTransferLanguage": [
        {
            Id: 1,
            Queue: "telugu",
            TransferName: 'Telugu',
        },
        {
            Id: 2,
            Queue: "tamil",
            TransferName: 'Tamil',
        },
        {
            Id: 3,
            Queue: "marathi",
            TransferName: 'Marathi',
        },
        {
            Id: 4,
            Queue: "kannad",
            TransferName: 'Kannada',
        },
        {
            Id: 5,
            Queue: "malayalam",
            TransferName: 'Malayalam',
        },
        {
            Id: 6,
            Queue: "healthbengali",
            TransferName: "Bengali"
        }
    ],

    "RegionalTransferTerm": [
        {
            Id: 1,
            Queue: "termbengal",
            TransferName: 'Bengali/West Bengal',
        },
        {
            Id: 2,
            Queue: "termmarathi",
            TransferName: 'Marathi/Maharastra',
        },
        {
            Id: 3,
            Queue: "termtelugu",
            TransferName: 'Telugu/Telangana',
        },
        {
            Id: 4,
            Queue: "termkannada",
            TransferName: 'Kannada/Karnataka',
        },
        {
            Id: 5,
            Queue: "termtamil",
            TransferName: 'Tamil/Tamil Nadu',
        },
        {
            Id: 6,
            Queue: "termmalayalam",
            TransferName: 'Malayalam/Kerala',
        },
        {
            Id: 7,
            Queue: "termgujrati",
            TransferName: 'Gujrati/Gujrat',
        },
        {
            Id: 8,
            Queue: "termhindi",
            TransferName: 'Hindi Transfer',
        },
        {
            Id: 9,
            Queue: "termnrigurgaon",
            TransferName: 'NRI Gurgaon Transfer',
        },
        {
            Id: 10,
            Queue: "termnrImalyalam",
            TransferName: 'NRI Malayalam Transfer',
        },
        {
            Id: 11,
            Queue: "termodiya",
            TransferName: 'Odiya Transfer',
        }
    ],

    "RegionalTransferSavings": [
        {
            Id: 1,
            Queue: "invteluguhyd",
            TransferName: 'Hyderabad Regional',
        },
        {
            Id: 2,
            Queue: "invassam",
            TransferName: 'Regional Assam',
        },
        {
            Id: 3,
            Queue: "invbengali",
            TransferName: 'Regional Bengal',
        },
        {
            Id: 4,
            Queue: "invgujrati",
            TransferName: 'Regional Gujarati',
        },
        {
            Id: 5,
            Queue: "invkannada",
            TransferName: 'Regional Kannada',
        },
        {
            Id: 6,
            Queue: "invmalayalam",
            TransferName: 'Regional Malayalam',
        },
        {
            Id: 7,
            Queue: "invmarathi",
            TransferName: 'Regional Marathi',
        },
        {
            Id: 8,
            Queue: "invorissa",
            TransferName: 'Regional Orissa',
        },
        {
            Id: 9,
            Queue: "invtamil",
            TransferName: 'Regional Tamil',
        }
    ],
    "RenewalTransferTypesSme": [
        {
            Id: 1,
            Queue: "ebrenewal",
            TransferName: 'EB Renewal',
        },
        {
            Id: 2,
            Queue: "otherenewal",
            TransferName: 'Inbound Renewal',
        },
        {
            Id: 3,
            Queue: "liabrenewal",
            TransferName: 'Liability Renewal',
        },
        {
            Id: 4,
            Queue: "proprenewal",
            TransferName: 'Property Renewal',
        }
    ],
    "RenewalRegionalType": [
        {
            Id: 1,
            Queue: "hrtelugu",
            TransferName: 'Telugu',
        },
        {
            Id: 2,
            Queue: "hrtamil",
            TransferName: 'Tamil',
        },
        {
            Id: 3,
            Queue: "hrmalayalam",
            TransferName: 'Malayalam',
        },
        {
            Id: 4,
            Queue: "hrmarathi",
            TransferName: 'Marathi',
        },
        {
            Id: 5,
            Queue: "hrkannada",
            TransferName: 'Kannada',
        }
    ],
    "SMECrossBUTransferQueues": [
        {
            Id: 7,
            Queue: "smemoc",
            TransferName: 'Car Inbound',
        },
        {
            Id: 8,
            Queue: "smehoc",
            TransferName: 'Health Inbound',
        }
    ],

    "CrossBUTransfer": [
        {
            Id: 8,
            ProductId: [2, 3, 7, 114, 115, 117, 139, 131],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Health Inbound',
            TransferProduct: 2,
            Queue: "health",
            Transfertype: "blindTransferFlag",
            Action: "blindtransfer",
            isCreateLead: "yes"
        },
        {
            Id: 6,
            ProductId: [117],
            RoleId: 13,
            GroupId: [1443, 137, 682, 1264],
            TransferName: 'New Car Inbound',
            TransferProduct: 117,
            Queue: "newcarpd",
            Transfertype: "transfer_newcar",
            Action: "conference",
            IsRenewable: false
        },
        {
            Id: 7,
            ProductId: [2, 3, 7, 114, 115, 117, 139, 131],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Car Inbound',
            TransferProduct: 117,
            Queue: "car ",
            Transfertype: "blindTransferFlag",
            Action: "blindtransfer",
            isCreateLead: "yes"
        },
        {
            Id: 9,
            ProductId: [2, 3, 7, 114, 115, 117, 139, 131],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Term Life Inbound',
            TransferProduct: 7,
            Queue: "term",
            Transfertype: "blindTransferFlag",
            Action: "blindtransfer",
            isCreateLead: "yes"
        },
        {
            Id: 10,
            ProductId: [2, 3, 7, 114, 115, 117, 139, 131],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Investment Inbound',
            TransferProduct: 115,
            Queue: "nterm",
            Transfertype: "blindTransferFlag",
            Action: "blindtransfer",
            isCreateLead: "yes"
        },
        {
            Id: 11,
            ProductId: [2, 3, 7, 114, 115, 117, 139, 131],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Car Tamil Inbound',
            TransferProduct: 117,
            Queue: "cartamil",
            Transfertype: "blindTransferFlag",
            Action: "blindtransfer",
            isCreateLead: "yes"
        },
        {
            Id: 14,
            ProductId: [2, 3, 7, 114, 115, 117, 139],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Car Renewal Inbound',
            TransferProduct: 117,
            Queue: "motorrenewib",
            Transfertype: "blindTransferFlag",
            Action: "blindtransfer",
            isCreateLead: "yes"
        },
        {
            Id: 18,
            NotAllowedProductId: [114, 219],
            RoleId: 13,
            Queue: "twib",
            TransferName: 'Two-wheeler Inbound',
            TransferProduct: 114,
            Transfertype: "sales_to_sales",
            Action: "conference", // to check

        },
        {
            Id: 19,
            NotAllowedProductId: [3, 219],
            RoleId: 13,
            Queue: "travel",
            TransferName: 'Travel Inbound',
            TransferProduct: 3,
            Transfertype: "sales_to_sales",
            Action: "conference", // to check

        },
        {
            Id: 20,
            NotAllowedProductId: [131, 219],
            RoleId: 13,
            Queue: "smesalesib",
            TransferName: 'SME Inbound',
            TransferProduct: 131,
            Transfertype: "sales_to_sales",
            Action: "conference", // to check
        },
        {
            Id: 21,
            NotAllowedProductId: [101, 219],
            RoleId: 13,
            Queue: "homeib",
            TransferName: 'Home Inbound',
            TransferProduct: 101,
            Transfertype: "sales_to_sales",
            Action: "conference", // to check
        },
    ],

    "ClaimTransfer": [
        {
            Id: 1,
            ProductId: [2, 7, 115, 117],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Health Claim',
            TransferProduct: 2,
            Queue: "",
            Transfertype: "sales_to_claims",
            Action: "conference",
            isCreateLead: "yes"
        },
        {
            Id: 2,
            ProductId: [2, 7, 115, 117],
            RoleId: 13,
            GroupId: [],
            TransferName: 'Motor Claim',
            TransferProduct: 117,
            Queue: "",
            Transfertype: "sales_to_claims",
            Action: "conference",
            IsRenewable: false
        },
    ],

    "UnderWritersTransfer": [
        {
            Id: 1,
            ProductId: [2],
            RoleId: 13,
            // GroupId: [2916, 2786, 1497, 1488, 1256, 1181, 1182, 1471, 895, 1378, 887, 2698, 2900, 3171,2536, 2548, 2642, 2728,
            //     2807,2259,3163,3166,2303,2984,3153,3151,3152,3310,3309,3308,3332,2222,3386,1563,2584,3133,3341,3342,3343,3344,3345,3340,2727,3438],
            TransferName: 'GGN Underwriters Transfer',
            Queue: "healthunderwriters",
            Transfertype: "sales_to_sales",
            Action: "transfer", 
            //IsRenewalLead: false
        },
        {
            Id: 2,
            ProductId: [2],
            Queue: "healthuwtelugu",
            TransferName: 'Telugu Underwriters Transfer',
            Transfertype: "sales_to_sales",
            Action: "conference"
        },
        {
            Id: 3,
            ProductId: [2],
            Queue: "healthuwmalaylam",
            TransferName: 'Malyalam Underwriters Transfer',
            Transfertype: "sales_to_sales",
            Action: "conference"
        },
        {
            Id: 4,
            ProductId: [2],
            Queue: "healthuwtamil",
            TransferName: 'Tamil Underwriters Transfer',
            Transfertype: "sales_to_sales",
            Action: "conference"
        }
    ],

    "CRTTransfer": [
        {
            Id: 1,
            ProductId: [2, 7, 115, 117],
            Queue: "",
            TransferName: 'Health',
            TransferProduct: 2,
            Transfertype: "sales_to_service",
            Action: "conference"
        },
        {
            Id: 2,
            ProductId: [2, 7, 115, 117],
            Queue: "",
            TransferName: 'Motor',
            TransferProduct: 117,
            Transfertype: "sales_to_service",
            Action: "conference"
        },
        {
            Id: 3,
            ProductId: [2, 7, 115, 117],
            Queue: "",
            TransferName: 'Term',
            TransferProduct: 7,
            Transfertype: "sales_to_service",
            Action: "conference"
        },
        {
            Id: 4,
            ProductId: [2, 7, 115, 117],
            Queue: "",
            TransferName: 'Savings',
            TransferProduct: 115,
            Transfertype: "sales_to_service",
            Action: "conference"
        },
    ]
}

export default TransferTypes;