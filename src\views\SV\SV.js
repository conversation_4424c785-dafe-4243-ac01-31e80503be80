import React, { useEffect, useState } from "react";
import Header from "./Header";
import Main from "./Main";
import RightBlock from "./RightBlock";
import RightBar from "./RightBar";
import MessageStrip from "./Header/messageStrip";

import { Drawer, Fab, Grid } from "@mui/material";
// import { makeStyles } from "@mui/material/styles";
import { makeStyles, useTheme } from '@mui/styles';
import { useMediaQuery } from '@mui/material';
import ErrorBoundary from "../../hoc/ErrorBoundary/ErrorBoundary";
import Footer from "./Footer/Footer";
import { connect, useSelector } from "react-redux";
import NoLeadToWork from "./Main/NoLeadToWork";
import rootScopeService from "../../services/rootScopeService";
import CallPickPopup from "./Main/Modals/CallPickPopup";
import PredictiveCallAlertPopup from "./Main/Modals/PredictiveCallAlertPopup";
import { setRefreshLead, set_BizRating } from "../../store/actions/SalesView/SalesView";
import User from "../../services/user.service";
import { CancelRounded, People } from "@mui/icons-material";
import {  GetConsolidatedBizHealthDataService, GetBHRPercentageAndColorService } from '../../../src/layouts/SV/components/Sidebar/helper/sidebarHelper';
import { reduceUserCookie, setPosCookie, showGenie } from "../../helpers/commonHelper";
import HdfcPasa from "./Main/HdfcPasa";
import { IsApptfeedbackLead, getScriptURLs } from "../../services/Common";
import { useScript } from "../../hooks/useScript";
import { CONFIG, SV_CONFIG } from "../../appconfig";
import { useInterval } from "./Main/helpers/useInterval";
import InboundVCAcceptPopup from "./Main/Modals/InboundVCAcceptPopup";
import { CALL_API } from "../../services";
import Genie from "../Genie";

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    width: "95%",
    backgroundColor: theme.palette.background.paper,
  },
}));


function SV(props) {
  const classes = useStyles();
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'), {
    defaultMatches: true
  });
  let [ParentLeadId, RefreshLead] = useSelector(state => {
    let { parentLeadId, RefreshLead } = state.salesview;
    return [parentLeadId, RefreshLead]
  });
  // let [BizRating] = useSelector(({ salesview }) => [salesview.BizRating]);
  const [urls, setUrls] = useState([]);
  let Ispriority = rootScopeService.getPriority();
  const isMobile = useSelector(state => state.common.isMobile);
  const CallType = useSelector(state => state.salesview.CallType);
  const [openOnlineCustDrawer, setOpenOnlineCustDrawer] = useState(false);
  const [showPredictiveCallAlertPopup, setshowPredictiveCallAlertPopup] = useState(false);
  const [context, setContext] = useState(null);
  const [showInboundVCPopup, setShowInboundVCPopup] = useState(false);
  const [customerName, setCustomerName] = useState('');
  const [leadId, setLeadId] = useState(0);

  const handleDrawerOpen = () => {
    setOpenOnlineCustDrawer(true);
  };
  const handleDrawerClose = () => {
    setOpenOnlineCustDrawer(false);
  }

  useEffect(() => {
    if (!SV_CONFIG.stopReduceUserCookie) {
      reduceUserCookie();
    }
  }, []);

  useEffect(() => {
    if (IsApptfeedbackLead()) {
      let url = CONFIG.PUBLIC_URL + '/apptview/' + rootScopeService.getEncryptEndUrl();
      window.location = url;
    }
    // lead refresh on browser back
    window.addEventListener('popstate', function (event) {
      props.setRefreshLeadToRedux(true);
    })

    setUrls(getScriptURLs());

    let usergrp = User.ProductList;
    let productid = 0;
    let roleId = User.RoleId;
    if (Array.isArray(usergrp)) {
      for (const item of usergrp) {
        if ([115, 1001].indexOf(item.ProductId) !== -1) {
          productid = 115;
          break;
        } else if ([7, 1000].indexOf(item.ProductId) !== -1) {
          productid = 7;
          break;
        }
      }
    };
  }, []);

  useEffect(() => {
    setPosCookie();
  }, []);

  // Remove IsInboundVC From localStorage on tab/browser close
  useEffect(() => {
    return () => {
      localStorage.removeItem('IsInboundVC');
    };
  }, []);

  useEffect(() => {
    let result = false;
    if (User.RoleId == 13) {
      let groupList = User.UserGroupList;
      let HOMCallsGroups = SV_CONFIG.HOMCallsGroups;
      if (Array.isArray(HOMCallsGroups) && Array.isArray(groupList)) {
        for (var i = 0, len = groupList.length; i < len; i++) {
          if (HOMCallsGroups.indexOf(groupList[i].GroupId) > -1) {
            result = true;
            break;
          }
        }
      }
    }

    if ((((CallType && ["PDOB"].indexOf(CallType) !== -1) || ["PDOB"].indexOf(localStorage.getItem("calltype")) !== -1)
      || (result && ((CallType && ["IB", "CTC"].indexOf(CallType) !== -1) || ["IB", "CTC"].indexOf(localStorage.getItem("calltype")) !== -1)))
      && (window.localStorage.getItem("onCall") === "true")) {
      setshowPredictiveCallAlertPopup(true);
      let timer = setTimeout(() => setshowPredictiveCallAlertPopup(false), 10 * 1000);
      return () => {
        clearTimeout(timer);
      };
    }
    else {
      setshowPredictiveCallAlertPopup(false);
    }

    if ((((CallType && ["IB"].indexOf(CallType) !== -1) || ["IB"].indexOf(localStorage.getItem("calltype")) !== -1))
      && (window.localStorage.getItem("onCall") === "true")
      && (window.localStorage.getItem("IBContext") === "crttosalescrosssell")) {
      setContext("crttosalescrosssell");
      setshowPredictiveCallAlertPopup(true);
      let timer = setTimeout(() => setshowPredictiveCallAlertPopup(false), 10 * 1000);
      window.localStorage.removeItem('IBContext');
      return () => {
        clearTimeout(timer);
      };

    }
    else {
      setshowPredictiveCallAlertPopup(false);
      setContext(null);
      window.localStorage.removeItem('IBContext');
    }
  }, [CallType])

  useInterval(() => {
    const isInboundVC = localStorage.getItem('IsInboundVC');
    const ibvcdata = localStorage.getItem('IBVCData');
    const ibvcDataParsed = JSON.parse(ibvcdata);
    if (ibvcDataParsed) {
      setLeadId(ibvcDataParsed.leadId);
    }
    if (!showInboundVCPopup) {
      if (isInboundVC && ibvcDataParsed) {
        setCustomerName('');
        const url = `api/LeadDetails/GetLeadBasicInfo?leadId=${ibvcDataParsed.leadId}`
        const _input = {
          url: url,
          method: 'GET',
          service: 'MatrixCoreAPI',
        }
        CALL_API(_input).then((resultData) => {
          setCustomerName(resultData.Name);
        }).catch((error) => { })

        setShowInboundVCPopup(true);
      }
      else {
        setShowInboundVCPopup(false);
      }
    }
  }, 1000)

  useScript(urls);

  let isNoLeadToWorkPage = !(RefreshLead || rootScopeService.getCustomerId());

  if (IsApptfeedbackLead()) {
    return null;
  }

  return (
    <>

      <div className={`${classes.root} wrapper`}>

        <Grid container spacing={1}>

          <Grid item sm={12} md={12} xs={12}>
            {(User.RoleId === 13 && Ispriority) ?
              <ErrorBoundary name="Header">
                <Header></Header>
              </ErrorBoundary>
              : null
            }
            {(User.RoleId === 13 && !Ispriority) ?
              <ErrorBoundary name="MessageStrip">
                <MessageStrip/>
              </ErrorBoundary>
              : null
            }
          </Grid>


          <Grid item sm={12} md={9} xs={12}>
            <Grid container spacing={2}>
              <Grid item sm={6} md={5} xs={12}>
                <ErrorBoundary name="HdfcPasa">
                  <HdfcPasa />
                </ErrorBoundary>
              </Grid>
            </Grid>
          </Grid>

          <Grid item sm={(isDesktop || isNoLeadToWorkPage) ? 9 : 12} xs={12}>
            <ErrorBoundary name="Main">
              {!isNoLeadToWorkPage ? <Main /> : <NoLeadToWork />}
              {/* {(RefreshLead || ParentLeadId) && <Main />}
              {(!RefreshLead && ParentLeadId == undefined) && <NoLeadToWork />} */}
            </ErrorBoundary>
          </Grid>
          {isDesktop || isNoLeadToWorkPage ?
            <Grid item sm={3} md={3} xs={12}>
              <ErrorBoundary name="RightBlock">
                <RightBlock Isshow={!!ParentLeadId} />
              </ErrorBoundary>
            </Grid>
            : null
          }
          {
          }
          {(!isMobile) ?
            (User.RoleId === 13 && Ispriority) ?
              <Grid item sm={12} md={12} xs={12}>
                <Footer></Footer>
              </Grid> : null
            :
            (<Fab color="primary" className='OnlineCustomerFabBtn' aria-label="add">
              <People onClick={handleDrawerOpen} />
            </Fab>)
          }
          {User.RoleId === 13 && showGenie() &&
            <ErrorBoundary name="Genie">
              <Genie />
            </ErrorBoundary> 
          }
          <Drawer anchor='bottom' className="OnlineCustomerDrawer" open={openOnlineCustDrawer} onClose={handleDrawerClose}>
            <p class="text-right" onClick={handleDrawerClose}><CancelRounded /></p>
            <Footer></Footer>
          </Drawer>
        </Grid>
      </div>
      <ErrorBoundary name="RightBar">
        <RightBar />
      </ErrorBoundary>
      <ErrorBoundary name="CallPickPopup">
        <CallPickPopup />
      </ErrorBoundary>
      {showPredictiveCallAlertPopup ? <ErrorBoundary name="PredictiveCallAlertPopup">
        <PredictiveCallAlertPopup showPredictiveCallAlertPopup={showPredictiveCallAlertPopup} setshowPredictiveCallAlertPopup={setshowPredictiveCallAlertPopup} CallType={CallType} Context={context} />
      </ErrorBoundary> : null}
      <ErrorBoundary name="InboundVCAcceptPopup">
        {showInboundVCPopup && (
          <InboundVCAcceptPopup
            showInboundVCPopup={showInboundVCPopup}
            setShowInboundVCPopup={setShowInboundVCPopup}
            customerName={customerName}
            leadId={leadId}
          />
        )}
      </ErrorBoundary>
    </>
  );
}

const mapDispatchToProps = (dispatch) => {
  return {
    setRefreshLeadToRedux: (value) => dispatch(setRefreshLead({ RefreshLead: value })),
  };
};
export default connect(() => ({}), mapDispatchToProps)(SV);