import pluck from "underscore/modules/pluck";
import { apiConfig, CONFIG, ConfigUrl, SV_CONFIG } from "../appconfig";
import { getAsteriskToken, IsProgressiveApp } from "../helpers";
import { localStorageCache } from "../utils/utility";
import rootScopeService from "./rootScopeService";
import User from "./user.service";
import { IsSourceCustomerWhatsapp } from ".";
import { getSourcefromQuery } from "../helpers";
import { getSearchObjFromLS } from "../helpers/commonHelper";
import { enqueueSnackbar } from "notistack";
const { CALL_API } = require(".");

export const API_STATUS = {
    SUCCESS: 'SUCCESS',
    FAIL: 'FAIL',
    CHECKING: 'CHECKING',
    LOADING: 'LOADING',
    RETRY: 'RETRY',
    UNAUTHORISED: 'UNAUTHORISED'
}

const Common = {

    SetNotificationAction: (requestData) => {
        //reqData {"agentID":30155,"parentID":162889898,"action":4}
        // const input = {
        //     url: `AgentNotification/MarkTLAction/${requestData.UserId}`,
        //     method: 'POST',
        //     requestData: {
        //         "agentID": requestData.UserId, "parentID": requestData.parentID, action: requestData.action
        //     }
        // };
        // return CALL_API(input).then(function (response) {
        //     return response;
        // });
    },

    InsertUpdateErrorLog: (reqData) => {
        const input = {
            url: window.location.origin + SV_CONFIG["internalMatrixRef"].serviceUrl + "InsertUpdateErrorLog",
            method: 'POST',
            service: "custom",
            requestData: {
                objError: reqData
            }
        };
        return CALL_API(input).then(function (response) {
            return response;
        });
    },
    InsertUpdateAgentProductivity: (requestData) => {
        const input = {
            url: "Agents/InsertUpdateAgentProductivity",
            method: 'POST',
            requestData: requestData
        };
        return CALL_API(input);
    },
    // CheckCustomerOTPVerified: (requestData) => {
    //     const input = {
    //         url: "LeadDetails/CheckCustomerOTPVerified",
    //         method: 'POST',
    //         requestData
    //     };
    //     return CALL_API(input);
    // },


}

export default Common;

export const checkTimeZoneService = (leadIds) => {

    const input = {
        url: `onelead/api/LeadPrioritization/CheckLeadTimeZone`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: leadIds
    };
    return CALL_API(input)
}

// export const GetCustomerCallbackProducts = () => {
//     let GetCustomerActivityProduct = false;
//     let GetNewCustomerActivityProducts = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.GetCustomerActivityProducts) || SV_CONFIG.GetCustomerActivityProducts;

//     GetCustomerActivityProduct=GetNewCustomerActivityProducts && GetNewCustomerActivityProducts.indexOf(rootScopeService.getProductId())>-1
//     return GetCustomerActivityProduct;

// }

export const GetOneLeadService = (userID, productId) => {
    let source = "";
    if (IsProgressiveApp()) {
        source = "appprogressive";
    }
    const input = {
        url: `onelead/api/LeadPrioritization/GetOneLead/${userID}/${productId}?Source=${source}`,
        method: "GET",
        service: "MatrixCoreAPI"
    };
    return CALL_API(input)
}

export const GetCustomerAppointmentData = (InputParm, Type) => {
    const input = {
        url: `api/fos/GetAppointmentSummary?InputParm=${InputParm}&Type=${Type}&NoOfDays=90`,
        method: "GET",
        service: "MatrixCoreAPI"
    };
    return CALL_API(input)

}

export const LinkApptWithLead = (leadId, apptId, Type) => {
    const input = {
        url: `api/fos/LinkAppointment`,
        method: "POST",
        service: "MatrixCoreAPI",
        requestData: {
            "LeadId": leadId, "ApptId": apptId, Type: Type
        }
    };
    return CALL_API(input)

}

export const IsGetCityListProductsV2 = () => {
    let GetCityListProduct = false;
    let GetNewCityListProducts = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.GetCityListProducts) || SV_CONFIG.GetCityListProducts;

    GetCityListProduct = Array.isArray(GetNewCityListProducts) && GetNewCityListProducts.indexOf(rootScopeService.getProductId()) > -1
    return GetCityListProduct;

}
export const IsGetCrossSellProductsV2 = () => {
    let GetCrossSellProduct = false;
    let GetNewCrossSellProducts = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.GetCrossSellProducts) || SV_CONFIG.GetCrossSellProducts;

    GetCrossSellProduct = Array.isArray(GetNewCrossSellProducts) && GetNewCrossSellProducts.indexOf(rootScopeService.getProductId()) > -1
    return GetCrossSellProduct;

}

export const IsGetSubProductsV2 = () => {
    let GetSubProduct = false;
    let GetNewSubProducts = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.GetSubProducts) || SV_CONFIG.GetSubProducts;

    GetSubProduct = Array.isArray(GetNewSubProducts) && GetNewSubProducts.indexOf(rootScopeService.getProductId()) > -1
    return GetSubProduct;

}

export const IsLeadContent = () => {
    try {
        let ans = (window.location.href && window.location.href.toLowerCase().indexOf('leadcontent') !== -1) ? true : false;
        return ans;
    }
    catch {
        return false;
    }
}
export const CheckIsMonthlyModeTeam = () => {
    let usergrp = User.UserGroupList || [];
    if (usergrp.length > 0) {
        let IsMonthlyModeTeam = Array.isArray(usergrp) && usergrp.some(item => [1610, 3160, 3156, 3161].indexOf(item.GroupId) !== -1);
        return IsMonthlyModeTeam;
    }
    return false;
}
export const IsRemoveConnectCallSF = () => {
    const RemoveCallSFCondition = (window && window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.RemoveCallSFCondition) || SV_CONFIG.RemoveCallSFCondition;
    if (CheckIsMonthlyModeTeam() == true && [106].indexOf(rootScopeService.getProductId()) > -1
        && RemoveCallSFCondition == false) {
        return false;
    }
    return true;
}

export const IsCustomerAccess = () => {
    try {
        let ans = (window.location.href && window.location.href.toLowerCase().indexOf('apptview') !== -1) ? true : false;
        return ans;
    }
    catch {
        return false;
    }
}

export const GetTLCallingNo = (userID) => {

    const input = {
        url: `onelead/api/LeadPrioritization/GetTLCallingNo/${userID}`,
        method: "GET",
        service: "MatrixCoreAPI"
    };

    return CALL_API(input)

}

export const createCallTranserUrl = (Campaign, transfer_type, productId, data, isCreateLead, thirdpartynumber, parentLeadId, UserGroupId, subProductId = 0, initialLeadSubProductId = 0, isSmeRenewal = false, highlightAdvisor="", title="", customerId = "", crossProduct = false, crossBULeadId = null) => {
    let url = "";
    let RMEmployeeID, action;
    try {
        ({ RMEmployeeID, action } = data);
    } catch { }

    url = "u=" + User.UserId + "&agent=" + User.EmployeeId;
    url = url + "&transfer_agents=&campaign=" + Campaign + "&bookingid=" + parentLeadId + "&transfer_type=" + transfer_type;
    if (thirdpartynumber) url += "&thirdpartynumber=" + thirdpartynumber;
    if (productId) url += "&productid=" + productId;
    if (UserGroupId) url += "&groupid=" + UserGroupId;

    if (isCreateLead === 'yes') {
        url = url + "&iscreatelead=" + isCreateLead;
    }

    if (RMEmployeeID) {
        url += "&assignedagent=" + RMEmployeeID;

    }
    if (action) {
        url += "&action=" + action
    }
    if (transfer_type === "SmeInterTeamTransfer" && subProductId > 0) {
        url += "&subProductId=" + subProductId + "&initialLeadSubProductId=" + initialLeadSubProductId + "&isSmeRenewal=" + isSmeRenewal
    }

    if (highlightAdvisor) {
        url += "&highlightEcode=" + highlightAdvisor
    }

    if (title) {
        url += "&title=" + title;
    }

    if (customerId) {
        url += "&customerId=" + customerId;
    }

    url += "&crossProduct=" + crossProduct;

    if (crossBULeadId) {
        url += "&crossBULeadId=" + crossBULeadId;
    }
    
    return url;
}

export const IsDiscountOptionAvailable = () => {
    let IsDiscountOptionAvailable = false;
    IsDiscountOptionAvailable = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.IsDiscountOptionAvailable);
    return IsDiscountOptionAvailable;

}

export const AddLeadToPriorityQueueService = async (priorityLead) => {
    const input = {
        url: 'onelead/api/LeadPrioritization/ValidateAddLeadToPriorityQueue',
        method: 'POST', service: 'MatrixCoreAPI',
        requestData: {
            "UserId": User.UserId,
            "Leads": [priorityLead]
        }
    };
    return CALL_API(input);
}

export const AddLeadsToQueueService = (selectedLeads, Reason, ReasonId, Priority) => {
    let leadIds = pluck(selectedLeads, "LeadId");
    let incompatibleTimeZone = [];
    let res = {};
    return checkTimeZoneService(leadIds).then(function (resultData) {
        if (resultData != null && resultData != '' && Array.isArray(resultData)) {
            selectedLeads.forEach((items) => {
                if (resultData.indexOf(items.LeadId) > -1) {
                    if (items) {
                        var lead = {
                            "LeadId": items.LeadId,
                            "Name": items.CustName,
                            "CustomerId": items.CustID,
                            "UserID": User.UserId,
                            "Priority": Priority,
                            "ProductId": items.ProductID || rootScopeService.getProductId(),
                            "Reason": Reason,
                            "ReasonId": ReasonId,
                            "CallStatus": "",
                            "IsAddLeadtoQueue": 1,
                            "IsNeedToValidate": 0
                        }

                        AddLeadToPriorityQueueService(lead).then((resultData) => {
                            if (resultData != null) {
                                if (resultData && resultData.message && resultData.message == "Success") {
                                    // enqueueSnackbar("Lead " + items.LeadId + " Added successfully", {
                                    //     variant: 'success',
                                    //     autoHideDuration: 3000,
                                    // });
                                }
                                else {
                                    let error = (resultData && resultData.message && resultData.message !== '')
                                        ? (resultData.LeadID + " : " + resultData.message)
                                        : "Error while adding lead: " + resultData.LeadID;
                                    // enqueueSnackbar(error, { variant: 'error', autoHideDuration: 2000, });
                                }
                            }
                        });

                        // todo
                        //props.handleClose();
                        // var PredictiveDialLeads = localStorage.getItem('PredictiveDialLeads');
                        // if (PredictiveDialLeads != null) {
                        //     PredictiveDialLeads = JSON.parse(PredictiveDialLeads);
                        //     PredictiveDialLeads.CallStatus = "";
                        //     localStorage.setItem('PredictiveDialLeads', JSON.stringify(PredictiveDialLeads));
                        // }
                    }

                }
                else {
                    incompatibleTimeZone.push(items.LeadId)
                }
            });
            res.incompatibleTimeZone = incompatibleTimeZone;
        }
        return res;
    });
}

export const GetWhatsAppURL = (LeadID, Type, productId, customerId = 0) => {
    const input = {
        url: "api/SalesView/getWhatsAppURL/" + LeadID + "/" + Type + "/" + productId + "/" + customerId,
        method: 'GET',
        service: 'MatrixCoreAPI',
    }
    return CALL_API(input)
}

export const SendEmail = function (LeadID, ProductID, EmailId, AgentId, SupplierId) {
    var supplierArray = [];

    var triggerName = "";
    if (ProductID == 2 || ProductID == 117) {
        triggerName = "SoftCopyReceivedE2E";
    }
    else if (ProductID == 114) {
        triggerName = "Policy_Issued";
    }
    else if (ProductID == 3) {
        triggerName = "PolicyIssuedE2E";
    }
    else if (ProductID == 130 || ProductID == 118) {
        triggerName = "PolicyIssued";
    }

    supplierArray.push(SupplierId);
    var objEmailData = {
        "CommunicationDetails": {
            "LeadID": LeadID,
            "IsBooking": true,
            "ProductID": ProductID,
            "Conversations": [
                {
                    "From": "<EMAIL>",
                    "ToReceipent": [
                        EmailId
                    ],
                    "TriggerName": triggerName,
                    "CreatedBy": "SalesView",
                    "UserID": AgentId,
                    "AutoTemplate": true,
                    "SupplierId": supplierArray,
                    "LeadStatusID": "0",
                    "SubStatusID": "0"
                }
            ],
            "BookingDetail": { "BookingType": "DIRECT" },
            "CommunicationType": "1",
        }
    }
    var input = { "url": "send", "requestData": objEmailData, "timeout": "1000", "method": "POST", "service": "commService" };

    return CALL_API(input);
};

export const SendSMS = function (LeadID, ProductID, MobileNo, AgentId, SupplierId) {
    var supplierArray = [];
    supplierArray.push(SupplierId);
    var objSMSData = {
        "CommunicationDetails": {
            "LeadID": LeadID,
            "IsBooking": true,
            "ProductID": ProductID,
            "Conversations": [
                {
                    "ToReceipent": [
                        MobileNo
                    ],
                    "TriggerName": "PolicyIssueShortLinkSMS",
                    "CreatedBy": "SalesView",
                    "UserID": AgentId,
                    "AutoTemplate": true,
                    "SupplierId": supplierArray,
                    "LeadStatusID": "0",
                    "SubStatusID": "0"
                }
            ],
            "BookingDetail": { "BookingType": "DIRECT" },
            "CommunicationType": "2",
        }
    }
    var input = { "url": "send", "requestData": objSMSData, "timeout": "1000", "method": "POST", "service": "commService" };
    return CALL_API(input);
};
export const getCommonHeaders = () => {
    let Headers = {};

    if (IsSourceCustomerWhatsapp() == true) {

        Headers = {

            "encryptleadid": window.localStorage.getItem('EncryptedLeadId'),//,//'cSxgNtq5KIkz+OARHELtCQ==',//'zP7qJsUJarQbZHFCezGAPQ==',////localStorage.getItem('parentLeadId'),
            "source": getSourcefromQuery(),
            "token": localStorage.getItem('CustomerToken'),//'283973',//localStorage.getItem('CustomerToken'),
            "content-type": "application/json"
        }
    }

    else {

        Headers = {
            "AgentId": User.UserId,
            "Token": getAsteriskToken(),
            "Content-Type": "application/json"
        }
    }
    return Headers;

}
/*
Send Policy Soft Copy
*/
export const resendPolicyCopyService = (LeadID, ProductId, CommunicationType = 1) => {
    // CommunicationType => sms: 2, email: 1

    const input = {
        url: 'api/Ticket/resendpolicycopy',
        requestData: {
            CommunicationType,
            LeadID,
            ProductId
        },
        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 2000
    }
    return CALL_API(input);
}

export const GetBmsUrlService = (LeadId, Source = "SalesView") => {
    const input = {
        url: `api/LeadDetails/GetbmsLink/${LeadId}/${User.UserId}/${Source}`,
        method: 'GET', service: 'MatrixCoreAPI',
        timeout: 2000
    }
    return CALL_API(input);
}
export const GetSelfInforcementRatingData = () => {
    const input = {
        url: `api/SalesView/GetSelfEnforcementRatingData`,
        method: 'GET', service: 'MatrixCoreAPI',
        timeout: 2000
    }
    return CALL_API(input);
}
export const GetHierarchialInforcementRatingData = (userId, Role, ProductId) => {
    const input = {
        url: 'api/SalesView/GetInforcementRatingHierarchialData?UserId=' + userId + '&Role=' + Role + '&ProductId=' + ProductId,
        method: 'GET', service: 'MatrixCoreAPI',
        timeout: 5000
    }
    return CALL_API(input);
}
export const FetchBHRBookingDataTLWise = (EmployeeCode, Role, ProductID) => {
    const input = {
        url: `api/SalesView/FetchBHRBookingDataTLWise/${EmployeeCode}/${Role}/${ProductID}`,
        method: 'GET', service: 'MatrixCoreAPI',
        timeout: 5000
    }
    return CALL_API(input);
}
export const GetSelfInforcementRatingDataMonthWise = (month, year) => {
    const input = {
        url: 'api/SalesView/GetSelfEnforcementRatingDataMonthWise?month=' + month + '&year=' + year,
        method: 'GET', service: 'MatrixCoreAPI',
        timeout: 2000
    }
    return CALL_API(input);
}
export const GetCallDetails = (parentLeadId) => {

    const input = {
        url: `api/SalesView/GetCallDetails/${parentLeadId}`,
        method: "GET",
        service: "MatrixCoreAPI"
    }
    return CALL_API(input);
}

export const getCurrentTalkTime = () => {
    let durationTime = 0;
    var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;
    let ConnectCallSF = window.localStorage.getItem('ConnectCallSF');
    if (onCall && ConnectCallSF) {
        ConnectCallSF = JSON.parse(ConnectCallSF);
        var CallinitateTime = ConnectCallSF.CallInitTime;

        if (CallinitateTime) {
            durationTime = (new Date() - new Date(CallinitateTime)) / 1000;
        }
    }
    return durationTime;
}

export const IsLeadDataSetActive = (AllLeads) => {
    let IsActive = false;
    Array.isArray(AllLeads) && AllLeads.forEach(lead => {
        if ((lead.StatusMode == 'P')) {
            IsActive = true;
        }
    })
    return IsActive;
}
export const getCustomersRelationshipManager = (customerId) => {
    const input = {
        url: `api/Bms/GetRMDetails/${customerId}`,
        method: "GET",
        service: "MatrixCoreAPI"
    }
    return CALL_API(input);
    // .catch((e) => {
    //     console.error('getCustomersRelationshipManager: ', e);
    //     return "";
    // });

}
export const getCustomersRelationshipManagerTerm = (customerId, LeadId) => {
    const input = {
        url: `api/Bms/GetRMDetailsForTermSavings/${customerId}/${LeadId}`,
        method: "GET",
        service: "MatrixCoreAPI"
    }
    return CALL_API(input);
    // .catch((e) => {
    //     console.error('getCustomersRelationshipManager: ', e);
    //     return "";
    // });

}
export const GetAlreadyAssignedRMDetails = (customerId, LeadID, ProductID) => {
    const input = {
        url: `api/SalesView/IsRMVirtuallyAllocated`,
        method: 'POST',
        requestData: {
            "LeadId": LeadID,
            "ProductId": ProductID,
            "CustomerId": customerId
        },
        service: 'MatrixCoreAPI',
    }
    return CALL_API(input);
}
export const AssignRelationshipManagerBeforeIssuance = (customerId, LeadID, ProductID) => {
    const input = {
        url: `api/SalesView/AllocateRMBeforeIssuance`,
        method: 'POST',
        requestData: {
            "LeadId": LeadID,
            "ProductId": ProductID,
            "CustomerId": customerId
        },
        service: 'MatrixCoreAPI',
    }
    return CALL_API(input);
}
export const GetRMCommentsService = (LeadIds, EmployeeId) => {
    const input = {
        url: `api/Bms/GetRMComments`,
        method: "POST",
        requestData: {
            "LeadIds": LeadIds,
            "EmployeeID": EmployeeId
        },
        service: 'MatrixCoreAPI',
    }
    return CALL_API(input);
}


export const ScheduleCTCConnectCallService = (data, followupTS) => {
    const productService = getProductService(data.ProductId)

    if(SV_CONFIG && SV_CONFIG.UseSchCTCConnectCallApi) {
        let url = `CTCRestService.svc/SchCTCConnectCall/0/${data.LeadID}/${productService}/MATRIX/${followupTS}?`;
        if (data.SubIssueID && data.IssueID) {
            url += `subissue=${data.SubIssueID}&issue=${data.IssueID}&`
        }
        if (data.comment) {
            url += `comment=${data.comment}`
        }
        const input = {
            url,
            method: 'GET', service: 'CTCservice', timeout: 's'
        };
        return CALL_API(input);
        
    } else {
        const requestData = {
            "LeadID": data.LeadID,
            "LeadSource":"MATRIX",
            "GroupID":productService,
            "ScheduleTime":followupTS,
            "CallNow":data.CallNow
        };
        if (data.SubIssueID && data.IssueID) {
            requestData.SubIssue = data.SubIssueID;
            requestData.Issue = data.IssueID;
        }
        if (data.comment) {
            requestData.Comments = data.comment;
        }
    
        const input = {
            url: `api/WebSiteService/CTCCallBack`,
            method: 'POST', 
            service: 'MatrixCoreAPI',
            requestData
        };
        return CALL_API(input);
    }
}

export const getMoodleUrlService = () => {
    // var reqData = {
    //     "obj": {
    //         "UserName": User.UserName,
    //         "EmployeeId": User.EmployeeId,
    //         "Products": User.PrdGroupList,
    //         "RoleId": User.RoleId
    //     }
    // };
    // const input = {
    //     url: 'User/getMoodleUrl', method: 'POST', service: 'core',
    //     requestData: reqData
    // }
    // return CALL_API(input);
    return null;
}

export const getProductNamebyId = (productId) => {
    if (productId == '' || productId == 0) {
        return "";
    }
    var obj = {
        2: "Health",
        159: "Int Personal Loans",
        160: "International Bank Accounts",
        165: "Partnerships",
        101: "Home Insurance",
        139: "Commercial Vehical",
        148: "International Car",
        155: "International Term",
        163: "International TwoWheeler",
        151: "International health",
        7: "Term",
        1000: "Term",
        115: "Investment",
        117: "Car",
        219: "Customer Assistance"

    }
    return obj[productId];
}
const getProductService = (productId) => {
    switch (productId) {
        case 138:
            return 'CANCER_SERVICE';
        case 1:
            return 'CAR_SERVICE';
        case 117:
            return 'CAR_SERVICE';
        case 139:
            return 'Comm_Service';
        case 2:
            return 'HEALTH_SERVICE';
        case 101:
            return 'HOME_SERVICE';
        case 115:
            return 'NTM_SERVICE';
        case 7:
            return 'TERM_SERVICE';
        case 3:
            return 'TRAVEL_SERVICE';
        case 114:
            return 'TW_SERVICE';
        default:
            return 'UNREGISTERED_SERVICE';
    }

}

export const GetCustomerPitchedContent = (LeadId) => {
    const input = {
        url: `api/LeadDetails/GetCustomerPitchedRemarks?leadid=${LeadId}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}

export const fetchConfigService = (configName, useCache = true) => {
    let cachedData = localStorageCache.readFromCache(configName);
    if (useCache && cachedData !== null) { return Promise.resolve(cachedData) }

    if (configName) {
        let header = getCommonHeaders();
        let input = {
            url: ConfigUrl,
            timeout: 3000,

            // Headers: {
            //     "AgentId": User.UserId,
            //     "Token": localStorage.getItem('AsteriskToken'),
            //     "Content-Type": "application/json"
            // },
            Headers: header,

            // Headers : {

            //     "encryptleadid": window.localStorage.getItem('EncryptedLeadId'),//,//'cSxgNtq5KIkz+OARHELtCQ==',//'zP7qJsUJarQbZHFCezGAPQ==',////localStorage.getItem('parentLeadId'),
            //     "source": "customerwhatsapp",
            //     "token": localStorage.getItem('CustomerToken'),//'283973',//localStorage.getItem('CustomerToken'),
            //     "content-type": "application/json"
            // },

            service: 'custom'

        }
        // if (IsSourceCustomerWhatsapp()) {
        //     const response = apiConfig;
        //     if (useCache) {
        //         localStorageCache.writeToCache(configName, response, 1 * 60 * 60 * 1000);
        //         window.location.reload();
        //     }
        //     return Promise.resolve(response);
        // }
        return CALL_API(input).then((res) => {
            const response = res;
            if (useCache) {
                localStorageCache.writeToCache(configName, response, 1 * 60 * 60 * 1000);
                window.location.reload();
            }
            return Promise.resolve(response);
        }).catch((error) => {
            if (IsSourceCustomerWhatsapp()) {
                const response = apiConfig;
                if (useCache) {
                    localStorageCache.writeToCache(configName, response, 1 * 60 * 60 * 1000);
                    window.location.reload();
                }
                return Promise.resolve(response);
            }
            else {
                return Promise.reject(error)
            }

        })
    }
    return Promise.resolve({});
}
export const SetRealTimeLeadStatusService = (ParentId, LeadSource, productId) => {
    const requestData = {
        ParentId, LeadSource, productId
    }

    const input = {
        url: "onelead/api/LeadPrioritization/SetRealTimeLeadStatus",
        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 's',
        requestData
    };

    return CALL_API(input);
}

export const InvAdvisorVerifyService = (ParentId) => {
    const input = {
        url: `api/SalesView/InvAdvisorVerify/${ParentId}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    };

    return CALL_API(input);
}
export const MotorAdvisorVerifyService = (ParentId) => {
    const input = {
        url: `api/SalesView/MotorAdvisorVerify/${ParentId}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    };

    return CALL_API(input);
}


export const verifyTokenService = () => {
    let asteriskToken = getAsteriskToken();
    const input = {
        url: `customer/verifytoken/`,
        method: 'POST', service: 'CustomerNotificationURL',
        requestData: { "userid": User.UserId, "token": asteriskToken }
    }
    return CALL_API(input);
}
export const GetFOSChurnLogicMsgService = (ParentLeadId) => {

    const input = {
        url: `api/SalesView/GetFOSChurnLogicMsg/${ParentLeadId}`,
        method: 'GET',
        service: 'MatrixCoreAPI',
    }
    return CALL_API(input)
}
export const assignToCallCentreService = (ParentLeadId) => {

    const input = {
        url: `api/SalesView/ReAssignChurnLead`,
        method: 'POST',
        requestData: {
            "LeadID": ParentLeadId
        },
        service: 'MatrixCoreAPI',
    }
    return CALL_API(input)
}


export const GeneratePbmeetLinkService = (requestData, usePbmeetV2 = false) => {
    let url = `api/SalesView/GeneratePbmeetLink`;
    if (usePbmeetV2) {
        url = `api/SalesView/GeneratePbmeetLinkV2`
    }
    const input = {
        url,
        method: 'POST', service: 'MatrixCoreAPI',
        timeout: 3000,
        requestData,
    }
    return CALL_API(input);
}

export const GenerateVerificationLinkService = (requestData) => {
    const url = `api/SalesView/GenerateVerificationVCLink`;
    
    const input = {
        url,
        method: 'POST', service: 'MatrixCoreAPI',
        timeout: 3000,
        requestData,
    }
    return CALL_API(input);
}

export const GeneratePbmeetLinkServiceV2 = (requestData) => {
    let url = `api/SalesView/GeneratePbmeetLinkV2`;
    const input = {
        url,
        method: 'POST', service: 'MatrixCoreAPI',
        timeout: 3000,
        requestData,
    }
    return CALL_API(input);
}

export const GeneratePbmeetLinkServiceV3 = (requestData) => {
    let url = `api/SalesView/GeneratePbmeetLinkV3`;
    const input = {
        url,
        method: 'POST', service: 'MatrixCoreAPI',
        timeout: 3000,
        requestData,
    }
    return CALL_API(input);
}

export const CheckCustomerOnCallService = (LeadId) => {
    let url = `api/SalesView/GetPBMeetConferenceLink?LeadId=${LeadId}`;
    const input = {
        url,
        method: 'GET', service: 'MatrixCoreAPI',
        timeout: 3000,
    }
    return CALL_API(input);
}

export const GetUpsellClickStatus = (LeadId) => {
    const input = {
        url: `api/LeadDetails/GetUpsellClickStatus?leadid=${LeadId}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}

export const GetRenewalDetails = (LeadId) => {
    const input = {
        url: `coremrs/api/LeadDetails/GetRenewalDetails/${LeadId}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}
export const FetchProposalFormUrl = (LeadId) => {
    const input = {
        url: `api/HealthRenewal/FetchProposalForm/${LeadId}/${rootScopeService.getCustomerId()}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}

export const GetHealthProfileDetails = (LeadId, ProductId) => {
    var reqData = {
        LeadId,
        ProductId,
        "CustomerId": rootScopeService.getCustomerId()
    };

    var input = {
        url: `coremrs/api/ProposalService/GetProductDetails_SV/?LeadId=${LeadId}&ProductId=${ProductId}&CustomerId=${rootScopeService.getCustomerId()}`,
        method: 'GET',
        service: 'MatrixCoreAPI'
    }
    let IsNewAPI = true;
    let ProductDetails_SVConfig = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.ProductDetails_SVConfig) || SV_CONFIG.ProductDetails_SVConfig;
    if (ProductDetails_SVConfig && ProductDetails_SVConfig.indexOf(rootScopeService.getProductId()) > -1) {
        IsNewAPI = false;
    }
    if(IsNewAPI == false)
    {
        input = {
            url: 'Proposal/GetProductDetails_SV', method: 'POST', service: 'core',
            requestData: reqData
        }
    }
    return CALL_API(input);
}

export const SaveCarDetailsAPI = (reqData) => {
    const input = {
        url: 'LeadDetails/SaveCarDetails', method: 'POST', service: 'core',
        requestData: reqData
    }
    return CALL_API(input);
}

export const GetPortDetails = (LeadId) => {
    const input = {
        url: `api/HealthRenewal/GetSetPortDetails?LeadId=${LeadId}&type=2`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}

export const GetExclusiveBenifits = (LeadId) => {
    const input = {
        url: `api/HealthRenewal/GetExclusiveBenifit?LeadId=${LeadId}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}

export const checkUserGroup = (checkGrpsArray) => {
    let newAPIGroups = [];
    let isUserGrp = false;
    if (Array.isArray(checkGrpsArray)) {
        newAPIGroups = checkGrpsArray
    } else { return false; }
    let usergrp = User.UserGroupList || [];
    usergrp.forEach(function (item) {
        if (Array.isArray(newAPIGroups) && (newAPIGroups.indexOf(item.GroupId) > -1)) {
            isUserGrp = true;
        }
    });
    return isUserGrp;
}
export const GetAgentAllLeadsService = (showCallableLeads) => {
    let UserProdSession = JSON.parse(window.sessionStorage.getItem('UserMainProdId'));
    if (!UserProdSession) {
        try {
            // Handle case when salesview opened directly (without matrix)
            UserProdSession = { ProdId: User.ProductList[0].ProductId }
            window.sessionStorage.setItem('UserMainProdId', JSON.stringify(UserProdSession));
        }
        catch { }
    }

    if (UserProdSession && User && User.UserId) {
        let UserProd = UserProdSession.ProdId;
        let mathCal = Math.floor(Math.random() * Math.floor(1000));

        let input = {
            url: `onelead/api/LeadPrioritization/GetAgentAllLeads/${User.UserId}/${UserProd}?showCallableLeads=${showCallableLeads}&v=${mathCal}`,
            method: 'GET', service: 'MatrixCoreAPI'
        }

        return CALL_API(input);
    }
    else {
        return Promise.reject(new Error('Invalid UserProdSession or UserId'));
    }
}


export const GetCustomerCommentConfig = () => {
    let GetCustomerCommentConfig = false;
    let GetCustomerCommentConfigs = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.GetCustomerCommentConfig) || SV_CONFIG.GetCustomerCommentConfig;

    GetCustomerCommentConfig = GetCustomerCommentConfigs && GetCustomerCommentConfigs.indexOf(rootScopeService.getProductId()) > -1
    return GetCustomerCommentConfig;

}

export const GetCustomerComment = (ParentLeadId, requestData) => {
    const input = {
        url: `coremrs/api/MRSCore/GetCustomerComment/${ParentLeadId}`,
        method: 'GET',
        service: 'MatrixCoreAPI',
    }
    return CALL_API(input)
}



export const SetCustomerComment = (requestData) => {

    const input = {
        url: `coremrs/api/MRSCore/SetCustomerComment`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData
    }
    return CALL_API(input)

}

export const getCustContactInfoService = (customerId, parentLeadId, type = 'emailid') => {

    const input = {
        url: `onelead/api/Communication/getCustContactInfo/${customerId}/${type}/${parentLeadId}`,
        method: "GET",
        service: 'MatrixCoreAPI',
    };

    return CALL_API(input)
}

export const setCustContactInfoService = (requestData) => {

    const input = {
        url: 'api/SalesView/SetCustContactEmailInfo',
        requestData,
        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 6000
    }
    return CALL_API(input);
}

export const sendVCTemplateService = (reqData) => {
    let input = {
        url: 'api/SalesView/sendVCTemplate',
        method: 'POST', service: 'MatrixCoreAPI', timeout: 'm',
        requestData: reqData
    };
    return CALL_API(input);
}

export const sendVCTemplateServiceV1 = (reqData) => {
    let input = {
        url: 'api/SalesView/sendVCTemplateV1',
        method: 'POST', service: 'MatrixCoreAPI', timeout: 'm',
        requestData: reqData
    };
    return CALL_API(input);
}

export const GetCustomerPlansfromCJ = (LeadID, ProductID = null) => {
    const input = {
        url: 'api/SalesView/GetCustomerPlansFromCJv2?LeadID=' + LeadID + '&ProductID=' + ProductID,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 6000
    }
    return CALL_API(input);
}
export const PostDiscountDetailstoCJ = (requestData) => {
    const input = {
        url: 'api/SalesView/AvailDiscountonCJv2',
        requestData: requestData.ProductID ? requestData : { requestData },
        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 2000
    }
    return CALL_API(input);
}

export const GetCouponRedeemValue = (CustomerId) => {
    const input = {
        url: 'api/WebSiteService/GetCouponRedeemValue',
        requestData: {
            CustomerId
        },
        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 2000
    }
    return CALL_API(input);
}
export const SetCouponRaiseRequest = (reqData) => {
    const input = {
        url: 'api/WebSiteService/SetCouponRaiseRequest',
        requestData: reqData,

        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 2000
    }
    return CALL_API(input);
}
export const SendCouponTrigger = (LeadId) => {
    const input = {
        url: `api/SalesView/SendCouponTrigger/${LeadId}`,

        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 2000
    }
    return CALL_API(input);
}
// export const GetSubStatusV2 = (StatusId, ProductId, leadsourceId, RoleId, UserId) => {
//     let input = {};
//     let productIds = GetSubStatusV2Products();
//     let isCoreApi = false;
//     if (Array.isArray(productIds) && productIds.indexOf(ProductId) > -1) {
//         input = {
//             url: `coremrs/api/LeadDetails/GetSubStatus/${StatusId}/${ProductId}/${leadsourceId}/${RoleId}`,
//             method: 'GET',
//             service: 'MatrixCoreAPI',
//             timeout: 3000
//         };
//         isCoreApi = true;
//     }
//     else {
//         input = {
//             url: 'LeadDetails/GetSubStatusV2', method: 'POST', service: 'core',
//             requestData: {
//                 "StatusId": StatusId,
//                 "LeadSourceId": leadsourceId,
//                 "ProductId": ProductId,
//                 "roleId": RoleId,
//                 "userId": UserId
//             }
//         }
//     }
//     return GetSubStatus(input, isCoreApi);
// }

export const GetSubStatusV2 = (StatusId, ProductId, leadsourceId, RoleId, UserId) => {
    let input = {
        url: `coremrs/api/LeadDetails/GetSubStatus/${StatusId}/${ProductId}/${leadsourceId}/${RoleId}`,
        method: 'GET',
        service: 'MatrixCoreAPI',
        timeout: 3000
    };
    return GetSubStatus(input, true);
}

const GetSubStatus = (input, isCoreApi = true) => {
    return CALL_API(input).then((response) => {
        let result;
        if (isCoreApi && response && response.Data && Array.isArray(response.Data))
            result = response.Data;
        else if (response && response.hasOwnProperty('GetSubStatusV2Result') && Array.isArray(response.GetSubStatusV2Result.Data))
            result = response.GetSubStatusV2Result.Data;

        return result;
    });
}

// export const GetRejectLeadsInput = (LeadIds, LeadSubStatus, productId, UserId, futureProspectdate, RejectAll, ReasonData) => {
//     let input = {};
//     let productIds = GetSubStatusV2Products();
//     let isCoreApi = false;
//     if (Array.isArray(productIds) && productIds.indexOf(productId) > -1) {
//         var request = {
//             Leads: LeadIds,
//             SubStatusId: LeadSubStatus,
//             ProductId: productId,
//             UserID: 0,
//             FutureProsDate: futureProspectdate,
//             RejectAllleads: RejectAll === true ? 1 : 0,
//             ReasonData: ReasonData
//         };
//         input = {
//             url: `coremrs/api/LeadDetails/RejectLeads`,
//             method: 'POST',
//             service: 'MatrixCoreAPI',
//             requestData: request,
//             timeout: 3000
//         }

//         isCoreApi = true;
//     }
//     else {
//         input = {
//             url: 'LeadDetails/RejectLeads', method: 'POST', service: 'core', timeout: 'm',
//             requestData:
//             {
//                 "Leads": LeadIds,
//                 "SubStatusId": LeadSubStatus,
//                 "ProductId": productId,
//                 "UserID": UserId,
//                 "FutureProsDate": futureProspectdate,
//                 "rejectAllleads": RejectAll === true ? 1 : 0,
//                 "ReasonData": ReasonData
//             }
//         }
//     }
//     return { input, isCoreApi: isCoreApi };
// }

export const GetRejectLeadsInput = (LeadIds, LeadSubStatus, productId, UserId, futureProspectdate, RejectAll, ReasonData) => {
    var request = {
        Leads: LeadIds,
        SubStatusId: LeadSubStatus,
        ProductId: productId,
        UserID: 0,
        FutureProsDate: futureProspectdate,
        RejectAllleads: RejectAll === true ? 1 : 0,
        ReasonData: ReasonData
    };
    let input = {
        url: `coremrs/api/LeadDetails/RejectLeads`,
        method: 'POST',
        service: 'MatrixCoreAPI',
        requestData: request,
        timeout: 3000
    }
    return { input, isCoreApi: true };
}

export const GetRejectLeads = (input, isCoreApi = true) => {
    return CALL_API(input).then((response) => {
        let result;
        if (isCoreApi && response)
            result = response;
        else if (response && response.hasOwnProperty("RejectLeadsResult"))
            result = response.RejectLeadsResult;

        return result;
    });
}

export const GetSubStatusV2Products = () => {
    let productIds = [];
    productIds = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.GetSubStatusV2CoreApi) || SV_CONFIG.GetSubStatusV2CoreApi;
    return productIds;
}

export const UpdateCallStatusService = (LeadID, Status, UserId) => {
    if (!UserId || !LeadID) return;
    let service = "MatrixCoreAPI";
    let url = `onelead/api/Communication/updatecallstatus?leadid=${LeadID}&status=${Status}&userid=${UserId}`

    const input = { url, service };
    return CALL_API(input)
}

export const connectCallSFservice = (lead, IsAttempts, source, useAswatPhone) => {
    let apiCallTime = "";
    if (localStorage.getItem('serverdate') != null) {
        apiCallTime = localStorage.getItem('serverdate');
    }
    let baseUrl = 'onelead/api/communication/ConnectCallSF/'
    let service = 'MatrixCoreAPI'

    let url = baseUrl
        + lead.LeadId + '/' + User.UserId + '/' + lead.CustomerId
        + '?attempt=' + IsAttempts
        + '&roleId=' + User.RoleId
        + '&apiCallTime=' + apiCallTime
        + '&PriorityReasonId=' + lead.ReasonId;


    if (User.Asterisk_IP) { url += '&AsteriskIP=' + User.Asterisk_IP }
    if (source) { url += '&source=' + source; }
    if (useAswatPhone) { url += '&callOnAswat=true'; }

    const input = {
        url, method: 'GET', service
    };

    return CALL_API(input);
}

export const GetCustomerVisitTrailsService = (parentLeadId) => {
    let input = {
        url: 'coremrs/api/MRSCore/GetCustomerVisitTrail', method: 'POST', service: 'MatrixCoreAPI',
        requestData: { LeadId: parentLeadId }
    };
    return CALL_API(input)
}
export const GetCustomerPitchedIntentService = (leadforSummary) => {

    const requestData = { lead_id: leadforSummary }
    const input = {
        url: `api/SalesView/LeadSummaryAI`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData
    };

    return CALL_API(input)
}

export const isShowCustomerPitch = () => {
    const grps = Array.isArray(SV_CONFIG.ShowCustomerPitchGrps) ? SV_CONFIG.ShowCustomerPitchGrps : [];
    const products = Array.isArray(SV_CONFIG.ShowCustomerPitchPrds) ? SV_CONFIG.ShowCustomerPitchPrds : [];

    let flag = false;
    if (SV_CONFIG.ShowCustomerPitch || checkUserGroup(grps) || products.includes(rootScopeService.getProductId())) {
        flag = true;
    }
    return flag;
}
export const saveIntentFeedbackService = (requestData) => {
    const input = {
        url: `api/SalesView/SaveFeedbackV2`,
        method: 'POST', service: 'MatrixCoreAPI', requestData
    };

    return CALL_API(input)
}

export const saveSummaryFeedbackService = (requestData) => {
    const input = {
        url: `api/Salesview/SaveSummaryFeedbackAI`,
        method: 'POST', service: 'MatrixCoreAPI', requestData
    }

    return CALL_API(input)
}

export const IsAswatUser = () => {
    return ["ASWAT", "ASWATINDIA"].includes(User.CallingCompany);
}
export const IsHangupEnable = (durationTime) => {
    var result = true;
    if (parseInt(rootScopeService.getProductId()) == 106 && parseInt(durationTime) <= 30) {
        result = false;
    }
    return result;
}

export const getScriptURLs = () => {
    let Ispriority = rootScopeService.getPriority();
    let version = 102;
    let _urls = [];
    if (CONFIG.BUILD_ENV === 'live') {
        _urls.push(CONFIG.PUBLIC_URL + "/libs/chat/live/main.js?c=" + version);
        _urls.push("/pgv/Content/Js/AsterickDialer.js");
    }
    else {
        _urls.push(CONFIG.PUBLIC_URL + "/libs/chat/qa/main.js?c=" + version);
    }
    if (IsAswatUser()) {
        _urls.push(CONFIG.PUBLIC_URL + "/libs/swap-webphone/swapAgentCall.js?c=" + version);
        // _urls.push(CONFIG.PUBLIC_URL + "/libs/swapWebphone/webphoneControl.js?c=" + version)
    }
    else
        if (Ispriority
            // && User.IsProgressive
        ) {
            _urls.push("../softphone/AgentCall.js?c=" + version);
        }
    return _urls;
    // setUrls(_urls);
}

export const showWebphoneWindow = () => {
    window.ShowCallWindow(User.EmployeeId);
}
export const IsCustomerIntentFOS = (AllLeads) => {
    let IsCustomerIntent = false;
    let FOSCustIntentUtmMediums = SV_CONFIG.FOSCustIntentUtmMediums
    Array.isArray(AllLeads) && Array.isArray(FOSCustIntentUtmMediums) && AllLeads.forEach(lead => {
        if ((lead.UTM_Medium && FOSCustIntentUtmMediums.indexOf(lead.UTM_Medium) > -1)) {
            IsCustomerIntent = true;
            return;
        }
    })
    return IsCustomerIntent;
}
export const OpenLeadContentOnClick = (leadId, CustomerID, ProductId) => {
    try {
        let leadContentString = CustomerID + '/' + ProductId + '/' + leadId + '//' + User.UserId  //atob('10576214/2/44453246//91106')
        const baseUrl = CONFIG.PUBLIC_URL + "/leadview/";
        let leadContentUrl = baseUrl + btoa(leadContentString);
        window.open(leadContentUrl);
    }
    catch (ex) {
        console.log(ex.message);
    }
};

export const OpenReadOnlyViewMatrix = (leadId, CustomerID, ProductId) => {
    try {
        let leadContentString = CustomerID + '/' + ProductId + '/' + leadId + '//' + User.UserId  //atob('10576214/2/44453246//91106')
        const baseUrl = CONFIG.PUBLIC_URL + "/leadcontent/";
        let leadContentUrl = baseUrl + btoa(leadContentString);
        window.open(leadContentUrl);
    }
    catch (ex) {
        console.log(ex.message);
    }
};

export const isLeadAssignedToUser = (ParentLeadId) => {
    let _LstAgentLeads = getSearchObjFromLS();
    let isAssigned = false;
    _LstAgentLeads.forEach(lead => {
        if (lead && lead.LeadID === ParentLeadId) {
            isAssigned = true;
        }
    });
    return isAssigned;
}
export const GeneratePGLink = (requestData) => {
    const input = {
        url: `api/HealthRenewal/GeneratePGLink`,
        method: "POST",
        service: "MatrixCoreAPI",
        requestData
    };
    return CALL_API(input)

}

export const checkLeadAssignmentDetails = (LeadId) => {
    const input = {
        url: `api/LeadDetails/checkLeadAssignmentDetails?LeadId=${LeadId}`,
        method: "GET",
        service: "MatrixCoreAPI"
    };
    return CALL_API(input)

}
export const GetClaimDetailsInbound = (requestData) => {
    const input = {
        url: `api/Salesview/GetClaimDetailsInbound`,
        method: "POST",
        requestData,
        service: 'MatrixCoreAPI',
    }
    return CALL_API(input);
}


export const GetEmailMobileStatusService = (requestData) => {

    const input = {
        url: `api/SalesView/GetEmailMobileStatus`,
        method: "POST",
        service: "MatrixCoreAPI",
        requestData
    }
    return CALL_API(input);
}
export const GetAppointmentFeedbackData = () => {
    const input = {
        url: "api/SalesView/GetFeedBackMaster",
        method: 'GET',
        service: 'MatrixCoreAPI',
    }
    return CALL_API(input).then(function (response) {
        return response;
    });
}

export const saveAppointmentFeedbackData = (requestData) => {
    const input = {
        url: `api/SalesView/SaveFOSFeedback`,
        method: 'POST', service: 'MatrixCoreAPI', requestData
    };

    return CALL_API(input)
}

export const IsApptfeedbackLead = () => {
    return (rootScopeService.getProductId() == 220)

}

export const matrixLogout = (timeout = 3000, logoutTypeId = 0, reason = "") => {
    // logout and show alert
    setTimeout(function () {
        if (logoutTypeId) {
            window.location = `/pgv/Leads/BlankLogout.aspx?logoutSV=1&logoutTypeId=${logoutTypeId}`;
        } else {
            window.location = "/pgv/LogIn.aspx";
        }
    }, timeout);
    try {
        if (reason) {
            alert(reason);
        }
    } catch (e) { }
}

export const getTotalTalktime = (parentLeadId, Talktime) => {

    let durationTime = getCurrentTalkTime();
    if (durationTime >= Talktime) {
        return true;
    }
    else {
        let HistoryTalktime = 0;
        GetCallDetails(parentLeadId).then(function (resultData) {
            if (resultData != null && !resultData.isError) {

                HistoryTalktime = parseInt(resultData.HistoryTalktime) || 0;
                if (HistoryTalktime + durationTime >= Talktime) {
                    return true;
                }
            }
        }).catch((error) => {
            return false;
        });
    }
    return false;
}

export const SaveCallIntent = (requestData) => {
    const input = {
        url: `api/SalesView/SaveCallIntent`,
        method: 'POST',
        service: 'MatrixCoreAPI',
        requestData
    }
    return CALL_API(input)
}

export const GetESQuickQuotesForPortV3 = (LeadId) => {
    const input = {
        url: `api/HealthRenewal/GetESQuickQuotesForPortV3/${LeadId}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}

export const GetCustAddress = (CustomerId) => {
    const input = {
        url: `api/SalesView/GetCustAddress/${CustomerId}`,
        method: "GET",
        service: "MatrixCoreAPI",
    };
    return CALL_API(input)
}

export const GetSmeAnualOpenLead = (CustomerId) => {
    const input = {
        url: `coremrs/api/LeadDetails/GetSmeAnualOpenLead/${CustomerId}`,
        method: "GET",
        service: "MatrixCoreAPI",
    };
    return CALL_API(input)
}

export const ExecuteReOpenLeadV2 = (requestData) => {
    const input = {
        url: `coremrs/api/MRSCore/ReOpenMatrixLead`,
        method: "POST",
        service: "MatrixCoreAPI",
        requestData
    };
    return CALL_API(input)
}

export const GetPortSelectionURL = (requestData) => {
    const input = {
        url: `api/HealthRenewal/GetPortSelectionURL`,
        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 's',
        requestData
    }
    return CALL_API(input);
}

export const GetLeadReferralDetails = (LeadId,type) => {
    const input = {
        url: `coremrs/api/LeadDetails/GetLeadAdditionalDetails/${LeadId}/${type}`,
        method: "GET",
        service: "MatrixCoreAPI"
    }
    return CALL_API(input);
}

export const IsValidUserGroup = function (groupIds, roleIds) {
    let result = false;
    try {
        if (Array.isArray(groupIds) && Array.isArray(roleIds) && roleIds.indexOf(User.RoleId) > -1) {
            let groupList = User.UserGroupList;
            for (var i = 0, len = groupList.length; i < len; i++) {
                if (groupIds.indexOf(groupList[i].GroupId) > -1) {
                    result = true;
                    break;
                }
            }
        }
    }
    catch { }
    return result;
};
  
export const getCheckForNoCallingLead = (allLeads, parentLeadId) => {
    try{
         // Check if allLeads is null or undefined
         if (!Array.isArray(allLeads) || allLeads?.length === 0) {
            return false;
        }
        // Find the lead details from leadCollection
        const leadDetails = allLeads?.filter((e) => e.LeadID == parentLeadId)[0];

        // Extract utmSource and leadSource
        const utmSource = leadDetails?.Utm_source?.toLowerCase() || ""; // Safely convert to lowercase
        const leadSource = leadDetails?.LeadSource?.toLowerCase() || ""; // Safely convert to lowercase

        // Check conditions for showing an error
        if (utmSource === "medicalassociation" && leadSource === "externalsources") {
            enqueueSnackbar("Manual Calling is not allowed for this lead.", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return true; // Prevent further execution
        }
    }
    catch{

    }
    return false;
}

export const UpdateCallableNumber = (requestData) => {
    const input = {
        url: `api/salesview/UpdateCallableNumber`,
        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 'm',
        requestData
    }
    return CALL_API(input);
};

export const GetIssuedPolicies = (CustomerID, ProductID) => {
    const input = {
        url: `coremrs/api/MRSCore/GetCustPolicies/${CustomerID}/${ProductID}/4`,
        method: 'GET', service: 'MatrixCoreAPI',
        timeout: 2000
    }
    return CALL_API(input);
}

export const GetShopseEligibleOffers = (MobileNo, Amount, IsExistMobileNo, LeadID) => {
    const input = {
        url: `external/api/HealthRenewalExternal/GetShopseEligibleOffers/${Amount}/${MobileNo}/${IsExistMobileNo}/${LeadID}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}

export const GetEncryptedMobileNo = (LeadID) => {
    const input = {
        url: `external/api/HealthRenewalExternal/GetEncryptedMobileNo/${LeadID}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}

export const SetLeadAudit = (requestData) => {
    const input = {
        url: `coremrs/api/LeadDetails/SetLeadAudit`,
        method: 'POST',
        service: 'MatrixCoreAPI',
        timeout: 's',
        requestData
    }
    return CALL_API(input);
}


export const GetAxioEligibleOffers = (requestData) => {
    const input = {
        url: `external/api/HealthRenewalExternal/GetAxioEligibleOffers`,
        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 'l',
        requestData
    }
    return CALL_API(input);
}

export const GetVirtualNumberListService = (LeadID) => {
    const input = {
        url: "api/SalesView/GetVirtualNumberList/" + "?LeadId=" + LeadID,
        method: 'GET',
        service: 'MatrixCoreAPI',
    }
    return CALL_API(input)
}

export const IsUnfreeze = (LeadId) => {
    const input = {
        url: `api/LeadDetails/IsUnfreezeCjURL/${LeadId}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}

export const GetPaymentAttemptStatus = (requestData) => {
    const input = {
        url: `api/SalesView/GetPaymentAttemptStatus`,
        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 's',
        requestData
    }
    return CALL_API(input);
}
export const GetHealthRenewalContinueLink = (leadid, productId, Supplier, leadSource) => {
    const _input = {
        url: 'api/UpdateRenewalLeads/GetCJUrl?LeadID=' + leadid + '&ProductId=' + productId + '&SupplierId=' + Supplier + '&LeadSource=' + leadSource,
        method: 'GET',
        service: 'MatrixCoreAPI'
    }
    return CALL_API(_input);
  }

export const GetClaimStatus = (CustomerID, ProductId) => {
    const input = {
        url: "coremrs/api/ProposalService/GetClaimStatus/" + CustomerID + "/" + ProductId ,
        method: 'GET',
        service: 'MatrixCoreAPI',
    }
    return CALL_API(input)
}

export const GetLeadCustomerDetails = (leadid) => {
    const input = {
                url: `api/SalesView/GetLeadCustomerDetails/${leadid}`,
                method: 'GET', service: 'MatrixCoreAPI',
            };
    return CALL_API(input);
}

export const AssignLeadToGroup = (requestData) => {

    const input = {
        url: `coremrs/api/MRSCore/AssignLeadToGroup`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData
    }
    return CALL_API(input)

}

export const getBookingIdsFromCustomerId = async (requestData) => {
    const input = {
        url: `api/SalesView/GetCustomerBookingsByCustomerId`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData
    }
    return CALL_API(input)
}

export const getSalesToClaimCallTransferDetails = async (requestData) => {
    const input = {
        url: `api/SalesView/GetClaimTransferInfoOrScheduleCB`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData
    }
    return CALL_API(input)
}

export const getBookingDetailsByLeadIdCRT = async (requestData) => {
    const input = {
        url: `api/SalesView/GetBookingInfoByLeadIdCRT`,
        method: 'POST', service: 'MatrixCoreAPI',
        requestData
    }

    return CALL_API(input);
}

export const getCustomerOpenTicketCount = () => {

    const customerId = rootScopeService.getCustomerId();
    const input = {
        service: 'MatrixCoreAPI',
        url: `api/Ticket/GetTicketCount/${customerId}`,
        method: 'GET',
        timeout: 2000
    };

    return CALL_API(input);
}

export const getAssignedAgentDetailsByProductIdAndCustomerId = (requestData) => {
    const input = {
        service: 'MatrixCoreAPI',
        url: `api/SalesView/GetAssignedAgentDetails`,
        method: 'POST',
        requestData,
    };

    return CALL_API(input);
}

export const GetCustomerOpenLeads = (requestData) => {
    const url = `api/SME/GetCustomerOpenLeads?CustomerId=${requestData.CustomerId}&SubProductId=${requestData.SubProductId}`;
    const input = {
        url: url,
        method: 'GET',
        service: 'MatrixCoreAPI'
    };
    return CALL_API(input);
}

export const FetchUserPaymentRequest = () => {
    const _input = {
        url: `coremrs/api/ProposalService/FetchPendingPaymentRequest`,
        method: 'GET',
        service: 'MatrixCoreAPI',
        timeout: 's'
    }
    return CALL_API(_input);
}

export const FetchPaymentRequestDetails = (requestID) => {
    const _input = {
        url: `coremrs/api/ProposalService/FetchPaymentRequestDetails/${requestID}`,
        method: 'GET',
        service: 'MatrixCoreAPI',
        timeout: 's'
    }
    return CALL_API(_input);
}

export const InsertRenewalPaymentDetails = (requestData) => {
    const _input = {
        url: `coremrs/api/ProposalService/InsertRenewalPaymentDetails`,
        method: 'POST',
        service: 'RenewalPaymentUploadFileToUrl',
        timeout: 's',
        requestData
    }
    return CALL_API(_input);
}

export const FetchAgentAssist = (LeadID) => {
    const input = {
        url: `coremrs/api/HealthRenewal/FetchAgentAssist?LeadID=${LeadID}`,
        method: 'GET', service: 'MatrixCoreAPI'
    }
    return CALL_API(input)
}

export const FetchHealthRenewalNeedAnalysis = (LeadID) => {
    const _input = {
        url: `coremrs/api/HealthRenewal/FetchHealthRenewalNeedAnalysis/${LeadID}`,
        method: 'GET',
        timeout: 's',
        service: 'MatrixCoreAPI'
        }
    return CALL_API(_input);
}

export const SetHealthRenewalNeedAnalysis = (requestData) => {
    const input = {
        service: 'MatrixCoreAPI',
        url: `coremrs/api/HealthRenewal/SetHealthRenewalNeedAnalysis`,
        method: 'POST',
        requestData,
    };

    return CALL_API(input);
}

export const getBrandNewCarTLTransferDetails = (leadId) => {
    const input = {
        url: `coremrs/api/MRSCore/GetBrandNewCarTLInfo?leadId=${leadId}`,
        method: 'GET', service: 'MatrixCoreAPI',
    }
    return CALL_API(input)
}

export const SendShopseNTB = (requestData) => {
    const input = {
        url: `external/api/HealthRenewalExternal/SendShopseNTB`,
        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 'l',
        requestData
    }
    return CALL_API(input);
}


export const GetEncryptedEmailID = (LeadID) => {
    const input = {
        url: `external/api/HealthRenewalExternal/GetEncryptedEmailID/${LeadID}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}


export const FetchShopseFormDetails = (LeadID) => {
    const input = {
        url: `external/api/HealthRenewalExternal/FetchShopseFormDetails/${LeadID}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}

export const GetShopseQuickEligibility = (ShopseEligibilityId) => {
    const input = {
        url: `external/api/HealthRenewalExternal/GetShopseQuickEligibility/${ShopseEligibilityId}`,
        method: "GET",
        service: "MatrixCoreAPI",
        timeout: 's'
    }
    return CALL_API(input);
}