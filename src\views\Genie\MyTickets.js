import React, { useEffect, useState } from "react";
import User from "../../services/user.service";
import { useDispatch, useSelector } from "react-redux";
import { updateStateInReduxGenie } from "../../store/actions";
import { CustomerIssuesDashboard } from "../../layouts/SV/components/Sidebar/components/Modals/CustomerIssuesDashboard";
import { GetRecentCustTicketByAgentID, GetRecentTicketByAgentID } from "../../services/GenieService";
import { Badge, Box, Link, Typography } from "@mui/material";
import RecentTickets from "./RecentTickets";
import dayjs from "dayjs";
import EscalatedTickets from "./EscalatedTickets";
import { ticketDetailsTab, showEscalation } from "./GenieHelper";
import GenieNotification from "./GenieNotification";
import { localStorageCache } from "../../utils/utility";
import { useInterval } from "../SV/Main/helpers/useInterval";

const MyTickets = () => {

    const dispatch = useDispatch();
    const [ showTab, setShowTab ] = useState(ticketDetailsTab.MYTICKET);
    const [ openMyTicketPopup, setOpenMyTicketPopup ] = useState(false);
    const refreshMyTckt = useSelector(state => state.genie.refreshMyTckt);
    const tempRecentTicket = useSelector(state => state.genie.tempRecentTicket);

    useEffect(() => {
        if (refreshMyTckt) {
            Promise.all([
                GetRecentTicketByAgentID(),
                GetRecentCustTicketByAgentID({
                    StartDate:  new Date(dayjs().subtract(30, 'day').format('YYYY-MM-DD 23:59:59')).valueOf(),
                    EndDate: new Date(dayjs().format('YYYY-MM-DD 23:59:59')).valueOf(),
                    Type: 1,
                    UserName: User.UserName,
                    EmployeeID: User.EmployeeId 
                })
            ])
            .then(([result1, result2]) => {
                let mergedData = [];

                if(Array.isArray(result1) && result1.length > 0) {
                    const escalatedTicket = result1.filter(item => item.SourceID === 11)
                    .map(item => ({                         
                        ...item,
                        className: "MatrixTicket"
                    }));
    
                    const sortedEscalated = escalatedTicket.sort((a, b) => new Date(b.CreatedOn) - new Date(a.CreatedOn));
    
                    dispatch(updateStateInReduxGenie({ escalatedTicket: sortedEscalated }));
    
                    mergedData = result1.map(item => ({
                        ...item,
                        className: "MatrixTicket",
                        BookingID: 0,
                        IssueSubIssueName: "",
                        ParentTicketDetailsID: ""
                    }));
                }
    
                if(result2 && Array.isArray(result2.Data) && result2.Data.length > 0) {
                    const formattedResult2 = result2.Data.map(item => ({
                        TicketID: item.TicketID,
                        TicketDisplayID: item.TicketDetailsID,
                        CreatedOn: new Date(item.CreatedOn).toISOString(),
                        UpdatedOn: new Date(item.UpdatedOn).toISOString(),
                        StatusName: item.Status,
                        BookingID: item.BookingID,
                        TicketURL: item.TicketURL,
                        IssueSubIssueName: item.IssueName,
                        className: "CustomerRelated",
                        Title: "Related to customer",
                        ProductName: item.ProductName,
                        escalationAllowed: item.escalationAllowed,
                        escalationNotAllowedReason: item.escalationNotAllowedReason,
                        APE: item["APE"],
                        ProductId: item.ProductId,
                        ConfirmationPending: item.ConfirmationPending,
                        ParentTicketDetailsID: item.ParentTicketDetailsID,
                        ParentPrevObjectID: item.ParentPrevObjectID
                    }));
    
                    mergedData = [...mergedData, ...formattedResult2];
                }
    
                if(mergedData.length > 0) {
                    const sortedData = mergedData.sort((a, b) => new Date(b.CreatedOn) - new Date(a.CreatedOn));
                
                    const topSix = sortedData.slice(0, 6);
                    
                    dispatch(updateStateInReduxGenie({ tempRecentTicket: topSix, refreshMyTckt: false }));
                } else {
                    dispatch(updateStateInReduxGenie({ tempRecentTicket: [], refreshMyTckt: false}));
                }
            })
            .catch (error => {
                dispatch(updateStateInReduxGenie({ tempRecentTicket: [] }));
            });
        }
    },[refreshMyTckt])


    const newNotification = () => {
        const UnreadNotifData = localStorageCache.readFromCache('MongoNotificationData')? JSON.parse(localStorageCache.readFromCache('MongoNotificationData')).filter(item => item.type.includes("genie") && item.IsRead !== true) : [];

        if (tempRecentTicket.length > 0) {
            const notificationMap = new Map();
            UnreadNotifData.forEach(item => {
                const ticketDisplayId = item.event ? item.event.split("_")[0] : "";
                notificationMap.set(ticketDisplayId, {
                    text: item.text,
                    id: item.id
                });
            });

            const updatedData = tempRecentTicket.map(ticket => ({
                ...ticket,
                showNotification: notificationMap.has(ticket.TicketDisplayID),
                notificationText: notificationMap.get(ticket.TicketDisplayID)?.text || "",
                notificationId: notificationMap.get(ticket.TicketDisplayID)?.id || null
            }));

            dispatch(updateStateInReduxGenie({ recentTicket: updatedData, showRecentTickets: true }));
        } else {
            dispatch(updateStateInReduxGenie({ recentTicket: [], showRecentTickets: true }));
        }
    };

    useInterval(() => {
        newNotification();
    },1000)

    const GetUnreadNotificationCount = () => {
        const UnreadNotifData = localStorageCache.readFromCache('MongoNotificationData') 
            ? JSON.parse(localStorageCache.readFromCache('MongoNotificationData'))
            .filter(item => item.type.includes("genie") && item.IsRead !== true) 
            : [];

        return UnreadNotifData.length;
    }

    return <>
        <Box className="ptTop myTicket">
            <Box className="MyTicketHeading">
                <Typography variant="h6" fontWeight="bold">
                    {showTab}
                </Typography>
                <Box gap={5} className="DFlex">
                    <Link 
                        underline="hover" 
                        onClick={() => {
                            showTab === ticketDetailsTab.NOTIFICATION 
                                ? setShowTab(ticketDetailsTab.MYTICKET) 
                                : setShowTab(ticketDetailsTab.NOTIFICATION)
                        }}
                    >
                        {showTab === ticketDetailsTab.NOTIFICATION ? "Hide" : "" } Notifications
                        <Badge badgeContent={GetUnreadNotificationCount()} color="error" sx={{ ml: 0.5 }} />
                    </Link>
                    {showEscalation() && 
                        <Link 
                            underline="hover" 
                            color="inherit" 
                            fontWeight="bold"
                            onClick={() => {
                                showTab === ticketDetailsTab.ESCALATEDTICKET 
                                    ? setShowTab(ticketDetailsTab.MYTICKET) 
                                    : setShowTab(ticketDetailsTab.ESCALATEDTICKET)
                            }}
                        >
                            View {showTab === ticketDetailsTab.ESCALATEDTICKET ? "recent" : "escalated" } tickets
                        </Link>
                    }
                </Box>
            </Box>
            {showTab === ticketDetailsTab.MYTICKET && 
                <RecentTickets setOpenMyTicketPopup={setOpenMyTicketPopup} />
            }
            {showTab === ticketDetailsTab.ESCALATEDTICKET &&
                <EscalatedTickets />
            }
            {showTab === ticketDetailsTab.NOTIFICATION &&
                <GenieNotification/>
            }
        </Box>
        {openMyTicketPopup &&
            <CustomerIssuesDashboard open={openMyTicketPopup} handleClose={() => { setOpenMyTicketPopup(false) }} />
        }
    </>
}

export default MyTickets;