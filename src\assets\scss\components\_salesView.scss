:root {
  --background-color: #eff3f6;
  --button-color: #ffffff;
  --box-color: #0065ff;
  --white-color: #fff;
}

$base-color: var(--background-color);
$base-button-color: var(--button-color);
$base-box-color: var(--box-color);
$base-white-color: var(--white-color);

body {
  margin: 0px !important;
  font-family: "Roboto" !important;
  background-color: #eff3f6;
}

button {
  text-transform: capitalize !important;
}

.nodataFound {
  padding: 10px 34px 20px;
  text-align: center;
  font-size: 18px !important;
  color: #546e7a !important;
}


.centerAlign {
  position: absolute;
  top: 0px;
  bottom: 0px;
  height: 26px;
  margin: auto;
  left: 0px;
  right: 0px;
  text-align: center;
  color: #dd2c2c;
  font: normal normal 500 17px/24px Roboto;
  letter-spacing: 0.22px;
}


.Headingstyle {
  text-align: center;
  font-size: 20px;
  padding-top: 10px;
  padding-bottom: 10px;
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(0);
  }
}

/* Change autocomplete styles in WebKit */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-text-fill-color: #808080;
  -webkit-box-shadow: 0 0 0px 1000px #fff inset;
  transition: background-color 5000s ease-in-out 0s;
}

.MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] .MuiAutocomplete-input {
  padding: 4.5px 4px !important;
}

.MuiOutlinedInput-root {
  position: relative;
  border-radius: 4px;
  height: 42px !important;
}

.MuiOutlinedInput-notchedOutline {
  border-color: #808080 !important;
}

.MuiFormLabel-root {
  font: normal normal normal 14px/24px Roboto !important;
  letter-spacing: 0.22px !important;
  color: #808080 !important;
  opacity: 1;
}

.MuiSelect-select:focus {
  -webkit-text-fill-color: #808080;
  -webkit-box-shadow: 0 0 0px 1000px #fff inset;
  transition: background-color 5000s ease-in-out 0s;
}

.MuiInputLabel-outlined {
  color: #808080 !important;

}

.MuiMenuItem-root {
  font: normal normal normal 14px/20px Roboto !important;
  letter-spacing: 0.22px !important;
  color: #808080 !important;
  opacity: 1;
  white-space: break-spaces;
  border-bottom: 1px solid #f8f8f8b5;
  word-wrap: break-word;
}

.Widthoccupancy {
  width: 328px;
}

.MuiNativeSelect-select,
.MuiSelect-select {
  font: normal normal normal 14px/24px Roboto !important;
  letter-spacing: 0.22px !important;
  color: #808080 !important;
  opacity: 1;
}

.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #808080 !important;
}

.MuiOutlinedInput-input {
  padding: 11.5px 14px;
  font: normal normal normal 14px/24px Roboto !important;
  letter-spacing: 0.22px !important;
  color: #808080 !important;
  opacity: 1;
}

.MuiOutlinedInput-inputMarginDense {
  padding-top: 11.5px !important;
  padding-bottom: 11.5px !important;
}

.MuiInputLabel-outlined.MuiInputLabel-marginDense {
  transform: translate(14px, 9px) scale(1) !important;
  z-index: 0;
}

.MuiInputLabel-outlined.MuiInputLabel-shrink {
  transform: translate(12px, -8px) scale(0.75) !important;

}

.MuiDialogContent-root {
  padding: 12px 24px !important;
}

/*autocomplete css */
.MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] {
  // padding: 0px;
  // padding-right: 0px !important;

  .MuiAutocomplete-endAdornment {
    .MuiIconButton-label {
      svg {
        display: none;
      }
    }
  }
}

.MuiInputLabel-outlined {
  z-index: 1;
  transform: translate(14px, 9px) scale(1) !important;
  pointer-events: none;
}

.AutoComplete {
  .MuiOutlinedInput-root {
    height: auto !important;
  }

  .MuiChip-filled {
    background-color: transparent;
    max-width: 60%; //calc(100% - 25px);

    svg {
      display: none;
    }
  }
}

.AutoCompleteData {
  font: normal normal normal 14px/20px Roboto !important;
  letter-spacing: 0.22px !important;
  color: #808080 !important;
  opacity: 1;
  //white-space: break-spaces;
  border-bottom: 1px solid rgba(248, 248, 248, 0.7098039216);
  word-wrap: break-word;
  display: flex;
  align-items: center;

  .Mui-checked {
    color: #0065FF;
  }

}

/*end autocomplete css*/
.selesviewUI {
  display: flex;
  width: 100%;
  background-color: $base-color;

  .expandmoreIcon {
    float: right;
    position: absolute;
    top: 16px;
    right: 18px;

    svg {
      color: #0065ff;
    }

    &.expandarrow {
      top: 311px;
    }
  }

  .hovermenu {
    background: #fff 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 16px #00000029;
    width: 190px;
    z-index: 100000000;
    position: fixed;
    border-radius: 18px;
    top: 235px;
    font-size: 14px;
    left: 76px;

    ul {
      list-style-type: none;
      padding: 20px;

      li {
        line-height: 40px;

        a {
          font-size: 14px;
          font: normal normal normal 14px/24px Roboto;
          letter-spacing: 0.22px;
          color: #253858;
        }
      }
    }
  }

  .hovermenu::after {
    content: "";
    content: "";
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    position: absolute;
    border-right: 18px solid white;
    border-bottom: 10px solid transparent;
    left: -18px;
    bottom: 61px;
  }

  .leftmenu {
    .MuiDrawer-paperAnchorDockedLeft {
      border-right: none;
      width: 83px;
      background-color: $base-color;
    }

    .MuiDrawer-docked {
      width: 83px;
    }

    .activeIcon {
      fill: #0065ff;
      stroke: #0065ff;
    }

    // .MuiListItem-root:nth-last-child(2) {
    //   margin-top: 7em;
    // }
    // .speedTestResult {
    //   background: transparent linear-gradient(105deg, #6bd894 0%, #59e6c0 100%)
    //     0% 0% no-repeat padding-box;
    //   border-radius: 4px;
    //   font: normal normal 10px/21px Roboto;
    //   letter-spacing: 0.14px;
    //   color: #ffffff;
    // }

    .menuicon {
      .right-0 {
        right: 0px;
      }

      .iconbox {
        margin: auto;
        background-color: #fff;
        border-radius: 50%;
        width: 42px;
        height: 42px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;

        img {
          width: 19px;
        }
      }

      .NewStories {
        background: linear-gradient(148.86deg, #84FFAE -31.97%, #336EDF 125.12%) !important;

      }

      .highlight {
        // background: url("/public/images/salesview/agentstories/needattendance.gif") no-repeat;
        top: 7px;
        position: absolute;
        left: -23px;
        border-radius: 50%;
        z-index: 0;
      }

      p {
        text-align: center;
        font: normal normal normal 12px/15px Roboto;
        letter-spacing: 0px;
        color: #303030;
        opacity: 1;
        padding-top: 6px;
        white-space: pre-wrap;
      }
    }
  }

  .fade {
    width: 100%;
    height: 100%;
    position: fixed;
    background-color: #000000;
    opacity: 0.3;
    z-index: 1111;
    top: 0;
    left: 0;
    display: none;
  }

  .rightmenu {
    width: 65px;
    right: 0;
    position: fixed;
    top: 1%;
    background-color: transparent;
    border-radius: 10px 0 0 10px;
    height: 98%;
    box-shadow: none;
    display: inline-block;
    transition: width 0.7s;
    z-index: 1111;
    overflow: hidden;
    padding-left: 20px;

    .menu {
      width: 46px;
      height: 100%;
      display: inline-block;
      position: relative;
      background-color: #fff;
      box-shadow: 0px 0px 16px #00000014;
      border-radius: 10px 0 0 10px;

      ul {
        position: absolute;
      }

      ul:first-child {
        top: 0;
      }

      ul:last-child {
        bottom: 0;
      }

      li {
        list-style: none;
        padding: 10px 3px;
        font-size: 8px;
        text-align: center;
        line-height: 9px;
        width: 45px;
        cursor: pointer;

        .rhs-icon {
          background: url("/public/images/right-icons.svg") no-repeat;
          margin: 0 auto;
          background-size: 100%;
          display: inline-block;
          width: 24px;
          height: 24px;

          &.msg {
            background-position: 2px 2px;
          }

          &.whatsapp {
            background-position: 2px -77px;
          }

          &.callback {
            background-position: 1px -154px;
          }

          &.logs {
            background-position: 1px -230px;
          }

          &.feedback {
            background-position: 2px -307px;
          }

          &.notes {
            background-position: 2px -459px;
          }

          &.tickets {
            background-position: 0px -537px;
          }

          &.checklist {
            background-position: 3px -619px;
          }
        }

        &.active {
          border-left: 2px solid #0065ff;
          color: #0065ff;

          .rhs-icon {
            &.msg {
              background-position: 2px -36px;
            }

            &.whatsapp {
              background-position: 2px -115px;
            }

            &.callback {
              background-position: 1px -192px;
            }

            &.logs {
              background-position: 1px -268px;
            }

            &.feedback {
              background-position: 2px -344px;
            }

            &.notes {
              background-position: 2px -498px;
            }

            &.tickets {
              background-position: 0px -578px;
            }

            &.checklist {
              background-position: 3px -657px;
            }
          }
        }
      }
    }

    .content {
      width: calc(100% - 46px);
      text-align: center;
      height: 100%;
      background-color: #ffffff;
      float: right;
      overflow-y: auto;

      .notesPopup {
        padding: 12px;

        textarea {
          background: #fafafa;
          border: 1px solid #eaeaea;
          border-radius: 8px;
          width: 100%;
          padding: 10px;
          height: 250px !important;
          outline: none;
        }

        button {
          background: #0065ff;
          border-radius: 8px;
          width: 90%;
          margin-top: 35px;
          letter-spacing: 0.17px;
          padding: 8px;
          border: none;
          color: #ffffff;
          font: normal normal 12px/21px Roboto;
          cursor: pointer;
          outline: none;
        }
      }

      .checklistPopup {
        padding: 20px;
        height: calc(100vh - 100px);
        overflow: auto;
        max-height: calc(100vh - 100px);

        h3 {
          font: normal normal bold 14px/16px Roboto;
          letter-spacing: 0.2px;
          color: #303030;
          text-align: left;
          margin: 1em 0px 2em 0px;
        }

        div {
          width: 100%;
          margin-bottom: 20px;
          display: inline-block;

          input[type="checkbox"] {
            position: relative;
            cursor: pointer;
            margin: 0px;
            float: left;
          }

          input[type="checkbox"]:before {
            content: "";
            display: block;
            position: absolute;
            width: 18px;
            border-radius: 10px;
            height: 18px;
            top: -3px;
            left: -3px;
            background-color: white;
            border: 1px solid #707070;
          }

          input[type="checkbox"]:checked:before {
            content: "";
            display: block;
            position: absolute;
            width: 18px;
            border: 1px solid #0065ff;
            height: 18px;
            top: -3px;
            left: -3px;
            background-color: #e7f0ff;
          }

          input[type="checkbox"]:checked:after {
            content: "";
            display: block;
            width: 8px;
            height: 16px;
            border: solid #0065ff;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
            position: absolute;
            top: -6px;
            left: 6px;
          }

          label {
            text-align: left;
            float: left;
            margin-left: 10px;
            font: normal normal normal 12px/16px Roboto;
            letter-spacing: 0.17px;
            color: #303030;
            width: 90%;
          }
        }
      }

      .checklist-save-btn {
        background: #0065ff;
        border-radius: 8px;
        width: auto;
        margin-top: 35px;
        letter-spacing: 0.17px;
        padding: 8px 40px;
        border: none;
        color: #ffffff;
        font: normal normal normal 12px/21px Roboto;
        cursor: pointer;
        outline: none;
        margin: 20px 0;
      }

      .chatLeadSection {
        padding: 12px 15px;

        h3 {
          font: normal normal 16px/24px Roboto;
          letter-spacing: 0.26px;
          color: #253858;
          text-align: left;
          margin-bottom: 15px;
        }

        .leadStatus {
          float: left;
          margin-bottom: 15px;
          width: 100%;

          .userName {
            text-align: left;
            float: left;
            font: normal normal bold 14px/19px Roboto;
            letter-spacing: 0px;
            color: #2e2e2e;
          }

          .userMsg {
            text-align: left;
            font: normal normal normal 14px/19px Roboto;
            letter-spacing: 0px;
            float: left;
            width: 100%;
            color: #2e2e2e;
          }

          .date {
            float: right;
          }
        }

        .userIcon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          float: left;
          line-height: 48px;
          background: #eaeaea;

          span {
            width: 8px;
            height: 8px;
            background: #1ddbaf 0% 0% no-repeat padding-box;
            border-radius: 10px;
          }
        }

        .userCount {
          background: #e06666;
          border-radius: 3px;
          color: #fff;
          line-height: 24px;
          text-align: center;
          width: 24px;
          height: 24px;
          margin-bottom: 10px;
          float: right;
        }

        hr {
          width: 100%;
          height: 2px;
          border: 1px solid #e2e2e280;
          margin-bottom: 10px;
        }
      }

      .whatsappChat {
        padding: 12px 15px;

        h4 {
          text-align: left;
          font: normal normal bold 14px/19px Roboto;
          letter-spacing: 0px;
          color: #2e2e2e;
          margin-bottom: 10px;
        }

        .saveButton {
          background: #0065ff;
          border-radius: 8px;
          color: #fff;
          padding: 10px;
          font: normal normal 12px Roboto;
          letter-spacing: 0.17px;
          border: none;
          outline: none;
          width: 100%;
          margin-top: 10px;
        }

        .userDetails {
          margin-bottom: 10px;

          span {
            float: left;
            font: normal normal normal 12px/21px Roboto;
            letter-spacing: 0.17px;
            color: #808080;
          }

          labal {
            font: normal normal 12px/21px Roboto;
            letter-spacing: 0.17px;
            color: #253858;
            float: right;
            font-weight: 600;
          }
        }
      }
    }

    .widthResize {
      display: block;
      position: absolute;
      color: #253858;
      left: -29px;
    }
  }

  .wrapper {
    background-color: $base-color;
    width: calc(100vw - 110px);
    padding: 0px 20px 100px 10px;

    .noleadToWork {
      text-align: center;
      margin-top: 7em;

      h3 {
        font: normal normal bold 16px/21px Roboto;
        letter-spacing: 0px;
        color: #253858;
        margin: 30px 0px 15px 0px;
      }

      p {
        font: normal normal normal 12px/16px Roboto;
        letter-spacing: 0px;
        color: #253858;
      }
    }

    .topbarLeft {
      display: flex;
      padding: 0px;
      position: fixed;
      width: calc(100% - 150px);
      z-index: 999;
      background: #ffffff 0% 0% no-repeat padding-box;
      box-shadow: 0px 3px 8px #0065ff29;
      border-radius: 9px 0px 16px 16px;
      height: 57px;
      opacity: 1;

      .leadName {
        border-right: 1px solid #b4cff9;
        margin-right: 9px;
        padding: 8px 7px 4px;

        h3 {
          font: normal normal 12px/11px Roboto;
          letter-spacing: 0.19px;
          color: #253858;
          font-weight: 600;
        }

        span {
          font: normal normal normal 10px/21px Roboto;
          letter-spacing: 0.14px;
          color: #808080;
        }
      }

      .leadName.active {
        background-color: #0065ff;

        h3 {
          color: #fff;
        }

        span {
          color: #fff;
        }
      }

      // .arrow-prev{
      //   display: none;
      // }
      // .arrow-next{
      //   display: none;
      // }
      .scroll-menu-arrow {
        svg {
          background-color: #000;
          color: #fff;
          width: 0.6em;
          height: 0.6em;
          border-radius: 12px;
        }
      }

      .DailingIcon {
        animation: 1s ease-out 1s 0 slideInFromLeft;
        margin-right: 5px;
        position: relative;
        top: 0px;
        background-color: #e8e6ff;
        padding: 11px 5px 10px 10px;
        border-radius: 8px;
        float: left;

        div {
          //for the divs created by modal (calltranser)
          display: none;
        }

        svg {
          color: #808080;
          font-size: 26px;
          margin-right: 10px;
        }

        p {
          font: normal normal normal 16px/21px Roboto;
          letter-spacing: 0px;
          color: #000;
          float: left;
          margin-right: 10px;
          min-width: 50px;

          span {
            display: block;
            background-color: #ffef29;
            font: normal normal 600 10px/13px Roboto;
            letter-spacing: 0px;
            color: #000000;
            opacity: 1;
            text-align: center;
            border-radius: 4px;
            margin-top: 0px;
            padding: 1px;
          }
        }

        .callDailing {
          background-color: #a4f1df;
          border-radius: 20px;
          padding: 7px 8px;
          position: relative;
          top: -9px;
          display: inline-flex;

          svg {
            color: #196c3f;
            margin-right: 0px;
          }
        }

        .callEnd {
          background-color: #ffd1d1;
          border-radius: 20px;
          padding: 7px 8px;
          display: inline-flex;
          position: relative;
          top: -4px;

          svg {
            color: #de350b;
            margin-right: 0px;
          }
        }

        .ConnectedLeadId {
          background: #a9f5ed 0% 0% no-repeat padding-box;
          border-radius: 8px;
          padding: 9px;

          span {
            font: normal normal 600 12px/16px Roboto;
            letter-spacing: 0px;
            color: #304964;
            opacity: 1;
            background-color: transparent;
          }
        }

        .blinkAnimation {
          animation: mymove 0.5s infinite;
        }

        @keyframes mymove {
          0% {
            background-color: #a9f5ed;
          }

          25% {
            background-color: #24d7c5;
          }

        }

        .mute {
          background: #ffffff 0% 0% no-repeat padding-box;
          box-shadow: 0px 0px 16px #00000014;
          border-radius: 8px;
          opacity: 1;
          padding: 11px 9px 7px;
          margin-right: 8px;
          float: left;
        }

        .cltransfer {
          background: #ffffff 0% 0% no-repeat padding-box;
          box-shadow: 0px 0px 16px #00000014;
          border-radius: 8px;
          opacity: 1;
          padding: 8px 9px 7px;
          margin-right: 8px;
          float: left;
          margin-left: 0px;
          height: 37px;
        }

        .cusFeedback {
          background: #ffffff 0% 0% no-repeat padding-box;
          box-shadow: 0px 0px 16px #00000014;
          border-radius: 8px;
          opacity: 1;
          padding: 6px 7px 7px 7px;
          margin-right: 8px;
          float: left;

          img {
            position: relative;
            top: 3px;
          }
        }

        .clEnd {
          background: #ffffff 0% 0% no-repeat padding-box;
          box-shadow: 0px 0px 16px #00000014;
          border-radius: 8px;
          opacity: 1;
          padding: 10px 7px 9px 8px;
          margin-right: 8px;
          float: left;

          img {
            position: relative;
            top: 0px;
            right: 1px;
          }
        }

        .DialpadIcon {
          background: #ffffff 0% 0% no-repeat padding-box;
          box-shadow: 0px 0px 16px #00000014;
          border-radius: 8px;
          opacity: 1;
          padding: 11px 5px 5px 7px;
          margin-right: 8px;
          float: left;

          img {
            position: relative;
            top: 0px;
            right: 1px;
            height: 15px;

          }
        }
      }

      .timerBtn {
        font: normal normal 14px Roboto;
        letter-spacing: 0.22px;
        color: #253858;
        padding: 2px 14px;
        font-weight: 600;
        margin-left: 0px;
        margin-right: 9px;
        display: inline-block;
        background: #eaeaea 0% 0% no-repeat padding-box;
        border: 1px solid #cecece;
        border-radius: 8px;
        opacity: 1;
        margin-top: 12px;

        svg {
          float: left;
        }

        p {
          float: left;
          padding-top: 4px;
          padding-left: 9px;
        }
      }

      .timerBtn.disabled {
        color: #cccccc;
      }

      .rightbtns {
        float: right;
        display: flex;
        padding: 0px 0px 10px 0px;

      }

      .NotificationIcon {
        text-align: center;
        width: 35px;
        border: 1.5px solid #E06666;
        margin-top: 10px;
        float: right;
        box-shadow: 0px 0px 0px 4px #FFDCDC;
        height: 35px;
        border-radius: 25px;
        margin-right: 10px;
        cursor: pointer;
        position: relative;

        img {
          position: relative;
          top: 6px;
        }

        .counter {
          top: -6px;
          right: 21px;
          font-weight: bold;
        }

        .tooltiptext {
          visibility: visible;
          border-radius: 12px;
          padding: 10px 15px;
          position: absolute;
          z-index: 1;
          height: auto;
          width: 218px;
          background: #4F83FF;
          box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1607843137);
          top: 60px;
          right: 50%;
          text-align: left;
          font: normal normal 600 14px/24px Roboto;
          letter-spacing: 0.22px;
          color: #ffffff;
          opacity: 1;
          display: flex;
          align-items: center;

        }

        .tooltiptext::after {
          content: "";
          position: absolute;
          top: -43%;
          left: 83%;
          margin-top: 0px;
          border-width: 19px;
          border-style: solid;
          border-color: transparent #4f83ff transparent transparent;
        }
      }

      .noLeadbox {
        min-width: 190px !important;
      }

      #nextFive {
        left: 13px;
        background: #c8dbf9 0% 0% no-repeat padding-box;
        border-radius: 8px;
        opacity: 1;
        float: left;
        position: relative;
        top: 5px;
        height: 46px;

        .noLead {
          text-align: center;
          font-size: 14px;
          font-weight: normal;
          font-family: "Roboto";
          margin-top: 13px;
        }

        .arrow-prev {
          position: relative;
          left: -10px;
        }

        .arrow-next {
          position: relative;
          right: -10px;
        }

        .horizontal-menu {
          position: relative !important;
          top: 0px !important;
          height: 46px;
        }

        .nextfiveLead {
          h4 {
            text-align: left;
            font: normal normal 600 11px/12px Roboto;
            letter-spacing: 0.9px;
            color: #253858;
          }

          p {
            text-align: left;
            font: normal normal bold 10px/16px Roboto;
            letter-spacing: 0.14px;
          }

          span {
            text-align: left;
            font: normal normal normal 10px/13px Roboto;
            letter-spacing: 0.08px;
            color: #303030;
            opacity: 1;
            display: block;
          }
        }

        .Connected {
          background-color: #1ddbaf66;

          h4,
          span,
          p {
            color: #196c3f;
          }
        }

        .Answered {
          background-color: #50e9c5;
        }

        .NotAnswered {
          background-color: #ffd1d1;
        }

        .CallInitiated {
          background-color: #43ffd366;
          border-radius: 4px;
          // animation: blink-animation 1s steps(5, start) infinite;
          // -webkit-animation: blink-animation 1s steps(5, start) infinite;
        }

        @keyframes blink-animation {
          to {
            visibility: hidden;
          }
        }

        @-webkit-keyframes blink-animation {
          to {
            visibility: hidden;
          }
        }
      }
    }

    .topbarRight {
      margin-bottom: 0.5em;

      button {
        background: #ffffff 0% 0% no-repeat padding-box;
        box-shadow: 0px 6px 16px #3469cb29;
        border-radius: 8px;
        text-align: center;
        font: normal normal 600 12px/19px Roboto;
        letter-spacing: 0.17px;
        color: #0065ff;
        padding: 10px 19px;
        margin: 0px 7px 5px 2px;
        height: 40px;
        text-transform: capitalize;
      }

      button:last-child {
        margin-right: 0px;
      }

      .VirtualNoBtn {
        width: 100%;
      }
    }

    .mx-8px {
      margin-right: 8px;
      margin-left: 8px;
    }

    .leadview {
      // display: flex;
      // flex-wrap: wrap;
      width: 100%;
      position: relative;

      // margin-top: 40px;
      .slick-slider {
        -webkit-user-select: text;
        -khtml-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
      }

      .slick-list.draggable {
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      // .scroll-menu-arrow {
      //   position: absolute;
      //   top: -31px;
      //   right: 13px;
      //   cursor: pointer;
      //   .arrow-next {
      //     border-radius: 14px;
      //     border: 2px solid #0065ff;
      //     color: #0065ff;
      //     padding: 0px;
      //     line-height: 0px;
      //     .MuiSvgIcon-root {
      //       width: 18px;
      //       height: 18px;
      //     }
      //   }
      //   .arrow-prev {
      //     border-radius: 14px;
      //     border: 2px solid #0065ff;
      //     color: #0065ff;
      //     padding: 0px 0px;
      //     left: -34px;
      //     line-height: 0px;
      //     position: relative;
      //     .MuiSvgIcon-root {
      //       width: 18px;
      //       height: 18px;
      //     }
      //   }
      // }

      .slick-prev {
        // top: -18px;
        // left: 850px;
        left: -9px;
        z-index: 99;

        &::before {
          color: #0065ff;
        }
      }

      .slick-next {
        // top: -18px;
        // right: 6px;
        right: -9px;
        z-index: 99;

        &::before {
          color: #0065ff;
        }
      }

      // .scroll-menu-arrow--disabled {
      //   .arrow-next,
      //   .arrow-prev {
      //     border: 2px solid #939393;
      //     color: #939393;
      //   }
      // }
      // .horizontal-menu {
      //   max-width: 100%;
      //   margin-top: 0.8em;
      // }
      .slick-list {
        padding-top: 15px;
      }

      .card-design {
        // width: 290px;
        background: #ffffff 0% 0% no-repeat padding-box;
        box-shadow: 0px 0px 16px #00000014;
        border-radius: 16px;
        //margin: 10px 7px 10px 6px;
        position: relative;
        height: 596px;
        margin-top: 10px;

        .ContinueJourney {
          text-align: center;
          text-transform: inherit;

          .ContinueJourneybtn {
            border: 1px solid #0065ff;
            border-radius: 8px;
            background-color: transparent;
            color: #0065ff;
            margin: 0px 0px 15px;
            letter-spacing: 0.17px;
            width: auto;
            padding: 8px 11px;
            font: normal normal normal 12px/21px Roboto;
            cursor: pointer;
            outline: none;
            text-transform: capitalize;
          }

          .viewBtn {
            background: #0065ff;
            border-radius: 8px;
            margin: 0px 0px 15px;
            letter-spacing: 0.17px;
            padding: 8px 21px;
            width: auto;
            border: none;
            margin-left: 10px;
            color: #ffffff;
            font: normal normal normal 12px/21px Roboto;
            cursor: pointer;
            outline: none;
          }
        }

        h3 {
          color: #253858;
          font: normal normal 600 16px/19px Roboto;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          letter-spacing: 0.26px;
          float: left;
          width: 165px;
          margin-bottom: 0px;
        }

        .contacted-text {
          border: none;
          padding: 3px 10px;
          font: normal normal 600 10px/13px Roboto;
          letter-spacing: 0.3px;
          color: #b98000;
          opacity: 1;
          background-color: #ffe7af;
          border-radius: 4px;
          margin-top: 10px;
          display: inline-block;
        }

        .Status-text {
          border: none;
          padding: 3px 10px;
          font: normal normal 600 10px/13px Roboto;
          letter-spacing: 0.3px;
          opacity: 1;
          border-radius: 4px;
          margin-top: 10px;
          display: inline-block;
          background-color: hsl(0, 77%, 49%);
          color: #ffffff;
          // text-transform: uppercase;
          // font-weight: normal;
        }

        .datetime {
          font: normal normal normal 10px/12px Roboto;
          letter-spacing: 0.14px;
          color: #808080;
          opacity: 1;
        }

        hr {
          background-color: #f4f4f4;
          height: 1px;
          margin-top: 10px;
          border: none;
          width: 100%;
        }

        .customercard-details {

          // label
          div:nth-child(odd) {
            width: 50%;
            text-align: left;
            font: normal normal normal 12px/21px Roboto;
            letter-spacing: 0.17px;
            color: #303030;
            padding: 2px 15px;
            opacity: 1;
          }

          // value
          div:nth-child(even) {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 50%;
            text-align: right;
            font: normal normal normal 12px/21px Roboto;
            letter-spacing: 0.17px;
            color: #303030;
            padding: 2px 15px;
            opacity: 1;
          }
        }

        .showFullText {
          div:nth-child(even) {
            overflow: visible;
            word-break: break-all;
            white-space: pre-line;
          }
        }

        .bottom-icon {
          text-align: center;

          p {
            background: #e7f0ff 0% 0% no-repeat padding-box;
            padding: 10px;
            border-radius: 50%;
            width: 40px;
            margin-left: 5px;
            height: 40px;
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 5px;
            cursor: pointer;
            position: relative;

            img {
              width: 20px;
              height: 20px;
            }
          }

          .disabledIcon {
            filter: grayscale(1);
          }


          .bulletPoint {
            top: 2px;
            background-color: red;
            position: absolute;
            padding: 4px;
            width: 3px;
            margin: 0px;
            border-radius: 10px;
            height: 3px;
            right: 0;
            right: 0;
          }

          .QuotesHighlight {
            z-index: 999;
          }
        }

        .MuiButtonGroup-contained {
          border-radius: 8px;
        }

        .producticon {
          .editIcon {
            float: right;
            fill: #808080;
          }
        }

        .hotClTransfer {
          width: 100%;
          background: transparent linear-gradient(270deg, #ffffff 0%, #3dbea5 46%, #20d0a7 100%) 0% 0% no-repeat padding-box;
          text-align: center;
          font: normal normal normal 10px/13px Roboto;
          letter-spacing: 0px;
          color: #ffffff;
          text-transform: capitalize;
          opacity: 1;
          padding: 3px;
          display: flex;
          justify-content: center;
          margin-bottom: 8px;

          img {
            margin-left: 10px;
          }
        }

        .iconSection {
          float: right;
          display: flex;
          margin-top: 10px;

          img {
            margin: 0px 0px 0px 4px;
          }

          .verifedIcon {
            width: 20px;
          }

          img.whatsapp-disabled {
            opacity: 0.6;
            position: relative;

            &:after {
              content: '';
              position: absolute;
              width: 100%;
              height: 2px;
              background-color: red;
              transform: rotate(45deg);
              top: 50%;
              left: 0;
            }
          }
        }

        .btm-bar {
          position: absolute;
          bottom: 0px;
          left: 0;
          right: 0;
        }

        .leadCardTagBar {
          padding: 0px;
          margin-bottom: 8px;

          .slick-track {
            height: auto !important;
          }

          .slick-list {
            padding-top: 0px !important;
            width: 100%;
          }

          .slick-slide {
            height: 21px;
            border: none;
          }

          p {
            text-align: center;
            font: normal normal 500 10px/18px Roboto;
            letter-spacing: 0px;
            background: transparent linear-gradient(270deg, #99a3a7 0%, #859398 52%, #283048 100%) 0% 0% no-repeat padding-box;
            color: #ffffff;
            // height: 20px;
            text-align: center;
            text-transform: capitalize;
            opacity: 1;
            -webkit-animation-name: slideInDown;
            animation-name: slideInDown;
            animation-duration: 2s;
            padding: 1px;

            .clickableImg {
              display: inline;
            }
          }

          .MegentaBg {
            background: #c92286 !important;
          }

          .YellowBg {
            background: transparent linear-gradient(270deg, #fff 0%, #f0c230 52%, #f1c517 100%) 0% 0% no-repeat padding-box !important;
          }

          .blueBg {
            background: transparent linear-gradient(270deg, #a3c2f1 0%, #0065ff 52%, #0065ff 100%) 0% 0% no-repeat padding-box !important;
          }

          .greenShadeBg {
            background: transparent linear-gradient(270deg, #49c1aa 0%, #0f9f91 52%, #49c1aa 100%) 0% 0% no-repeat padding-box !important;
          }

          .orangeBg {
            background: #f78631 0% 0% no-repeat padding-box !important;
          }

          .LimitedPay {
            width: 100%;
            background: #ccfce0 0% 0% no-repeat padding-box !important;
            text-transform: capitalize;
            opacity: 1;
            padding: 0px !important;
            display: flex !important;
            justify-content: center;
            height: 20px;
            cursor: pointer;

            p {
              text-align: center;
              font: normal normal 500 12px/18px Roboto !important;
              background: #ccfce0 0% 0% no-repeat padding-box !important;
              letter-spacing: 0px;
              color: #006028 !important;
              position: relative;
              left: -7px;
            }

            img {
              width: 37px;
              height: 37px;
              float: left;
              position: relative;
              top: -7px;
              left: 0px;
            }
          }

          .LICInterestedLeads {
            width: 100%;
            background: #00428c 0% 0% no-repeat padding-box !important;
            text-transform: capitalize;
            opacity: 1;
            padding: 0px !important;
            display: flex !important;
            justify-content: center;
            height: 20px;
            cursor: pointer;

            p {
              text-align: center;
              font: normal normal 500 12px/18px Roboto !important;
              background: #00428c 0% 0% no-repeat padding-box !important;
              letter-spacing: 0px;
              color: #f9faf9 !important;
              left: -4px;
              padding-left: 10px;
            }

            img {
              width: 47px;
              height: 38px;
              float: left;
              position: relative;
              top: -8px;
              left: 0px;
            }
          }
        }


      }

      .card-design.vertical {
        .datetime-div {
          margin: 0;
          float: left;
          width: 80%;
          text-align: left;

          // .hotCall-transfer {
          //   filter: brightness(0.5);
          //   cursor: pointer;
          //   margin-right: 8px;
          //   margin-top: 5px;
          //   float: right;
          // }

          .datetime {
            line-height: 16px;
          }
        }

        .edit-ico {
          span {
            cursor: pointer;
          }

          .editIcon {
            float: right;
            cursor: pointer;
          }

          .penSizingCss {
            animation: penSizing 1s cubic-bezier(.36, .07, .57, .99) infinite;
            animation-iteration-count: 4;
          }

          @keyframes penSizing {
            0% {
              transform: scale(1.2) rotate(0);
            }

            20% {
              transform: scale(1.2) rotate(30deg);
            }

            30% {
              transform: scale(1.2) rotate(-20deg);
            }

            50% {
              transform: scale(1.2) rotate(30deg);
            }

            60% {
              transform: scale(1.2) rotate(-20deg);
            }

            100% {
              transform: scale(1.2) rotate(0);
            }
          }
        }

        .producticon {
          margin-right: 10px;
          background: #ffffff 0% 0% no-repeat padding-box;
          border-radius: 12px;
          opacity: 1;
          float: left;
          padding: 12px;
          display: flex;
          width: 48px;
          height: 48px;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;

          img {
            width: 18px;
            height: 17px;
          }

          span {
            position: absolute;
            background-color: white;
            padding: 1px 7px;
            border-radius: 13px;
            top: 5px;
            left: 4px;
            border: 2px solid #fff2bc;
            font-size: 12px;
            font-family: roboto;
            letter-spacing: 0.17px;
            color: #3960a2;
            opacity: 1;
          }

          p {
            text-align: left;
            font: normal normal bold 8px/15px Roboto;
            letter-spacing: 0.13px;
            color: #253858;
            opacity: 1;
            text-transform: uppercase;
          }
        }

        .cust-info {
          border-radius: 16px 16px 0px 0px;
          opacity: 1;
          padding: 15px;
          min-height: 110px;
          // height:110px;

          .cust-data-div {
            display: flex;
            flex-wrap: wrap;
          }

          .interestedFos {
            top: -19px;
            text-align: center;
            font: normal normal 600 11px/15px Roboto;
            letter-spacing: 0px;
            color: #256265;
            background: #bcf0f2 0% 0% no-repeat padding-box;
            border-radius: 4px 4px 0px 0px;
            opacity: 1;
            position: absolute;
            padding: 2px 10px;
          }

          .PitchMaternity {
            top: -19px;
            text-align: center;
            font: normal normal 600 11px/15px Roboto;
            letter-spacing: 0px;
            border-radius: 4px 4px 0px 0px;
            opacity: 1;
            position: absolute;
            padding: 2px 10px;
            color: #ffffff;
            background: #f78631 0% 0% no-repeat padding-box;
          }

        }

        .primary {
          background: transparent linear-gradient(109deg, #fff1b7 0%, #ffffff 100%) 0% 0% no-repeat padding-box;
        }

        .secondary {
          background: transparent linear-gradient(109deg, #c8dbf9 0%, #ffffff 100%) 0% 0% no-repeat padding-box;

          span {
            border-color: #e9f1ff;
          }
        }

        .greenBackGround {
          background: transparent linear-gradient(109deg, #94eb9f 0%, #ffffff 100%) 0% 0% no-repeat padding-box;
        }

        .RedBackground {
          background: transparent linear-gradient(109deg, #fbbea1 0%, #ffffff 100%) 0% 0% no-repeat padding-box;
          ;

        }

        .Golden {
          background: transparent linear-gradient(109deg, #fdf2b0 0%, #ffffff 100%) 0% 0% no-repeat padding-box;
        }
      }

      // .card-design.landscape {
      //   //width: calc(100% - 80px);
      //   //width: 100%;
      //   // width: 98%;
      //   margin: 0px;
      //   background: #ffffff 0% 0% no-repeat padding-box;
      //   box-shadow: 0px 0px 16px #00000014;
      //   border-radius: 16px;
      //   //margin: 10px 30px 10px 6px;
      //   padding: 15px;
      //   .cust-info {
      //     display: flex;
      //     .producticon {
      //       margin-right: 16px;
      //       img {
      //         width: 48px;
      //         height: 48px;
      //       }
      //     }
      //     .cust-data-div {
      //       width: 20%;
      //     }
      //     .datetime-div {
      //       align-self: flex-end;
      //       width: calc(80% - 48px);
      //       display: flex;
      //       flex-wrap: wrap;
      //     }
      //   }
      //   .btm-bar {
      //     width: 100%;
      //     display: flex;
      //     align-items: center;
      //     margin-top: 15px;
      //     .bottom-icon {
      //       width: 50%;
      //       text-align: left;
      //     }
      //     .ContinueJourney {
      //       width: 50%;
      //       text-align: right;
      //       button:first-child {
      //         color: #fff;
      //       }
      //     }
      //   }
      //   h3 {
      //     color: #253858;
      //     font: normal normal 600 16px/24px Roboto;
      //     margin-bottom: 8px;
      //     letter-spacing: 0.26px;
      //   }
      //   .contacted-text {
      //     border-radius: 4px;
      //     background-color: #1ddbaf;
      //     border: none;
      //     padding: 4px 10px;
      //     font: normal normal bold 12px/21px Roboto;
      //     letter-spacing: 0.17px;
      //     color: #ffffff;
      //     text-transform: uppercase;
      //     font-weight: normal;
      //   }
      //   .datetime {
      //     font: normal normal normal 12px/21px Roboto;
      //     letter-spacing: 0.17px;
      //     color: #808080;
      //     width: 100%;
      //     display: block;
      //     text-align: right;
      //   }
      //   // .hotCall-transfer {
      //   //   filter: brightness(0.5);
      //   //   position: absolute;
      //   //   top: 17px;
      //   //   right: 60px;
      //   //   cursor: pointer;
      //   // }
      //   .edit-ico {
      //     font: normal normal normal 12px/21px Roboto;
      //     letter-spacing: 0.17px;
      //     color: #808080;
      //     width: 100%;
      //     display: block;
      //     text-align: right;
      //     cursor: pointer;
      //     .editIcon {
      //       float: right;
      //     }
      //     .paymentIcon {
      //       float: right;
      //       margin-top: 7px;
      //       margin-right: 10px;
      //     }
      //   }
      //   hr {
      //     background-color: #f4f4f4;
      //     height: 1px;
      //     margin-top: 10px;
      //     border: none;
      //     width: 100%;
      //     margin-bottom: 20px;
      //   }
      //   .customercard-details {
      //     padding: 10px 14px;

      //     &:first-child() {
      //       background: #ff0000;
      //     }
      //     &.last {
      //       border-right: 0;
      //       padding-right: 0;
      //       margin-right: 0;
      //     }
      //     div:nth-child(odd) {
      //       width: 50%;
      //       color: #808080;
      //       font: normal normal normal 12px/21px Roboto;
      //     }
      //     div:nth-child(even) {
      //       color: #253858;
      //       white-space: nowrap;
      //       font: normal normal 600 12px/21px Roboto;
      //       letter-spacing: 0.17px;
      //       overflow: hidden;
      //       text-overflow: ellipsis;
      //       width: 50%;
      //       text-align: right;
      //     }
      //   }
      //   .bottom-icon {
      //     text-align: center;
      //     p {
      //       background: #e7f0ff 0% 0% no-repeat padding-box;
      //       padding: 10px;
      //       border-radius: 50%;
      //       width: 40px;
      //       margin-left: 15px;
      //       height: 40px;
      //       display: inline-block;
      //       margin-right: 15px;
      //       margin-bottom: 15px;
      //       cursor: pointer;
      //     }
      //     .disabledIcon {
      //       filter: grayscale(1);
      //     }
      //   }

      //   .ContinueJourney {
      //     text-align: center;
      //     button:first-child {
      //       width: 210px;
      //       font-size: 12px;
      //       text-transform: capitalize;
      //       background-color: #0065ff;
      //       border-color: #065ce0;
      //       padding: 8px;
      //       border-radius: 8px 0px 0px 8px;
      //       letter-spacing: 0.17px;
      //       outline: none;
      //     }
      //     button:last-child {
      //       background-color: #0065ff;
      //       border-radius: 0px 8px 8px 0px;
      //     }
      //   }
      //   .MuiButtonGroup-contained {
      //     border-radius: 8px;
      //   }
      //   .producticon {
      //     .editIcon {
      //       float: right;
      //       fill: #808080;
      //     }
      //   }

      //   .GridRow:nth-child(3n + 2) {
      //     border-right: 1px solid #f1f1f1;
      //     border-left: 1px solid #f1f1f1;
      //   }
      // }
      .disabled-card-design {
        width: 290px;
        background: #1d1c1c30 0% 0% no-repeat padding-box;
        box-shadow: 0px 0px 16px #00000014;
        border-radius: 16px;
        padding: 15px;
        margin: 10px 7x 10px 6px;

        h3 {
          color: #253858;
          font: normal normal 600 16px/19px Roboto;
          margin-bottom: 8px;
          letter-spacing: 0.26px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .contacted-text {
          border-radius: 4px;
          background-color: #1ddbaf;
          border: none;
          padding: 4px 10px;
          font: normal normal bold 12px/21px Roboto;
          letter-spacing: 0.17px;
          color: #ffffff;
          text-transform: uppercase;
          font-weight: normal;
        }

        .datetime {
          font: normal normal normal 12px/21px Roboto;
          letter-spacing: 0.17px;
          color: #808080;
          float: right;
        }

        hr {
          background-color: #f4f4f4;
          height: 1px;
          margin-top: 10px;
          border: none;
          width: 100%;
        }

        .customercard-details {
          div:nth-child(odd) {
            width: 50%;
            color: #808080;
          }

          div:nth-child(even) {
            color: #253858;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 50%;
            text-align: right;
          }
        }

        .bottom-icon {
          text-align: center;

          p {
            background: #e7f0ff 0% 0% no-repeat padding-box;
            padding: 10px;
            border-radius: 50%;
            width: 40px;
            margin: 10px 15px;
            height: 40px;
            display: inline-block;
            cursor: pointer;
          }

          .disabledIcon {
            filter: grayscale(1);
          }
        }

        .ContinueJourney {
          text-align: center;

          button:first-child {
            width: 210px;
            font-size: 12px;
            text-transform: capitalize;
            background-color: #0065ff;
            border-color: #065ce0;
            padding: 8px;
            border-radius: 8px 0px 0px 8px;
            letter-spacing: 0.17px;
            outline: none;
          }

          button:last-child {
            background-color: #0065ff;
            border-radius: 0px 8px 8px 0px;
          }
        }

        .MuiButtonGroup-contained {
          border-radius: 8px;
        }

        .producticon {
          .editIcon {
            float: right;
            fill: #808080;
          }
        }
      }
    }

    .hoverable {
      color: #0065ff;
    }

    .detailBox {
      background: #e6f0ff 0% 0% no-repeat padding-box;
      border-radius: 4px;
      width: 24px;
      height: 24px;
      line-height: 25px;
      float: right;
      text-align: center;
      cursor: pointer;
    }

    .openDIvHeight {
      height: 596px;
    }

    .customerHistory {
      background-color: #fff;
      border-radius: 16px;
      // height: 80px;
      padding: 18px;
      letter-spacing: 0.17px;
      position: relative;
      margin-top: 25px;

      h3 {
        color: #253858;
        font: normal normal 600 16px/24px Roboto;
        margin-bottom: 8px;
        letter-spacing: 0.26px;
      }

      p {
        color: #808080;
        margin-bottom: 14px;
        font-size: 12px;
      }

      ul {
        justify-content: space-between;
        padding: 10px 0 0;
        font-size: 12px;
        display: flex;
        list-style-type: none;
        flex-wrap: wrap;
        width: 100%;

        li:nth-child(odd) {
          width: 50%;
          margin-bottom: 8px;
          font: normal normal normal 12px/21px Roboto;
          letter-spacing: 0.17px;
          color: #808080;
          opacity: 1;
        }

        li:nth-child(even) {
          color: #253858;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 50%;
          text-align: right;
          margin-bottom: 8px;
          font: normal normal normal 12px/21px Roboto;
          letter-spacing: 0.17px;
        }
      }

      button {
        border: 1px solid #0065ff;
        border-radius: 8px;
        padding: 9px 60px;
        width: 100%;
        color: #0065ff;
        background-color: #fff;
        margin-top: 3rem;
        cursor: pointer;
        outline: none;
        font-weight: 600;
        letter-spacing: 0.17px;
      }

      .calldetails {
        margin-top: 15px;
      }
    }

    .callDetails {
      background-color: #fff;
      border-radius: 16px;
      height: 80px;
      padding: 18px;
      letter-spacing: 0.17px;
      position: relative;

      h3 {
        font: normal normal 16px/24px Roboto;
        letter-spacing: 0.26px;
        color: #253858;
        font-weight: 600;
        margin-bottom: 1em;
      }

      ul {
        display: flex;
        list-style-type: none;
        flex-wrap: wrap;
        margin-top: 0px;
        width: 100%;
        letter-spacing: 0.17px;

        li:nth-child(odd) {
          width: 50%;
          color: #808080;
          margin-bottom: 10px;
        }

        li:nth-child(even) {
          color: #253858;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 50%;
          text-align: right;
          margin-bottom: 10px;
        }
      }
    }

    .sendsoftcopy-box {
      background-color: #fff;
      border-radius: 16px;
      padding: 18px;
      position: relative;
      height: 80px;
      margin-top: 15px;

      h3 {
        color: #253858;
        font: normal normal 600 16px/24px Roboto;
        margin-bottom: 8px;
        letter-spacing: 0.26px;
      }

      textarea {
        border: 1px solid #808080;
        border-radius: 4px;
        width: 100%;
        font-size: 14px;
        padding: 10px;
        outline: none;
        resize: none;
        font-family: "Roboto";
        letter-spacing: 0.22px;
        margin-top: 0px;
      }

      button {
        background: #0065ff;
        border-radius: 8px;
        width: 100%;
        margin-top: 35px;
        letter-spacing: 0.17px;
        padding: 8px;
        border: none;
        color: #ffffff;
        font: normal normal normal 12px/21px Roboto;
        cursor: pointer;
        outline: none;
      }
    }

    .opentoggleDivHeight {
      height: 390px;
    }

    .recomendations-section {
      width: 100%;
      background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      height: auto;
      margin-top: 2rem;
      padding: 18px;

      .addnew {
        text-align: right;
        margin-bottom: 0px;
        float: right;
        cursor: pointer;

        span {
          display: flex;
          align-items: center;
          font: normal normal bold 14px/21px Roboto;
          letter-spacing: 0.2px;
          color: #0065ff;
        }
      }

      .expandmoreIcon {
        float: right;
        position: relative;
        top: -16px;
        right: 2px;

        svg {
          color: #0065ff;
        }
      }

      h3 {
        color: #253858;
        font: normal normal 600 16px/24px Roboto;
        margin-bottom: 0px;
        letter-spacing: 0.26px;
      }

      span {
        letter-spacing: 0.17px;
        color: #808080;
        font: normal normal 12px/21px Roboto;
        line-height: 40px;
      }

      button {
        background: #0065ff;
        border-radius: 8px;
        width: 100%;
        margin-top: 15px;
        margin-bottom: 15px;
        letter-spacing: 0.17px;
        padding: 8px;
        border: none;
        color: #ffffff;
        font: normal normal 12px/21px Roboto;
        outline: none;
        cursor: pointer;
      }

      .horizontal-menu {
        max-width: 100%;
      }
    }

    .Preferred-section {
      width: 100%;
      background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      height: auto;
      margin-top: 2rem;
      padding: 18px 18px 0px 18px;

      .slick-slider {
        -webkit-user-select: text;
        -khtml-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
      }

      .slick-list.draggable {
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      .slick-prev {
        left: -9px;
        z-index: 99;

        &::before {
          color: #0065ff;
        }
      }

      .slick-next {
        right: 20px;
        z-index: 99;

        &::before {
          color: #0065ff;
        }
      }

      .slick-list {
        padding-top: 15px;
      }

      img {
        width: 100px;
        height: 50px;
        margin: auto;
      }

      h4 {
        color: #253858;
        font: normal normal 500 13px/13px Roboto;
        margin-top: 3px;
        letter-spacing: 0.26px;
        text-align: center;
      }

      h3 {
        color: #253858;
        font: normal normal 600 16px/24px Roboto;
        margin-bottom: 0px;
        letter-spacing: 0.26px;
      }

      .caption {
        letter-spacing: 0.17px;
        color: #808080;
        font: normal normal 12px/21px Roboto;
      }

      button {
        // background: #0065ff;
        border-radius: 8px;
        margin-top: 1px;
        margin-bottom: 15px;
        letter-spacing: 0.17px;
        padding: 8px;
        border: none;
        color: #ffffff;
        font: normal normal 12px/21px Roboto;
        outline: none;
        cursor: pointer;
      }

      .horizontal-menu {
        max-width: 100%;
      }
    }

    .createbooking-section {
      background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      height: auto;
      margin-top: 2rem;
      padding: 18px;
      line-height: 2;

      .expandmoreIcon {
        float: right;
        position: relative;
        top: -16px;
        right: 2px;

        svg {
          color: #0065ff;
        }
      }

      h3 {
        color: #253858;
        font: normal normal 600 16px/24px Roboto;
        margin-bottom: 8px;
        letter-spacing: 0.26px;
      }

      .caption {
        letter-spacing: 0.17px;
        color: #808080;
        font: normal normal 12px/21px Roboto;
      }

      .paymentDetails-title {
        margin-bottom: 1.5rem;
        margin-top: 1.5rem;
        width: 100%;
        padding-left: 0rem;
        cursor: pointer;
      }

      .emiTxt {
        font-size: 14px;
        font-weight: bold;
        letter-spacing: 0.22px;
        color: #253858;
        float: left;
        margin-top: 6px;
        margin-right: 10px;
      }

      .MuiIconButton-colorPrimary {
        color: #808080 !important;
      }

      .MuiIconButton-root {
        color: #808080 !important;
      }

      .MuiRadio-colorPrimary.Mui-checked {
        color: #0065ff !important;
      }

      .MuiCheckbox-colorSecondary.Mui-checked {
        color: #0065ff !important;
      }

      .editBtn {
        border: 1px solid #0065ff;
        border-radius: 8px;
        background-color: transparent;
        color: #0065ff;
        margin-top: 35px;
        letter-spacing: 0.17px;
        width: 258px;
        padding: 8px;
        font: normal normal 600 12px/21px Roboto;
        cursor: pointer;
        outline: none;
      }

      .confirmBtn {
        background: #0065ff;
        border-radius: 8px;
        margin-top: 35px;
        letter-spacing: 0.17px;
        padding: 8px;
        width: 258px;
        border: none;
        margin-left: 10px;
        color: #ffffff;
        font: normal normal 600 12px/21px Roboto;
        cursor: pointer;
        outline: none;
      }

      hr {
        margin: 1.2rem 0rem 0rem;
      }

      .addbtn {
        padding: 10px 0px 0px;
        background-color: transparent;
      }
    }

    .bookingDeatilsHead {
      h3 {
        font: normal normal 600 16px/24px Roboto;
        letter-spacing: 0.26px;
        color: #253858;
        margin: 25px 0px 0px;
        padding-left: 5px;
      }

      .caption {
        text-align: left;
        font: normal normal normal 12px/21px Roboto;
        letter-spacing: 0.17px;
        color: #808080;
        padding-left: 5px;
      }

      .orderbooking-section {
        background: #ffffff 0% 0% no-repeat padding-box;
        border-radius: 16px;
        height: auto;
        margin-top: 1rem;
        padding: 18px 0px 30px 0px;
        position: relative;

        .expandmoreIcon {
          float: right;
          position: absolute;
          top: 16px;
          right: 20px;

          svg {
            color: #0065ff;
          }
        }

        .leadId {
          font: normal normal bold 16px/21px Roboto;
          letter-spacing: 0px;
          color: #0065ff;
          padding: 15px;
        }

        .planName {
          font: normal normal 600 14px/21px Roboto;
          letter-spacing: 0px;
          color: #253858;

          label {
            font: normal normal normal 12px/21px Roboto;
            letter-spacing: 0.17px;
            color: #808080;
          }
        }

        .paymentDetails-title {
          margin: 25px 0px;
        }

        .bookingMidSection {
          background: #fafafa 0% 0% no-repeat padding-box;
          padding: 18px;
          border-left: 4px solid #0065ff;
          margin-top: 15px;
        }

        .editBtn {
          border: 1px solid #0065ff;
          border-radius: 8px;
          background-color: transparent;
          color: #0065ff;
          margin-top: 35px;
          letter-spacing: 0.17px;
          width: 258px;
          padding: 8px;
          font: normal normal 600 12px/21px Roboto;
          cursor: pointer;
          outline: none;
        }

        .confirmBtn {
          background: #0065ff;
          border-radius: 8px;
          margin-top: 35px;
          letter-spacing: 0.17px;
          padding: 8px;
          width: 258px;
          border: none;
          margin-left: 10px;
          color: #ffffff;
          font: normal normal 600 12px/21px Roboto;
          cursor: pointer;
          outline: none;
        }

        .MuiIconButton-root {
          color: #808080 !important;
        }

        .MuiRadio-colorPrimary.Mui-checked {
          color: #0065ff !important;
        }

        .emiTxt {
          font-size: 14px;
          font-weight: bold;
          letter-spacing: 0.22px;
          color: #253858;
          float: left;
          margin-top: 6px;
          margin-right: 10px;
        }

        .agentTxtBold {
          color: #253858;
        }
      }
    }

    .crossSell-section {
      background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      height: auto;
      margin-top: 1.5rem;
      padding: 18px;

      h3 {
        color: #253858;
        font: normal normal 600 16px/24px Roboto;
        margin-bottom: 8px;
        letter-spacing: 0.26px;
      }

      .subHeading {
        letter-spacing: 0.17px;
        color: #808080;
        font: normal normal 12px/21px Roboto;
      }

      textarea {
        border: 1px solid #808080;
        border-radius: 4px;
        width: 100%;
        font-size: 14px;
        padding: 10px;
        outline: none;
        resize: none;
        font-family: "Roboto";
        letter-spacing: 0.22px;
        margin-top: 25px;
      }

      button {
        background: #0065ff;
        border-radius: 8px;
        margin-top: 20px;
        letter-spacing: 0.17px;
        padding: 8px 105px;
        border: none;
        margin-left: 10px;
        color: #ffffff;
        font: normal normal 600 12px/21px Roboto;
        cursor: pointer;
        outline: none;
      }

      .expandmoreIcon {
        float: right;
        position: relative;
        top: -16px;
        right: 2px;

        svg {
          color: #0065ff;
        }
      }

      .radioDesign {
        margin-bottom: 15px;

        .MuiFormGroup-root {
          flex-wrap: nowrap;
          flex-direction: unset;
        }

        .MuiIconButton-colorSecondary {
          color: #5e6c84 !important;
        }

        .MuiFormControlLabel-label {
          font: normal normal normal 12px/21px Roboto;
          letter-spacing: 0.17px;
          color: #253858;
        }

        .MuiRadio-colorSecondary.Mui-checked {
          color: #0065ff !important;
        }
      }

      .info {
        position: relative;
        top: 34px;
        left: 4px;
        color: #808080;
        font-size: 20px !important;
      }
    }

    .AgentAssist-section {
      background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      height: auto;
      margin-top: 1.5rem;
      padding: 18px;

      h3 {
        color: #253858;
        font: normal normal 600 16px/24px Roboto;
        margin-bottom: 8px;
        letter-spacing: 0.26px;
      }

      .subHeading {
        letter-spacing: 0.17px;
        color: #808080;
        font: normal normal 12px/21px Roboto;
      }

      .MuiTableCell-root {
        font: normal normal normal 12px/16px Roboto;
        padding: 7px;
        color: #808080;
        border-bottom: none;
      }

      .MuiPaper-elevation1 {
        box-shadow: none;
      }

      .MuiTableHead-root {
        background-color: #e6e6e6;
        color: #808080;
      }

      h6 {
        text-align: left;
        font: normal normal 18px/24px Roboto;
        letter-spacing: 0px;
        color: #2e2e2e;
        font-weight: 600;
        padding-left: 10px;
      }

      hr {
        margin: 7px 0px 20px 0px;
        background-color: #eee;
        height: 1px;
        border: none;
      }

      .MuiTableRow-root {
        td {
          font: normal normal 12px/16px Roboto;
          letter-spacing: 0px;
          color: #2e2e2e;
          border: none;
        }
      }

      .moreDeatilsData:nth-child(odd) {
        background-color: #f8f8f8;
        border-radius: 4px;
      }

      .MuiIconButton-root {
        background-color: #eaeaea;
        position: absolute;
        padding: 5px;

        svg {
          font-size: 1.2rem;
        }
      }

      .expandmoreIcon {
        float: right;
        position: relative;
        top: -16px;
        right: 2px;

        svg {
          color: #0065ff;
        }
      }
    }

    .add-que {
      background: $base-button-color 0% 0% no-repeat padding-box;
      border-radius: 8px;
      display: flex;
      align-items: center;
      margin-right: 8px;
      padding: 10px 6px;
      height: 45px;
      cursor: pointer;

      a {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding-right: 10px;
        margin-right: 0;
        padding: 13px 10px;

        .icon-border {
          background: url("/public/images/add-to-que.svg") no-repeat;
          width: 16px;
          height: 16px;
          background-size: 100%;
          display: inline-block;
        }

        .atqLoader {
          background: url("/public/images/ATQ_Loader.gif") no-repeat;
          width: 21px;
          height: 19px;
          background-size: 100%;
          display: inline-block;
        }

        .atqCheck {
          background: url("/public/images/ATQ_check.gif") no-repeat;
          width: 22px;
          height: 19px;
          background-size: 100%;
          display: inline-block;
        }

        span.text {
          display: inline-block;
          font: normal normal 600 12px/19px Roboto;
          letter-spacing: 0.17px;
          margin: 0px 0px 0px 9px;
          color: #0065ff;
        }
      }
    }

    .callBtn {
      background: #0065ff 0% 0% no-repeat padding-box;
      box-shadow: 0px 6px 16px rgba(52, 105, 203, 0.1607843137);
      border-radius: 8px;
      text-align: center;
      font: normal normal 600 12px/19px Roboto;
      letter-spacing: 0.17px;
      color: #fff;
      padding: 12px 15px;
      margin: 0px 0px 5px 0px;
      height: 44px;

      a {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-right: 0;
        color: #fff;

        span.ico {
          height: 20px;

          svg {
            font-size: 20px;
            color: #fff;
          }
        }

        span.text {
          display: inline-block;
          font: normal normal 600 12px/19px Roboto;
          letter-spacing: 0.17px;
          margin: 0px 0px 0px 3px;
          color: #fff;
        }
      }
    }

    .add-car {
      background: #fff 0% 0% no-repeat padding-box;
      border-radius: 8px;
      display: flex;
      align-items: center;
      margin-right: 8px;
      height: 45px;
      cursor: pointer;
      margin-bottom: 6px;
      padding: 10px 6px;

      a {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-right: 0;
        padding: 13px 10px;

        .icon-border {
          background: url("/public/images/addcar.svg") no-repeat;
          width: 16px;
          height: 16px;
          background-size: 100%;
          display: inline-block;
        }

        span.text {
          display: inline-block;
          font: normal normal 600 12px/19px Roboto;
          letter-spacing: 0.17px;
          color: #0065ff;
        }
      }
    }

    .callBackSection {
      width: 100%;
      background: $base-button-color 0% 0% no-repeat padding-box;
      border: 1px solid #dddddd;
      border-radius: 8px;
      padding: 9px 15px;
      font: normal normal bold 12px/21px Roboto;
      letter-spacing: 0.17px;
      margin-bottom: 0em;
      color: #323232;

      .icon-calender {
        background: url("/public/images/salesview/calendar.svg") no-repeat;
        margin-right: 10px;
        width: 18px;
        position: relative;
        height: 18px;
        background-size: 100%;
        display: inline-block;
        top: 3px;
      }

      p {
        font: normal normal 600 12px/28px Roboto;
        letter-spacing: 0.17px;
        color: #0065ff;
        float: right;
        cursor: pointer;
      }
    }

    .callTransferSection {
      .transferbtn {
        background: #ffffff 0% 0% no-repeat padding-box;
        border: 1px solid #dddddd;
        border-radius: 8px;
        text-align: center;
        font: normal normal 600 12px/19px Roboto;
        letter-spacing: 0.17px;
        color: #0065ff;
        padding: 1px 0px 10px;
        margin: 0px 10px 15px 0px;
        width: 100%;
        outline: none;

        svg {
          position: relative;
          top: 7px;
          margin-right: 10px;
          color: #0065ff;
        }
      }
    }

    .suggestionBox {
      position: relative;

      ul {
        list-style-type: none;
      }

      // slider CSS//
      .mySlides {
        display: none;
      }

      /* Slideshow container */
      .slideshow-container {
        max-width: 1000px;
        position: relative;
        margin: auto;
        height: 164px;

        img {
          width: 100%;
          background-color: #fff;
          border-radius: 8px;
          height: 163px;
          margin-top: 10px;

        }
      }

      /* The dots/bullets/indicators */
      .dotBox {
        position: absolute;
        bottom: 11px;
        left: 0;
        margin: auto;
        text-align: center;
        right: 0;

        .dot {
          cursor: pointer;
          height: 4px;
          width: 34px;
          margin: 0 2px;
          background-color: #747474;
          border-radius: 5px;
          display: inline-block;
          transition: background-color 0.6s ease;
        }

        .active,
        .dot:hover {
          background-color: #fff;
          cursor: pointer;
        }
      }

      /* Fading animation */
      .fade {
        -webkit-animation-name: fade;
        -webkit-animation-duration: 1.5s;
        animation-name: fade;
        animation-duration: 1.5s;
      }

      @-webkit-keyframes fade {
        from {
          opacity: 0.4;
        }

        to {
          opacity: 1;
        }
      }

      @keyframes fade {
        from {
          opacity: 0.4;
        }

        to {
          opacity: 1;
        }
      }

      /* On smaller screens, decrease text size */
      @media only screen and (max-width: 300px) {

        .prev,
        .next,
        .text {
          font-size: 11px;
        }
      }

      .show {
        display: block;
      }
    }

    .UtmCampaignBanner {
      position: relative;

      ul {
        list-style-type: none;
      }

      // slider CSS//
      .mySlides {
        display: none;
      }

      /* Slideshow container */
      .slideshow-container {
        max-width: 1000px;
        position: relative;
        margin: auto;
        height: 164px;

        img {
          width: 100%;
          background-color: #fff;
          border-radius: 8px;
          height: 163px;
        }
      }

      /* The dots/bullets/indicators */
      .dotBox {
        position: absolute;
        bottom: 11px;
        left: 0;
        margin: auto;
        text-align: center;
        right: 0;

        .dot {
          cursor: pointer;
          height: 4px;
          width: 34px;
          margin: 0 2px;
          background-color: #747474;
          border-radius: 5px;
          display: inline-block;
          transition: background-color 0.6s ease;
        }

        .active,
        .dot:hover {
          background-color: #fff;
          cursor: pointer;
        }
      }

      /* Fading animation */
      .fade {
        -webkit-animation-name: fade;
        -webkit-animation-duration: 1.5s;
        animation-name: fade;
        animation-duration: 1.5s;
      }

      @-webkit-keyframes fade {
        from {
          opacity: 0.4;
        }

        to {
          opacity: 1;
        }
      }

      @keyframes fade {
        from {
          opacity: 0.4;
        }

        to {
          opacity: 1;
        }
      }

      /* On smaller screens, decrease text size */
      @media only screen and (max-width: 300px) {

        .prev,
        .next,
        .text {
          font-size: 11px;
        }
      }

      .show {
        display: block;
      }
    }

    .MarkImportant {
      display: inline-flex;
      cursor: pointer;
      margin-bottom: 20px;
      background: $base-button-color 0% 0% no-repeat padding-box;
      border-radius: 8px;
      float: left;
      padding: 10px 13px;
      margin-left: 8px;
      height: 44px;
    }

    .LastFiveLeads {
      display: inline-flex;
      cursor: pointer;
      margin-bottom: 20px;
      background: $base-button-color 0% 0% no-repeat padding-box;
      border-radius: 8px;
      float: left;
      padding: 10px 13px;
      margin-left: 8px;
      height: 44px;

      svg {
        color: #0065ff;
      }
    }

    .ASRTraning {
      display: inline-flex;
      cursor: pointer;
      margin-bottom: 20px;
      background: #56af97 0% 0% no-repeat padding-box;
      border-radius: 8px;
      float: left;
      padding: 10px 12px;
      margin-left: 0px;
      height: 44px;

      img {
        width: 28px;
      }
    }

    .PlayPauseBtn {
      margin-left: 10px;
      height: 44px;
      padding: 10px;
      box-shadow: none;
      border-radius: 8px;
      background-color: #0065FF;

      .MuiBadge-badge {
        top: -10px;
      }
    }

    .Foscityofflinestore {
      // background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      // padding: 8px 10px;
      margin-top: 0px;
      margin-bottom: 5px;
      position: relative;

      button {
        border: 1px solid #0065ff;
        border-radius: 8px;
        padding: 9px 50px;
        width: 100%;
        color: #0065ff;
        background-color: #fff;
        margin-top: 5px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        outline: none;
        font-weight: 600;
        letter-spacing: 0.17px;

        &.yes {
          box-shadow: 0px 6px 16px rgba(52, 105, 203, .1607843137);

          span {
            color: #ff0000;
          }
        }

        &.positive {
          box-shadow: 0px 6px 16px rgba(52, 105, 203, .1607843137);

          span {
            color: #19c702;
          }
        }

        svg {
          font-size: 15px;
          margin-left: 5px;
        }
      }
    }

    .shopse {
      // background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      // padding: 8px 10px;
      margin-top: 0px;
      margin-bottom: 5px;
      position: relative;

      button {
        border: 1px solid #0065ff;
        border-radius: 8px;
        padding: 9px 20px;
        width: 100%;
        color: #0065ff;
        background-color: #fff;
        margin-top: 5px;
        cursor: pointer;
        outline: none;
        font-weight: 600;
        letter-spacing: 0.17px;
      }
    }

    .commentbox {
      background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      padding: 18px 20px;
      margin-top: 25px;
      position: relative;

      h3 {
        color: #253858;
        font: normal normal 600 16px/24px Roboto;
        margin-bottom: 15px;
        letter-spacing: 0.26px;
      }

      textarea {
        border: 1px solid #808080;
        border-radius: 4px;
        width: 100%;
        font-size: 14px;
        padding: 10px 70px 10px 10px;
        outline: none;
        resize: none;
        font-family: "Roboto";
        letter-spacing: 0.22px;
      }

      .comment-text {
        width: 100%;
        background: #e7f0ff 0% 0% no-repeat padding-box;
        border-radius: 4px 16px 16px 16px;
        padding: 12px 15px;
        margin-top: 5px;
        margin-bottom: 2px;
        letter-spacing: 0.17px;

        .commentmsg {
          text-align: left;
          font: normal normal normal 12px/18px Roboto;
          letter-spacing: 0.17px;
          color: #253858;
          opacity: 1;
          margin-top: 5px;
          word-break: break-word;
        }

        p {
          text-align: left;
          font: normal normal 600 12px/15px Roboto;
          letter-spacing: 0.17px;
          color: #364765;
          opacity: 1;
        }

        small {
          font: normal normal 600 10px/21px Roboto;
          letter-spacing: 0.14px;
          color: #808080;
          opacity: 1;
        }
      }

      .HighLightComment {
        background: transparent linear-gradient(109deg, #94eb9f 0%, #ffffff 100%) 0% 0% no-repeat padding-box !important;
      }

      button {
        border: 1px solid #0065ff;
        border-radius: 8px;
        padding: 9px 60px;
        width: 100%;
        color: #0065ff;
        background-color: #fff;
        margin-top: 12px;
        cursor: pointer;
        outline: none;
        font-weight: 600;
        letter-spacing: 0.17px;
      }

      .textareaBtn {
        width: 61px;
        padding: 9px 10px;
        right: 27px;
        position: absolute;
      }

      .refreshBtn {
        width: auto;
        padding: 9px 10px;
      }

      .viewAllIcon {
        float: right;
        position: absolute;
        top: 16px;
        right: 18px;
        cursor: pointer;

        img {
          width: 30px;
        }
      }

      .scrollBar {
        overflow-y: auto;
        height: 375px;
      }
    }

    .claimComments {
      background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      padding: 18px 20px;
      // margin-bottom: 2em;
      position: relative;
      margin-top: 15px;

      .scrollBar {
        overflow-y: auto;
        height: 240px;
      }

      h3 {
        text-align: left;
        font: normal normal 600 16px/24px Roboto;
        letter-spacing: 0.26px;
        color: #253858;
        margin-bottom: 1em;
      }

      .comment-text {
        width: 100%;
        background: #e7f0ff 0% 0% no-repeat padding-box;
        border-radius: 4px 16px 16px 16px;
        padding: 12px 15px;
        margin-top: 5px;
        margin-bottom: 15px;
        letter-spacing: 0.17px;

        h4 {
          text-align: left;
          font: normal normal 600 12px/21px Roboto;
          letter-spacing: 0.17px;
          color: #364765;
          margin-bottom: 5px;
        }

        .commentmsg {
          text-align: left;
          font: normal normal normal 12px/18px Roboto;
          letter-spacing: 0.17px;
          color: #253858;
          opacity: 1;
          margin-top: 5px;
          word-break: break-word;
        }

        p {
          text-align: left;
          font: normal normal 600 12px/15px Roboto;
          letter-spacing: 0.17px;
          color: #364765;
          opacity: 1;
        }
      }
    }

    .RenewalAddonLead {
      background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      padding: 18px 20px;
      // margin-bottom: 2em;
      position: relative;
      margin-top: 15px;

      .scrollBar {
        overflow-y: auto;
        height: 240px;
      }

      h3 {
        text-align: left;
        font: normal normal 600 16px/24px Roboto;
        letter-spacing: 0.26px;
        color: #253858;
        margin-bottom: 1em;
      }
    }

    .customerActivity {
      background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      padding: 18px 20px;
      position: relative;
      margin-top: 15px;

      h3 {
        text-align: left;
        font: normal normal 600 16px/24px Roboto;
        letter-spacing: 0.26px;
        color: #253858;
        margin-bottom: 1em;
      }

      .GridRow {
        div:nth-child(odd) {
          width: 50%;
          text-align: left;
          font: normal normal normal 12px/18px Roboto;
          letter-spacing: 0.17px;
          color: #808080;
          padding: 2px 15px 15px 0px;
          opacity: 1;
        }

        div:nth-child(even) {
          font: normal normal normal 12px/18px Roboto;
          letter-spacing: 0.19px;
          color: #0065ff;
          width: 50%;
          padding: 2px 0px 15px 0px;
          opacity: 1;
          text-align: left;
        }
      }

      button {
        border: 1px solid #0065ff;
        border-radius: 8px;
        padding: 7px 60px;
        width: 100%;
        color: #0065ff;
        background-color: #fff;
        margin-top: 12px;
        cursor: pointer;
        outline: none;
        font-weight: 600;
        font-size: 12px;
        letter-spacing: 0.17px;
      }
    }

    .IncomeNotAvailable {
      background-color: #feeaef;
      padding: 9px 17px;
      width: 100%;
      font: normal normal 600 14px/19px Roboto;
      margin-bottom: 11px;
      border: 1px solid #ffafaf;
      border-radius: 8px;
      color: #ff1515;
    }

    .addInfo {
      background: #ffffff 0% 0% no-repeat padding-box;
      border-radius: 16px;
      position: relative;
      padding: 18px 15px;
      // margin-bottom: 2em;
      margin-top: 15px;

      .caption {
        font: normal normal normal 12px/21px Roboto;
        letter-spacing: 0.17px;
        color: #808080;
        margin-bottom: 15px;
      }

      h3 {
        text-align: left;
        font: normal normal 600 16px/24px Roboto;
        letter-spacing: 0.26px;
        color: #253858;
      }

      .submitBtn {
        border: none;
        background: #0065ff;
        border-radius: 8px;
        padding: 6px 45px;
        text-align: center;
        font: normal normal 12px/21px Roboto;
        letter-spacing: 0.17px;
        color: #fff;
        font-weight: 600;
        width: 100%;
        outline: none;
        margin-top: 2rem;
        cursor: pointer;
      }

      .disabledBtn {
        border: none;
        background: #aba9a9;
        border-radius: 8px;
        padding: 6px 45px;
        text-align: center;
        font: normal normal 12px/21px Roboto;
        letter-spacing: 0.17px;
        color: #fff;
        font-weight: 600;
        width: 100%;
        outline: none;
        margin-top: 2rem;
        cursor: default;
      }

      .radioDesign {
        margin-bottom: 15px;

        .MuiFormGroup-root {
          flex-wrap: nowrap;
          flex-direction: unset;
        }

        .MuiIconButton-colorSecondary {
          color: #5e6c84 !important;
        }

        .MuiFormControlLabel-label {
          font: normal normal normal 12px/21px Roboto;
          letter-spacing: 0.17px;
          color: #253858;
        }

        .MuiRadio-colorSecondary.Mui-checked {
          color: #0065ff !important;
        }
      }

      .Additional-details {
        justify-content: space-between;
        padding: 10px 0 0;
        font-size: 12px;
        display: flex;
        list-style-type: none;
        flex-wrap: wrap;
        width: 100%;
        letter-spacing: 0.17px;

        div:nth-child(odd) {
          width: 50%;
          font: normal normal normal 12px/37px Roboto;
          color: #303030;
        }

        div:nth-child(even) {
          color: #253858;
          white-space: nowrap;
          font: normal normal 600 12px/21px Roboto;
          letter-spacing: 0.17px;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 50%;
          text-align: right;
        }

        .MuiSwitch-track {
          background: #eaeaea !important;
        }

        .MuiSwitch-thumb {
          background-color: #808080;
        }

        .Mui-checked {
          .MuiSwitch-thumb {
            background-color: #0065ff;
          }
        }

        .PotentialBuyer {
          font-weight: 600 !important;
          font-size: 13px !important;
          width: 55% !important;
        }

        .PotentialBuyerToggle {
          width: 45% !important;
        }
      }

      .claim {
        display: none;
      }

      .NoCostEMIToggle {
        justify-content: flex-end;

        button {
          padding: 0px 7px;
          font: normal normal 600 12px/21px Roboto;
          text-transform: capitalize;
          height: 30px;
          color: #253858;

          &:first-child {
            border-radius: 20px;
            border-top-right-radius: 0px;
            border-bottom-right-radius: 0px;
          }

          &:last-child {
            border-radius: 20px;
            border-top-left-radius: 0px;
            border-bottom-left-radius: 0px;
          }
        }

        .Mui-selected {
          color: #fff;
          background-color: #0065ff;
        }

        .Mui-disabled {
          color: #fff;
          background-color: #aba9a9;
        }

        .Mui-disabled.Mui-selected {
          color: #fff;
          background-color: #0065ff;
        }
      }
    }


    #searchbar {
      margin-left: 7px;
      position: relative;

      .searchbox {
        background: #fff url("/public/images/salesview/search-icon.svg") no-repeat 242px center;
        border-radius: 8px;
        width: 100%;
        border: none;
        text-align: left;
        font: normal normal normal 12px/16px Roboto;
        letter-spacing: 0px;
        color: #808080;
        opacity: 1;
        outline: none;
        padding: 14px;
        margin-bottom: 0em;
      }

      .searchOption {
        background: #ffffff 0% 0% no-repeat padding-box;
        box-shadow: 0px 6px 6px #00000029;
        z-index: 1;
        position: absolute;
        border-radius: 16px;
        padding: 18px 6px 18px 12px;
        width: 100%;
        right: 0px;

        ul {
          list-style-type: none;
          max-height: 220px;
          overflow-y: auto;
          overflow-x: hidden;
          padding-right: 9px;

          li {
            cursor: pointer;
          }

          p {
            text-align: left;
            font: normal normal normal 12px/16px Roboto;
            letter-spacing: 0px;
            color: #000000;
            margin-bottom: 3px;
          }

          span {
            font: normal normal normal 12px/16px Roboto;
            letter-spacing: 0px;
            color: #808080;
            cursor: pointer;
          }

          label {
            font: normal normal normal 12px/16px Roboto;
            letter-spacing: 0px;
            color: #808080;
            float: right;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 50%;
          }
        }

        hr {
          margin: 7px 0px 7px 0px;
          background-color: #eee;
          height: 1px;
          border: none;
        }

        h3 {
          font-size: 15px;
        }
      }
    }

    .videoButton {
      float: left;
      position: relative;
      display: inline-block;
      height: 44px;
      line-height: 48px;
      margin-left: 5px;
      background-color: #0065FF;

      svg {
        font-size: 32px;
      }
    }

    .FOSAssignToCallCentre {
      padding-left: 2px;
      padding-right: 2px;
      margin-left: 5px;
      height: 44px;
      width: 120px;
      font-size: 13px;
    }

    .videoBtn {
      // margin-left: 13px;
      float: left;
      position: relative;
      display: inline-block;
      top: -10px;

      .tooltiptext {
        visibility: visible;
        border-radius: 15px;
        padding: 5px 20px;
        position: absolute;
        z-index: 1;
        width: 409px;
        height: 113px;
        background: #00a78d 0% 0% no-repeat padding-box;
        box-shadow: 0px 3px 6px #00000029;
        top: -35px;
        left: 110%;
        text-align: left;
        font: normal normal normal 14px/24px Roboto;
        letter-spacing: 0.22px;
        color: #ffffff;
        opacity: 1;
        display: flex;
        align-items: center;

        .gotItBtn {
          background: #ffffff99;
          border-radius: 8px;
          border: none;
          outline: none;
          font: normal normal 500 14px/21px Roboto;
          letter-spacing: 0.22px;
          color: #ffffff;
          width: 130px;
          text-align: center;
          padding: 4px;
          cursor: pointer;
        }
      }

      .tooltiptext::after {
        content: "";
        position: absolute;
        top: 50%;
        right: 100%;
        margin-top: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: transparent #00a78d transparent transparent;
      }
    }

    .cltransfer {
      background: #ffffff 0% 0% no-repeat padding-box;
      box-shadow: 0px 0px 16px #00000014;
      border-radius: 8px;
      opacity: 1;
      padding: 11px 15px 7px;
      margin-left: 13px;
      float: left;
      height: 44px;
      line-height: 28px;
    }

    .minimize {
      background: #fff url("/public/images/salesview/minus.svg") no-repeat center;
      position: absolute;
      right: 28px;
      top: -10px;
      border: none;
      box-shadow: 0px 0px 6px #0065ff29;
      border-radius: 28px;
      opacity: 1;
      width: 44px;
      height: 20px;
      outline: none;
    }

    .maximize {
      background: #fff url("/public/images/salesview/add.svg") no-repeat 11px;
      position: absolute;
      right: 28px;
      top: -10px;
      border: none;
      box-shadow: 0px 0px 6px #0065ff29;
      border-radius: 28px;
      opacity: 1;
      text-align: center;
      width: 44px;
      height: 20px;
      outline: none;

      span {
        margin-left: 12px;
        text-align: center;
        font: normal normal bold 12px/19px Roboto;
        letter-spacing: 0.17px;
        color: #000000;
        opacity: 1;
      }
    }

    .ht-20 {
      height: 20px;
    }

    .BusinessRating {
      width: 100%;
      font: normal normal normal 16px/29px Roboto;
      letter-spacing: 0px;
      color: #ffffff;
      background-color: var(--box-color);
      opacity: 1;
      padding: 7px 12px;
      border-radius: 8px;
      float: left;

      span {
        font-weight: 600;
      }

      button {
        float: right;
        background-color: transparent;
        padding: 4px 10px;
        position: relative;
        border: 1px solid #ffffff;
        color: #fff;
        font: normal normal normal 13px/19px Roboto;
        border-radius: 4px;
        top: 0px;
        cursor: pointer;
      }
    }

    .HDFCPasa {
      background-color: #96FFC3;
      border: 1px solid #96FFC3;
      text-align: center;
      font: normal normal 600 13px/17px Roboto;
      letter-spacing: 0px;
      color: #1E522E;
      opacity: 1;
      display: flex;
      border-radius: 8px;
      padding: 0px;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      button {
        text-decoration: underline;
        font: normal normal 600 11px/13px Roboto;
        letter-spacing: 0px;
        color: #0065ff;
        background-color: transparent;
        border: none;
        outline: none;
        cursor: pointer;
        padding: 14px 12px;
      }

      p {
        position: relative;
        overflow: hidden;
        padding: 12px 55px 12px 12px;

        &::before {
          background: linear-gradient(to right,
              fade_out(#fff, 1) 0%,
              fade_out(#fff, 0.5) 100%);
          content: "";
          display: block;
          height: 100%;
          left: -75%;
          position: absolute;
          top: 0;
          transform: skewX(-25deg);
          width: 50%;
          z-index: 0;
          animation: shine 0.8s infinite;
        }

        @keyframes shine {
          100% {
            left: 100%;
          }
        }
      }
    }

    .MaxPasa {
      background-color: #96FFC3;
      border: 1px solid #96FFC3;
      text-align: center;
      font: normal normal 600 12px/17px Roboto;
      letter-spacing: 0px;
      color: #1E522E;
      opacity: 1;
      display: flex;
      border-radius: 8px;
      padding: 0px;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      button {
        text-decoration: underline;
        font: normal normal 600 11px/13px Roboto;
        letter-spacing: 0px;
        color: #0065ff;
        background-color: transparent;
        border: none;
        outline: none;
        cursor: pointer;
        padding: 14px 12px;
      }

      p {
        position: relative;
        overflow: hidden;
        padding: 12px 12px 12px 8px;

        &::before {
          background: linear-gradient(to right,
              fade_out(#fff, 1) 0%,
              fade_out(#fff, 0.5) 100%);
          content: "";
          display: block;
          height: 100%;
          left: -75%;
          position: absolute;
          top: 0;
          transform: skewX(-25deg);
          width: 50%;
          z-index: 0;
          animation: shine 0.8s infinite;
        }

        @keyframes shine {
          100% {
            left: 100%;
          }
        }
      }
    }

    .PitchCard {
      width: 97% !important;
      position: relative;
      height: 596px;
      margin-top: 10px;
      padding: 15px;
      border-radius: 20px;
      background: linear-gradient(180deg, #BCD7FF 0%, #FBFCFF 100%);


      .backBtn {
        box-shadow: none;
        color: white;
        background: #A8ABE6 0% 0% no-repeat padding-box;
        border-radius: 20px;
        opacity: 1;
        font: normal normal 500 14px/28px Roboto;
        padding: 3px 14px;
      }

      .openNewtabIcon {
        float: right;
        cursor: pointer;

      }

      .submitBtn {
        color: white;
        background: #A8ABE6 0% 0% no-repeat padding-box;
        border-radius: 20px;
        opacity: 1;
        padding: 6px 40px;
        box-shadow: none;

        span {
          font: normal normal 500 14px/28px Roboto !important;
        }
      }

      .heading {
        display: flex;
        align-items: center;
        font: normal normal 500 20px/28px Roboto;
        letter-spacing: 0.4px;
        opacity: 1;
        margin: 8px 0px;
        font-size: 23px;
        background: linear-gradient(to right, #007bff, #6a0dad);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: bold;

        hr {
          width: 55%;
          border-color: #ffffff36;
          height: 1px;
          margin-left: 8px;
        }
      }

      .mt-2 {
        margin-top: 1em;
      }

      .TableHeading {
        font: italic normal 300 12px/28px Roboto;
        letter-spacing: 0.4px;
        color: #253858;
        opacity: 1;
      }

      ul {
        list-style-type: none;

        li {
          font: normal normal 500 12px/18px Roboto;
          letter-spacing: 0.5px;
          color: #253858E5;

          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          opacity: 1;
          width: 100%;
          padding: 3px 0px 5px;
          text-align: left;

          &::before {
            width: 6px;
            height: 6px;
            background-color: #253858;
            content: "";
            display: inline-block;
            border-radius: 14px;
            margin-right: 9px;
          }
        }

        .hidebullet {
          &::before {
            display: none;
            content: "";
          }
        }

        hr {
          border: 1px solid #ffffff36;
          margin: 5px 0px;
        }

        .doneLine {
          text-decoration: line-through;
          opacity: 0.5;
        }

        p {
          color: #253858;
          font-style: normal;
          font-size: 15px;
          font-weight: 500;
        }
      }

      .bottom-section {
        display: flex;
        text-align: center;
        justify-content: space-around;
        position: absolute;
        left: 0;
        right: 0;
        margin: auto;
        bottom: 15px;

        div {
          cursor: pointer;
        }

        img {
          margin: auto auto 3px;
          cursor: pointer;
          width: 48px;
        }

        span {
          text-align: left;
          font: italic normal 500 12px/22px Roboto;
          letter-spacing: 0.4px;
          color: #253858;
          opacity: 1;
        }
      }

      .scrollbar {
        height: 300px;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: #4e53a959;
          border-radius: 10px;
          border: none;
          box-shadow: none;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: #4e53a9;
        }
      }

      .feedbackForm {
        height: 385px;
        overflow-y: auto;
        overflow-x: hidden;

        p {
          font: normal normal 500 12px/17px Roboto;
          letter-spacing: 0.4px;
          color: #FFFFFF;
          opacity: 1;
          margin: 10px 0px 0px;
          -webkit-animation-name: slideInDown;
          animation-name: slideInDown;
          animation-duration: 0.3s;
        }

        .MuiIconButton-label {
          color: #fff;
        }

        .ListItem {
          .MuiFormControlLabel-label {
            font: normal normal 500 12px/20px Roboto;
            letter-spacing: 0.5px;
            color: #FFFFFF;
            opacity: 1;
            -webkit-animation-name: fadeIn;
            animation-name: fadeIn;
            animation-duration: 0.4s;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 214px;
          }
        }

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: #4e53a959;
          border-radius: 10px;
          border: none;
          box-shadow: none;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: #4e53a9;
        }
      }

      @keyframes slideInDown {
        from {
          -webkit-transform: translate3d(0, -100%, 0);
          transform: translate3d(0, -100%, 0);
          visibility: visible;
        }

        to {
          -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
        }
      }

      @-webkit-keyframes fadeIn {
        from {
          opacity: 0;
        }

        to {
          opacity: 1;
        }
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
        }

        to {
          opacity: 1;
        }
      }


    }
  }

  .toggleIcon {
    padding-top: 14px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 18px;
  }

  .agentInfoContainer {
    position: relative;
    display: inline-block;
    margin: 5px 5px 5px 0px;

    .agentInfo {
      padding-left: 10px;
      padding-bottom: 15px;
      text-align: center;

      h3 {
        text-align: center;
        font: normal normal bold 10px/12px Roboto;
        letter-spacing: 0.14px;
        color: #364765;
        opacity: 1;

        .SuperStar {
          display: none;
        }
      }

      img {
        background-color: #fff;
        padding: 5px;
        border-radius: 19px;
      }

      p {
        text-align: center;
        font: normal normal normal 10px/13px Roboto;
        letter-spacing: 0.14px;
        color: #364765;
        opacity: 1;
      }

      .viewProfile {
        border: 0;
        color: #0065ff;
        text-decoration: underline;
        font: normal normal 600 12px/16px Roboto;
        margin: 0px;
        background-color: transparent;
        cursor: pointer;
      }
    }
  }

  .JagGoldSuperStar {
    background: linear-gradient(to right, #d4b426, #ecd670, #f8e073);
    color: #253858;
    border-radius: 3px;
    font: normal normal 600 12px/16px Roboto;
    padding: 5px 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .JagSilverSuperStar {
    background: linear-gradient(to right, #bbc2cc, #dbdde0, #bbc2cc);
    color: #000;
    border-radius: 3px;
    font: normal normal 600 12px/16px Roboto;
    padding: 5px 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .JagHustlerSuperStar {
    background: linear-gradient(to right, #074c00, #42a341, #b8e2a3);
    color: #fff;
    border-radius: 3px;
    font: normal normal 600 12px/16px Roboto;
    padding: 5px 50px;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .warrior {
    background: transparent linear-gradient(270deg, #a30a74 0%, #690e85 52%, #a30a74 100%) 0% 0% no-repeat;
  }
}

.viewProfile {
  border: 0;
  color: #0065ff;
  text-decoration: underline;
  font: normal normal 600 12px/16px Roboto;
  margin: 0px 5px;
  background-color: transparent;
  cursor: pointer;
}

//for open leftmenu agent info
.leftbarMenu {
  .MuiBackdrop-root {
    opacity: 0 !important;
  }

  .MuiDrawer-paper {
    box-shadow: 5px 0px 6px #00000029 !important;
  }

  .leftmenuList {
    padding-left: 8px;
    width: 220px;
    padding-right: 8px;

    .speedTestResult {
      // background: transparent linear-gradient(105deg, #6bd894 0%, #59e6c0 100%)
      //   0% 0% no-repeat padding-box;
      background: transparent linear-gradient(105deg, #43a047 0%, #59e651 100%) 0% 0% no-repeat padding-box;
      border-radius: 5px;
      font: normal normal 10px/21px Roboto;
      letter-spacing: 0.14px;
      color: #ffffff;
      padding: 0 4px;
    }

    .notification-count {
      border-radius: 4px;
      padding: 0 7px;
      background-color: #de350b;
      color: white;
    }

    .MuiListItemIcon-root {
      min-width: 42px;
    }

    .menuTitle {
      span {
        text-align: left;
        font: normal normal normal 12px/21px Roboto;
        letter-spacing: 0px;
        color: #303030;
        opacity: 1;
      }
    }

    .MuiSvgIcon-root {
      font-size: 1rem;
    }

    .MuiListItem-button {
      padding-top: 4px;
      text-align: left;
      font: normal normal normal 12px/21px Roboto;
      letter-spacing: 0px;
      color: #303030;
      opacity: 1;
      margin-bottom: 5px;
      padding-bottom: 4px;

      &:hover {
        background-color: #e7f0ff;
      }
    }

    .MuiListItemText-primary {
      display: block;
      text-align: left;
      font: normal normal normal 12px/21px Roboto;
      letter-spacing: 0px;
      color: #303030;
      opacity: 1;
    }

    .active {
      background-color: #e7f0ff;
      border-radius: 4px;
    }

    .fosIcon {
      width: 22px;
    }

    .fosIconPayOverdue {
      width: 15px;
      filter: opacity(0.7);
    }
  }

  .menuOpenIcon {
    margin-left: 12px;
    margin-top: 14px;
    cursor: pointer;
  }

  .agentInfoSidebarDrawer {
    position: relative;
    display: inline-block;
    margin: 20px 5px 0px;
    border-bottom: 1px solid #f4f4f4;

    .agentInfo {
      padding-bottom: 15px;
      padding-left: 18px;

      img {
        margin-left: 6px;
        border: 1px solid #ddd;
        padding: 4px;
        border-radius: 50px;
      }

      h3 {
        font: normal normal bold 12px/18px Roboto;
        letter-spacing: 0.17px;
        color: #303030;
        opacity: 1;
        text-align: left;
        padding-top: 4px;
        padding-left: 7px;
        display: flex;

        .SuperStar {
          font-family: Roboto;
          font-size: 10px;
          font-weight: 500;
          line-height: 11.72px;
          text-align: left;
          color: #303030;
          margin-left: 6px;
          margin-bottom: 3px;
          padding: 4px 5px;
          border-radius: 20px;
        }

        .gold {
          background: rgba(242, 212, 34, 0.3019607843);
          border: 1px solid rgba(0, 0, 0, 0.2392156863);
        }

        .silver {
          background: #C9C9C94D;
          border: 1px solid #0000003D
        }

        .warrior {
          background: #0853E94D;
          border: 1px solid #0000003D
        }

        .hustler {
          background: #2BC0794D;
          border: 1px solid #0000003D
        }
      }

      p {
        text-align: left;
        font: normal normal normal 12px/13px Roboto;
        letter-spacing: 0.17px;
        color: #303030;
        opacity: 1;
        padding-left: 6px;
      }

      .viewProfile {
        border: 0;
        color: #0065ff;
        text-decoration: underline;
        font: normal normal 600 12px/16px Roboto;
        background-color: transparent;
        cursor: pointer;
        margin: 0px 6px;
      }
    }

    .viewProfile {
      margin: 0px 9px;
    }
  }
}

//
//popups
.AttemptsPopup {
  .MuiDialog-paperWidthSm {
    max-width: 500px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    height: auto;
  }

  .MuiTableContainer-root {
    box-shadow: none;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  .MuiTableCell-head,
  .MuiTableCell-root {
    border-bottom: none;
  }

  .MuiTableCell-root {
    padding: 3px 16px 0px 16px;
  }

  .MuiTableCell-head {
    color: #2e2e2e;
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
  }

  .MuiTableCell-alignRight {
    text-align: center;
  }

  .MuiTableCell-body {
    color: #2e2e2e;
    font-size: 14px;
  }

  p {
    color: #2e2e2e;
    font-size: 14px;
    padding: 16px;
    text-align: left;
  }

  .MuiIconButton-root {
    background-color: #eaeaea;
    position: absolute;
    padding: 5px;

    svg {
      font-size: 1.2rem;
    }
  }
}

.PlanPitchPopupClass {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    flex: 0 0 auto;
    height: auto;
    margin: 0;
    padding: 16px 15px;
    width: 900px;

    .MuiDialogTitle-root {
      padding: 15px !important;
    }
  }
}

.addDetailsPopup {
  .MuiTabs-indicator {
    display: none;
  }

  .MuiCircularProgress-root {
    width: 16px !important;
    height: 16px !important;
  }

  .rejected {
    color: #dd1d1d;
  }

  .Approved {
    color: #19b24d;
  }

  h6 {
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
  }

  .MuiDialogContent-root .MuiIconButton-root {
    position: relative !important;
    background-color: #fff !important;
    padding: 5px !important;
    top: 0 !important;
    right: 0 !important;
  }

  .MuiTab-root {
    min-width: auto;
    text-transform: capitalize;
    margin-right: 35px;
    font: normal normal normal 14px/19px Roboto;
    color: #2e2e2e;
    font-weight: 600;
  }

  // .MuiDialogContent-root {
  //   padding: 2px 10px !important;
  .HighLightMsg {
    background: #ffeaef 0% 0% no-repeat padding-box;
    border: 1px solid #ffafaf;
    border-radius: 8px;
    opacity: 1;
    padding: 5px 15px;
    font: normal normal 600 12px/21px Roboto;
    color: #e06666;
    margin-left: 5px;
    margin-top: 10px;
  }

  // }
  .MuiDialog-paperWidthSm {
    max-width: 631px;
    width: 631px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    height: auto;
  }

  .MuiTab-root.Mui-selected {
    opacity: 1;
    border-bottom: 3px solid #0065ff;
    color: #0065ff;
    border-radius: 2px;
    font-weight: 600;
  }

  .saveButton {
    background: #0065ff;
    border-radius: 8px;
    color: #fff;
    padding: 15px 37px;
    font: normal normal 600 12px Roboto;
    letter-spacing: 0.17px;
    border: none;
    outline: none;
    cursor: pointer;
  }

  .verificationMSG {
    border-radius: 4px;
    background: rgba(24, 194, 0, 0.05);
    display: flex;
    align-items: center;
    padding: 10px;
    color: #18C200;
    font-family: Roboto;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;

    img {
      margin-right: 5px;
    }
  }

  .disablebtnSave {
    background-color: #BDBDBD;
    cursor: default;
  }

  .disablebtn {
    background: #b0b5bd;
    border-radius: 8px;
    color: #fff;
    padding: 14px 65px;
    font: normal normal 12px Roboto;
    letter-spacing: 0.17px;
    border: none;
    outline: none;
    cursor: default;
  }

  .MuiBox-root {
    padding: 20px 5px;
  }

  .MuiFormControl-root {
    width: 100%;
  }

  .MuiTableContainer-root {
    box-shadow: none;
    overflow-x: inherit;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  .MuiTableCell-head,
  .MuiTableCell-root {
    border-bottom: none;
  }

  .MuiTableCell-root {
    padding: 3px 16px 0px 16px;
    text-align: center;
    font-size: 12px;
  }

  .MuiTableCell-root:first-child {
    text-align: left;
  }

  .MuiTableCell-head {
    color: #2e2e2e;
    font-weight: 600;
    font-size: 12px;
    line-height: 22px;
    text-align: center;
  }

  .MuiTableCell-head:first-child {
    text-align: left;
  }

  .MuiTableCell-alignRight {
    text-align: center;
    flex-direction: row-reverse;
  }

  .addPhoneNo {
    .callableBtn {
      text-align: center;
      border: 1px solid #0065ff;
      border-radius: 8px;
      color: #0065ff;
      font: normal normal 12px/21px Roboto;
      padding: 8px 40px;
      background-color: #fff;
      outline: none;
    }

    .MuiTableCell-stickyHeader {
      background-color: #fff;
    }

    *::-webkit-scrollbar,
    *::-webkit-scrollbar-thumb {
      width: 1px;
      border-radius: 13px;
      background-clip: padding-box;
      border: 1px solid transparent;
    }

    *::-webkit-scrollbar-thumb {
      box-shadow: inset 0 0 0 10px;

      :hover {
        color: rgba(0, 0, 0, 0.3);
      }
    }

    .MuiTableContainer-root {
      width: 100%;
      overflow-x: auto;
      overflow-y: auto;
      height: 158px;
    }

    .MuiRadio-colorPrimary.Mui-checked {
      color: #0065ff !important;
    }

    .MuiRadio-root {
      padding: 9px;

      svg {
        font-size: 1.2rem;
      }
    }

    .verificationNoti {
      text-align: left;
      font: normal normal 500 10px/15px Poppins;
      letter-spacing: 0px;
      color: #30722b;
      padding: 5px 10px;
      opacity: 1;
      background: #4bb54333 0% 0% no-repeat padding-box;
      border-radius: 4px;
    }

    .verificationNotiRed {
      text-align: left;
      font: normal normal 500 10px/16px roboto;
      letter-spacing: 0px;
      color: #cc0000;
      padding: 5px 10px;
      opacity: 1;
      background: #f443361f 0% 0% no-repeat padding-box;
      border-radius: 4px;
    }

    .otpverifIcon {
      position: relative;
      top: 21px;
      left: 8px;
      color: #c7c7c7;
    }

    .otpDone {
      position: relative;
      top: 21px;
      left: 0px;
      color: #4bb543;
    }

    .invalid {
      font: normal normal 500 10px/16px roboto;
      letter-spacing: 0px;
      color: #cc0000;
      opacity: 1;
      display: flex;
      align-items: center;

      svg {
        position: relative;
        top: 0px;
        left: 0px;
        color: #cc0000;
        margin-right: 5px;
        font-size: 18px;
      }
    }

    .SendVerificationBtn {
      text-align: center;
      border: 1px solid #0065ff;
      border-radius: 6px;
      color: #0065ff;
      font: normal normal 500 12px/21px Roboto;
      padding: 9.3px;
      background-color: #fff;
      outline: none;
      width: 100%;
      cursor: pointer;
    }

    .verificationTime {
      position: relative;
      padding: 0px;
      cursor: pointer;

      p {
        position: absolute;
        top: 12px;
        right: 5px;
        font: normal normal 600 10px/16px roboto;
        letter-spacing: 0px;
        display: flex;
        color: #0065ff;
        opacity: 1;
        align-items: center;
        cursor: pointer;
      }

      svg {
        font-size: 18px;
        margin-right: 3px;
      }
    }

    .disabledInput {
      background-color: #ddd;
      opacity: 0.5;
      cursor: default;
    }
  }

  .AddAddress {
    height: 450px;
  }

  .clearBtn {
    text-align: center;
    border: 1px solid #0065ff;
    border-radius: 8px;
    color: #0065ff;
    font: normal normal 12px/21px Roboto;
    padding: 10px 65px;
    background-color: #fff;
    outline: none;
    float: right;
  }

  .AddressTitle {
    color: #2e2e2e;
    font-size: 14px;
    font-family: "Roboto";
    line-height: 19px;
    font-weight: bold;
  }

  .AddressDeatils {
    color: #808080;
    line-height: 24px;
    font-size: 14px;
    margin-top: 1em;
    float: left;
  }

  .addressEditIcon {
    border: 1px solid #0065ff;
    color: #0065ff;
    border-radius: 8px;
    float: right;
    padding: 5px 7px 0px 7px;
    margin-top: 1em;
  }

  .Emailtooltip {
    position: relative;
    display: inline-block;
    cursor: pointer;

    svg {
      color: #0065ff;
    }

    .red {
      color: #e33f3f;
    }
  }

  .Emailtooltip .tooltiptext {
    visibility: hidden;
    width: 90px;
    background-color: #808080;
    color: #fff;
    text-align: center;
    border-radius: 4px;
    padding: 0px 0;
    position: absolute;
    z-index: 1;
    bottom: 106%;
    left: 50%;
    margin-left: -45px;
    font: normal normal 11px/21px Roboto;
  }

  .Emailtooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #808080 transparent transparent transparent;
  }

  .Emailtooltip:hover .tooltiptext {
    visibility: visible;
  }
}

/* inbound lead popup*/
.hide {
  display: none;
}

.CallAlert {
  background: #00000066 0% 0% no-repeat padding-box;
  opacity: 1;
  position: fixed;
  margin: auto;
  z-index: 99;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;

  .callAlertSection {
    background: #191f40 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000029;
    border-radius: 24px;
    opacity: 1;
    position: fixed;
    margin: auto;
    z-index: 99;
    height: 256px;
    width: 296px;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 20px;
    -webkit-animation: shake 0.6s ease-in-out 0.6s infinite alternate;

    .CallAlertTitle {
      text-align: center;
      font: normal normal normal 24px/24px Roboto;
      letter-spacing: 0.38px;
      color: #ffffff;
      opacity: 1;
      margin-top: 10px;
      margin-bottom: 10px;
    }

    .incomingCl {
      text-align: center;
      font: normal normal bold 14px/24px Roboto;
      letter-spacing: 0.22px;
      color: #ffffff;
      opacity: 1;
    }

    img {
      transform: translate(100px, 28px);
      margin-bottom: 28px;
      cursor: pointer;
    }

    .answer {
      font: normal normal 500 10px/24px Roboto;
      cursor: pointer;
    }

    p {
      text-align: center;
      font: normal normal bold 12px/24px Roboto;
      letter-spacing: 0.19px;
      color: #fff;
    }
  }


}



/*css for Predictive*/
.PredictiveCallAlert {
  background: #00000066 0% 0% no-repeat padding-box;
  opacity: 1;
  position: fixed;
  margin: auto;
  z-index: 99;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;

  .PredictiveCallAlertSection {
    background: #232323;
    box-shadow: 0px 3px 6px #f7e51929;
    border-radius: 24px;
    opacity: 1;
    position: fixed;
    margin: auto;
    z-index: 99;
    height: 410px;
    width: 680px;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: 10px;
    -webkit-animation: shake 0.6s ease-in-out 0.6s infinite alternate;

    img {
      width: 100%;
      // cursor: pointer;
    }

  }

  @-webkit-keyframes shake {
    0% {
      transform: translate(1px, 1px) rotate(0deg);
    }

    10% {
      transform: translate(-1px, -2px) rotate(-1deg);
    }

    20% {
      transform: translate(-3px, 0px) rotate(1deg);
    }

    30% {
      transform: translate(3px, 2px) rotate(0deg);
    }

    40% {
      transform: translate(1px, -1px) rotate(1deg);
    }

    50% {
      transform: translate(-1px, 2px) rotate(-1deg);
    }

    60% {
      transform: translate(-3px, 1px) rotate(0deg);
    }

    70% {
      transform: translate(3px, 1px) rotate(-1deg);
    }

    80% {
      transform: translate(-1px, -1px) rotate(1deg);
    }

    90% {
      transform: translate(1px, 2px) rotate(0deg);
    }

    100% {
      transform: translate(1px, -2px) rotate(-1deg);
    }
  }

  @keyframes shake {
    0% {
      transform: translate(1px, 1px) rotate(0deg);
    }

    10% {
      transform: translate(-1px, -2px) rotate(-1deg);
    }

    20% {
      transform: translate(-3px, 0px) rotate(1deg);
    }

    30% {
      transform: translate(3px, 2px) rotate(0deg);
    }

    40% {
      transform: translate(1px, -1px) rotate(1deg);
    }

    50% {
      transform: translate(-1px, 2px) rotate(-1deg);
    }

    60% {
      transform: translate(-3px, 1px) rotate(0deg);
    }

    70% {
      transform: translate(3px, 1px) rotate(-1deg);
    }

    80% {
      transform: translate(-1px, -1px) rotate(1deg);
    }

    90% {
      transform: translate(1px, 2px) rotate(0deg);
    }

    100% {
      transform: translate(1px, -2px) rotate(-1deg);
    }
  }
}


/* css for Predictive*/
.addmoreLeadPopup {
  .MuiDialog-paperWidthSm {
    max-width: 1250px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 1250px;
    height: auto;
  }

  .MuiTableCell-root {
    font: normal normal normal 12px/16px Roboto;
    padding: 7px;
    color: #808080;
    border-bottom: none;
  }

  .MuiPaper-elevation1 {
    box-shadow: none;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  h6 {
    text-align: left;
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
    padding-left: 10px;
  }

  .MuiTableRow-root {
    td {
      font: normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      border: none;
    }
  }

  .moreDeatilsData:nth-child(odd) {
    background-color: #f8f8f8;
    border-radius: 4px;
  }

  .MuiIconButton-root {
    background-color: #eaeaea;
    position: absolute;
    padding: 5px;

    svg {
      font-size: 1.2rem;
    }
  }

  .clearAllBtn {
    border: none;
    background: #bcbcbc;
    border-radius: 8px;
    padding: 6px 45px;
    text-align: center;
    font: normal normal 12px/21px Roboto;
    letter-spacing: 0.17px;
    color: #2e2e2e;
    font-weight: 600;
    margin-right: 12px;
    margin-top: 1rem;
    outline: none;
  }

  .addLeadBtn {
    border: none;
    background: #0065ff;
    border-radius: 8px;
    padding: 6px 45px;
    text-align: center;
    font: normal normal 12px/21px Roboto;
    letter-spacing: 0.17px;
    color: #fff;
    font-weight: 600;
    outline: none;
    margin-top: 1rem;
  }

  input[type="checkbox"] {
    position: relative;
    cursor: pointer;
    margin: 0px;
  }

  .content {
    margin-top: 20px;
  }

  input[type="checkbox"]:before {
    content: "";
    display: block;
    position: absolute;
    width: 20px;
    height: 20px;
    top: 0;
    left: -3px;
    background-color: #e9e9e9;
  }

  input[type="checkbox"]:checked:before {
    content: "";
    display: block;
    position: absolute;
    width: 20px;
    height: 20px;
    top: -1px;
    left: -2px;
    background-color: #1e80ef;
  }

  input[type="checkbox"]:checked:after {
    content: "";
    display: block;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    position: absolute;
    top: 2px;
    left: 6px;
  }

  .MuiTooltip-tooltip {
    max-width: 860px;
    padding: 0;
  }

  .excelexport {
    position: absolute;
    top: 40px;
    right: 57px;
  }

  .shortingIcon {
    position: relative;
    width: 93px;

    svg {
      position: absolute;
      top: 2px;
    }
  }
}

.scrollFx {
  .MuiDialogContent-root {
    overflow-y: hidden !important;
  }

  .content {
    margin-top: 20px;
    height: 320px;
    overflow-y: auto;
  }
}

.AgentAssistWFPopup {
  .MuiDialog-paperWidthSm {
    max-width: 930px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 800px;
    height: auto;
  }

  .MuiTableCell-root {
    font: normal normal normal 12px/16px Roboto;
    padding: 7px;
    color: #808080;
    border-bottom: none;
  }

  .MuiPaper-elevation1 {
    box-shadow: none;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  h6 {
    text-align: left;
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
    padding-left: 10px;
  }

  .MuiTableRow-root {
    td {
      font: normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      border: none;
    }
  }

  .moreDeatilsData:nth-child(odd) {
    background-color: #f8f8f8;
    border-radius: 4px;
  }

  .MuiIconButton-root {
    background-color: #eaeaea;
    position: absolute;
    padding: 5px;

    svg {
      font-size: 1.2rem;
    }
  }

  input[type="checkbox"] {
    position: relative;
    cursor: pointer;
    margin: 0px;
  }

  input[type="checkbox"]:before {
    content: "";
    display: block;
    position: absolute;
    width: 20px;
    height: 20px;
    top: 0;
    left: -3px;
    background-color: #e9e9e9;
  }

  input[type="checkbox"]:checked:before {
    content: "";
    display: block;
    position: absolute;
    width: 20px;
    height: 20px;
    top: -1px;
    left: -2px;
    background-color: #1e80ef;
  }

  input[type="checkbox"]:checked:after {
    content: "";
    display: block;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    position: absolute;
    top: 2px;
    left: 6px;
  }

  label {
    margin-left: 15px;
  }
}

.ticketPopup {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 900px;
    height: 550px !important;

    .MuiDialogContent-root {
      padding: 0px;
    }

    iframe {
      border: none;

      body {
        background-color: #fff !important;
        margin: 0px !important;
      }
    }
  }
}

.proposalDetails {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 90%;
    height: 850px !important;

    .MuiDialogContent-root {
      padding: 0px;
      overflow-y: hidden;
    }

    iframe {
      border: none;

      body {
        background-color: #fff !important;
        margin: 0px !important;
      }
    }
  }
}

.addcarPopup {
  width: 900px;

  h5 {
    font: normal normal bold 14px/19px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    margin-bottom: 10px;
    padding-left: 5px;
  }

  .editBtn {
    border: none;
    border-radius: 8px;
    background-color: transparent;
    color: #0065ff;
    letter-spacing: 0.17px;
    padding: 8px;
    font: normal normal 600 12px/21px Roboto;
    cursor: pointer;
    outline: none;
  }

  .clearBtn {
    text-align: center;
    border: 1px solid #0065ff;
    border-radius: 8px;
    color: #0065ff;
    font: normal normal 12px/21px Roboto;
    padding: 10px 65px;
    background-color: #fff;
    outline: none;
    width: 45%;
    margin-bottom: 25px;
  }

  .saveBtn {
    margin-left: 11px;
    border: none;
    background: #0065ff;
    border-radius: 8px;
    padding: 6px 45px;
    text-align: center;
    font: normal normal 12px/21px Roboto;
    letter-spacing: 0.17px;
    color: #fff;
    font-weight: 600;
    width: 45%;
    outline: none;
    margin-right: 16px;
    margin-bottom: 25px;
  }

  .MuiTableCell-root {
    font: normal normal normal 12px/16px Roboto;
    padding: 7px;
    color: #808080;
    border-bottom: none;
  }

  .MuiPaper-elevation1 {
    box-shadow: none;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  .MuiTableRow-root {
    td {
      font: normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      border: none;
    }
  }

  .moreDeatilsData:nth-child(odd) {
    background-color: #f8f8f8;
    border-radius: 4px;
  }
}

.rejectLeadPopop {
  width: 504px;

  p {
    font: normal normal bold 14px/19px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    margin-top: 20px;
    margin-left: 44px;
  }

  .PitchText {
    margin-top: 5px;
    margin-left: 0px;
  }

  button {
    border: none;
    background: #0065ff;
    border-radius: 8px;
    padding: 8px 45px;
    text-align: center;
    font: normal normal 12px/21px Roboto;
    letter-spacing: 0.17px;
    color: #fff;
    font-weight: 600;
    outline: none;
    margin-top: 2rem;
  }

  .MuiAutocomplete-popupIndicator,
  .MuiAutocomplete-clearIndicator {
    position: static !important;
    margin: 0px !important;
    background-color: transparent !important;
    color: #252538;
  }

  input[type="checkbox"] {
    position: relative;
    cursor: pointer;
    margin: 10px;
  }

  input[type="checkbox"]:before {
    content: "";
    display: block;
    position: absolute;
    width: 20px;
    height: 20px;
    top: 0;
    left: -2px;
    background-color: #e9e9e9;
    border: #546e7a 1px solid;
  }

  input[type="checkbox"]:checked:before {
    content: "";
    display: block;
    position: absolute;
    width: 20px;
    height: 20px;
    top: -1px;
    left: -2px;
    background-color: #1e80ef;
    border: #1e80ef 1px solid;
  }

  input[type="checkbox"]:checked:after {
    content: "";
    display: block;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    position: absolute;
    top: 2px;
    left: 6px;
  }

  label {
    font: normal normal normal 12px/16px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    padding-left: 0px;
  }

  .covidCaseDate {
    button {
      background-color: transparent;
      position: static;
      margin: 0px;
    }

    .MuiIconButton-root {
      color: #808080 !important;
    }
  }

  textarea {
    border: 1px solid #808080;
    border-radius: 4px;
    width: 100%;
    font-size: 14px;
    padding: 10px 75px 10px 10px;
    outline: none;
    resize: none;
    font-family: "Roboto";
    letter-spacing: 0.22px;
    height: 48px !important;

  }

  .TextCount {
    font-size: 12px;
    position: relative;
    top: -3px
  }

}

.paymentFailedPopup {
  h6 {
    color: #f53535 !important;
    background-color: antiquewhite;
    padding: 10px;
    border-radius: 5px;
  }

  table {
    th {
      text-align: center;
      font-size: 13px;
      font-weight: 600;
      padding: 13px 28px;
      background-color: #d1d1d1;
    }

    tr td {
      text-align: center;
      font-size: 13px;
      padding: 13px 28px;
    }
  }
}

#customer-profile {
  left: -16px;
  width: 90em;
  float: left;
  background-color: #eff7fd;
  position: relative;
  top: -19px;

  .column-left {
    width: 100%;
    float: left;
    background-color: #eff7fd;
    box-sizing: border-box;
    padding: 0px 20px;

    .termlife-btn {
      float: left;
      clear: both;
      padding: 8px 15px;
      color: #0065ff;
      border-radius: 24px;
      background-color: white;
      border: none;
      outline: none;
      font-size: 14px;
      line-height: 22px;
      margin-top: 5px;

      img {
        width: 24px;
        float: left;
        margin-right: 8px;
      }
    }

    h4 {
      margin-top: 20px;
      font-size: 16px;
      color: #324462;
      font-family: roboto;
      margin-bottom: 0px;
    }
  }

  .column-right {
    width: 100%;
    float: left;
    background-color: white;
    box-sizing: border-box;
    padding: 0px 20px;
    height: 542px;
    overflow-y: auto;
  }

  h4 {
    font-size: 14px;
    margin-top: 20px;
  }

  h3 {
    color: #303030;
    font-size: 16px;
    font-family: roboto;
    margin: 25px 0px 0px;
    float: left;
    width: 100%;
  }

  hr {
    width: 44px;
    height: 2px;
    background-color: #0065ff;
    display: inherit;
    border: 1px solid #0065ff;
    border-radius: 5px;
    float: left;
  }

  label {
    color: #808080;
    font-size: 12px;
    font-family: roboto;
    letter-spacing: 0.22px;
  }

  .bg-image {
    position: absolute;
    bottom: 0px;
    left: 15px;
  }
}

.callTrasnferPopup {
  width: 900px;
}

.EditPopup {
  width: 504px;
  margin-top: 8px;

  .MuiFormControl-root {
    width: 100%;
  }

  .saveButton {
    background: #0065ff 0% 0% no-repeat padding-box;
    border-radius: 8px;
    color: #fff;
    padding: 14px 65px;
    font: normal normal 12px Roboto;
    letter-spacing: 0.17px;
    border: none;
    outline: none;
  }

  .SwitchInput {
    justify-content: space-between;
    padding: 10px 0 0;
    font-size: 12px;
    display: flex;
    list-style-type: none;
    flex-wrap: wrap;
    width: 100%;
    letter-spacing: 0.17px;

    div:nth-child(odd) {
      width: 50%;
      font: normal normal normal 12px/37px Roboto;
      color: #303030;
    }

    div:nth-child(even) {
      color: #253858;
      white-space: nowrap;
      font: normal normal 600 12px/21px Roboto;
      letter-spacing: 0.17px;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 50%;
      text-align: right;
    }

    .MuiIconButton-root {
      top: 0;
      left: 0;
      right: auto;
      background-color: unset;
      padding: 9px;
    }

    .MuiSwitch-track {
      background: #eaeaea !important;
    }

    .MuiSwitch-thumb {
      background-color: #808080;
    }

    .Mui-checked {
      .MuiSwitch-thumb {
        background-color: #0065ff;
      }
    }
  }

  .MuiIconButton-root {
    position: static !important;
    background-color: transparent !important;
  }
}

.viewAllCommentsPopup {
  width: 504px;

  .comment-text {
    width: 100%;
    background: #e7f0ff 0% 0% no-repeat padding-box;
    border-radius: 4px 16px 16px 16px;
    padding: 12px 15px;
    margin-top: 5px;
    margin-bottom: 2px;
    letter-spacing: 0.17px;

    p {
      text-align: left;
      font: normal normal 600 12px/15px Roboto;
      letter-spacing: 0.17px;
      color: #364765;
      opacity: 1;
    }

    small {
      font: normal normal 600 10px/21px Roboto;
      letter-spacing: 0.14px;
      color: #808080;
      opacity: 1;
    }

    .commentmsg {
      text-align: left;
      font: normal normal normal 12px/18px Roboto;
      letter-spacing: 0.17px;
      color: #253858;
      opacity: 1;
      margin-top: 5px;
      word-break: break-word;
    }
  }
}

.addnew-form-wrapper {
  width: 504px;

  .goBtn {
    background: #0065ff 0% 0% no-repeat padding-box;
    border-radius: 8px;
    color: #fff;
    padding: 14px 65px;
    font: normal normal 12px Roboto;
    letter-spacing: 0.17px;
    border: none;
    outline: none;
  }
}

.AnalyticsPopup {
  // position: fixed;
  // background-color: #ffffff;
  // border-radius: 0px 16px 16px 0px;
  // height: 100%;
  // width: 202px;
  // box-shadow: 0px 0px 16px #00000014;
  // padding: 15px;
  // z-index: 1101;
  // overflow-y: auto;

  width: 572px;

  .timeDuration {
    background: #eff3f6 0% 0%;
    border-radius: 8px;
    padding: 12px 15px;
    margin-bottom: 1em;

    h2 {
      text-align: left;
      font: normal normal normal 24px Roboto;
      letter-spacing: 0.34px;
      color: #253858;
      margin-bottom: 3px;
    }

    p {
      text-align: left;
      font: normal normal 12px Roboto;
      letter-spacing: 0.17px;
      color: #808080;
      padding-bottom: 5px;
    }

    .userStatRed {
      text-align: left;
      font: normal normal bold 12px Roboto;
      letter-spacing: 0.17px;
      color: #e06666;
    }

    .userStatGreen {
      text-align: left;
      font: normal normal bold 12px Roboto;
      letter-spacing: 0.17px;
      color: #32cd32;
    }

    span {
      svg {
        font-size: 18px;
        position: relative;
        top: 3px;
        left: -2px;
      }
    }
  }

  .knowmore {
    margin-left: 9px;
    display: flex;
    align-items: center;
    color: #0065ff;
    cursor: pointer;

    svg {
      font-size: 12px;
    }
  }
}

.LogoutPopop {
  width: 432px;

  .reasonPoint {
    background: #eff3f6;
    border-radius: 16px;
    width: 100%;
    float: left;
    padding: 18px 25px;

    span {
      text-align: left;
      font: normal normal bold 14px/19px Roboto;
      float: left;
      padding-top: 14px;
      letter-spacing: 0px;
      color: #2e2e2e;
    }

    img {
      float: right;
    }
  }

  button {
    background: #0065ff;
    border-radius: 8px;
    color: #fff;
    padding: 14px 65px;
    font: normal normal 12px Roboto;
    letter-spacing: 0.17px;
    border: none;
    outline: none;
  }
}

.continueJourneyPopup {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 1090px;
    height: 600px !important;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      max-height: 600px;
      padding: 0px;

      .popupWrapper {
        background-color: #eff7fd;
        height: 600px;
      }
    }

    iframe {
      border: none;

      body {
        background-color: #fff;
        margin: 0px !important;
      }
    }
  }
}


.newcontinueJourneyPopup {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 500px;
    height: 150px !important;
    padding-bottom: 0px !important;
    max-height: calc(100% - 20px);

    .MuiDialogContent-root {
      padding: 0px !important;

      .column-left {
        width: 30%;
        float: left;
        background-color: #EFF7FD;
        box-sizing: border-box;
        padding: 0px 5px 0px 20px;
      }

      .column-left h3,
      .column-right h3 {
        color: #303030;
        font-size: 16px;
        font-family: roboto;
        margin: 20px 0px 0px;
        width: 100%;

      }

      .bg-image {
        position: relative;
        width: 200px;
        float: left;
        margin-top: 176px;
      }

      .bg-image img {
        position: absolute;
        width: 100%;
        left: 60px;
        bottom: 0px;
      }

      .column-right {
        width: 70%;
        float: left;
        background-color: white;
        box-sizing: border-box;
        padding: 0px 10px 0px 20px;
        border-radius: 0px;
      }

      .continue-journey {
        background-color: #0065FF;
        outline: none;
        border: none;
        padding: 12px 50px;
        margin-left: 10px;
        color: #fff;
        border-radius: 8px;
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 0px;
        cursor: pointer;
      }

      .continue-journey_disable {
        background-color: #0065FF;
        outline: none;
        border: none;
        padding: 12px 50px;
        margin-left: 10px;
        color: white;
        border-radius: 8px;
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 0px;
        opacity: 0.5;
      }

    }
  }

  .popup {
    width: 33.333333%;
    padding: 15px;
    left: 0;
    margin-left: 33.333333%;
    border: 1px solid #ccc;
    border-radius: 10px;
    background: white;
    position: absolute;
    top: 15%;
    box-shadow: 5px 5px 5px #000;
    z-index: 10001;
  }

  .overlay {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, .85);
    z-index: 10000;
  }

}

.uploadDocPopUp {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 900px;
    height: 600px !important;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      max-height: 600px;
      padding: 0px;

      .popupWrapper {
        background-color: #eff7fd;
        height: 600px;
      }
    }

    iframe {
      border: none;

      body {
        background-color: #fff;
        margin: 0px !important;
      }
    }
  }
}

.QualityCriteriaPopUp {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 900px;
    height: 600px !important;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      max-height: 600px;
      padding: 0px;
      overflow-y: hidden;

      .popupWrapper {
        background-color: #eff7fd;
        height: 600px;
      }
    }

    iframe {
      border: none;

      body {
        background-color: #fff;
        margin: 0px !important;
      }
    }
  }
}

.callGalleryPopup {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 1200px;
    height: 525px !important;
    overflow: hidden;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      height: 525px !important;
      overflow: hidden;
      padding-bottom: 0px !important;

      .popupWrapper {
        background-color: #eff7fd;
        height: 500px;
      }
    }

    iframe {
      border: none;

      body {
        background-color: #fff;
        margin: 0px !important;
      }

      .table-responsive {
        overflow: scroll;
        padding-bottom: 10px;
        height: 439px;
      }

      footer {
        display: none;
      }
    }
  }
}

.whatsappchatSection {
  .MuiBox-root {
    padding: 0px 15px !important;
  }

  .MuiTabs-flexContainer {
    margin-left: 0.5em !important;
    border-bottom: 1px solid #ddd;
  }

  h4 {
    text-align: left;
    font: normal normal bold 14px/19px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    margin-top: 1em;
  }

  p {
    font: normal normal normal 12px/21px Roboto;
    letter-spacing: 0.17px;
    color: #253858;
    text-align: left;
    float: left;
    margin-bottom: 2em;
  }
}

.SMEPopup {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 10px 19px rgb(62 57 57);
    border-radius: 16px;
    width: 1302px;
    height: 600px !important;
    max-height: calc(100% - 73px) !important;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      padding: 0px !important;
      overflow: hidden;
    }
  }
}

.MuiTooltip-tooltip {
  box-shadow: 0px 3px 8px #0065ff29;

  .MuiPaper-elevation1 {
    border: 1px solid #ddd;
  }

  .MuiTableHead-root {
    background-color: #e0e0e0;

    .MuiTableCell-root {
      color: #2e2e2e;
      font-weight: 600;
    }
  }
}

.ticketSummary {
  background: #ffeaef 0% 0% no-repeat padding-box;
  border: 1px solid #ffafaf;
  border-radius: 8px;
  opacity: 1;
  padding: 15px;
  margin-bottom: 15px;

  h3 {
    text-align: left;
    font: normal normal 600 16px/24px Roboto;
    letter-spacing: 0.26px;
    color: #253858;
    opacity: 1;
  }

  h4 {
    font-size: 12px;
    color: #546e7a;
  }

  .expandmoreIcon {
    float: right;
    position: relative;
    top: -24px;
    right: 2px;

    svg {
      color: #ff0606;
    }
  }

  span {
    color: #ff0606;

    a {
      text-align: right;
      font: normal normal 600 12px/16px Roboto;
      letter-spacing: 0px;
      color: #0075ff;
      opacity: 1;

      svg {
        font-size: 14px;
        position: relative;
        top: 4px;
      }
    }
  }

  p {
    text-align: left;
    font: normal normal normal 14px/22px Roboto;
    letter-spacing: 0px;
    color: #253858;

    img {
      position: relative;
      top: 6px;
    }
  }
}

.ticketPanel {
  padding: 12px;

  h3 {
    text-align: left;
    font: normal normal 600 16px/24px Roboto;
    letter-spacing: 0.26px;
    color: #253858;
    opacity: 1;
    margin-bottom: 10px;
  }

  .MuiPaper-elevation1 {
    box-shadow: none;

    &::before {
      display: none;
    }
  }

  .ticketheading {
    border-bottom: 1px solid #3469cb29;
    text-align: left;
    font: normal normal 600 14px/24px Roboto;
    letter-spacing: 0.22px;
    color: #253858;
    opacity: 1;
    min-height: 45px !important;
    height: 50px;
    padding: 0px;

    .MuiAccordionSummary-content.Mui-expanded {
      margin: 0px !important;
    }

    .MuiAccordionSummary-content {
      align-items: center;

      img {
        margin-right: 12px;
      }
    }

    .MuiIconButton-edgeEnd {
      color: #0065ff;
    }
  }

  .MuiAccordion-root.Mui-expanded {
    background: #f8fbff 0% 0% no-repeat padding-box;
    border: 1px solid #d7e8ff;
    border-radius: 8px;
    opacity: 1;
    padding: 0px 5px;
  }

  .ticketContent {
    margin: 10px 5px;
    box-shadow: 0px 6px 16px #3469cb29;
    border-radius: 8px;
    opacity: 1;
    background-color: #fff;
    display: block;
    padding: 0px;

    .productIcon {
      width: 100%;
      background: #eff3ff;
      height: 40px;
      display: flex;
      align-items: center;
      border-radius: 10px;
      justify-content: space-around;

      img {
        width: 90%;
      }
    }

    .prodcutName {
      text-align: left;
      font: normal normal normal 8px/13px Roboto;
      letter-spacing: 0px;
      color: #919bab;
      opacity: 1;
    }

    .pName {
      text-align: left;
      font: normal normal bold 12px/17px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 1;
      margin-left: 7px;
    }

    .pNumber {
      text-align: left;
      font: normal normal normal 11px/16px Roboto;
      letter-spacing: 0px;
      color: #253858;
      margin-left: 7px;
      opacity: 0.8;
    }

    .Itemlabel {
      text-align: left;
      font: normal normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #919bab;
      opacity: 1;
      margin-left: 7px;
    }

    .ItemDes {
      font: normal normal normal 12px/17px Roboto;
      text-align: left;
      color: #253858;
      margin-left: 7px;
    }

    .pd10 {
      padding: 10px;
    }

    .footerBox {
      border-radius: 0px 0px 8px 8px;
      opacity: 1;
      padding: 8px 12px;
      background-color: #fffbed;
      margin-top: 10px;

      .inProgress {
        font: normal normal 600 12px/16px Roboto;
        color: #ff900b;

        svg {
          width: 18px;
          margin-right: 4px;
        }
      }

      .cusPending {
        font: normal normal normal 12px/16px Roboto;
        color: #172b4d;
        padding-top: 5px;
        justify-content: center;
      }

      p {
        display: flex;
        align-items: center;
        color: #0065ff;
        cursor: pointer;
        font: normal normal normal 12px/16px Roboto;
        justify-content: center !important;

        svg {
          width: 18px;
          margin-right: 4px;
        }
      }
    }

    .bluebg {
      background-color: #cfdffa;

      p {
        justify-content: center;
      }
    }

    .greenbg {
      background-color: #e3fcef;

      .green {
        color: #19b24d;
        justify-content: flex-start;
      }

      p {
        justify-content: flex-end;
      }
    }
  }

  .MuiAccordionDetails-root {
    display: block;
  }

  .NodataText {
    font: normal normal normal 16px/24px Roboto;
    letter-spacing: 0.26px;
    color: #253858;
    opacity: 0.8;
  }
}

.HouseWifeInsurance {
  padding: 10px;

  h4 {
    text-align: center;
    font: normal normal 500 20px/24px Roboto;
    letter-spacing: 0.32px;
    color: #253858;
    margin-bottom: 18px;
  }

  .itemBox {
    padding: 10px 7px 8px;
    border-radius: 4px;
    background-color: #f8f8f8;
    text-align: center;
    margin-bottom: 8px;

    p {
      text-align: left;
      font: normal normal normal 12px/20px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      opacity: 1;
    }
  }

  .HIDisabled {
    background-color: #ddd !important;
    color: #00000094 !important;
    cursor: inherit !important;
    border: none !important;
    padding: 5px 20px !important;
    font: normal normal normal 12px/21px Roboto;
    border-radius: 8px;
  }

  .Nobtn {
    border: 1px solid #0065ff;
    border-radius: 8px;
    background-color: transparent;
    color: #0065ff;
    letter-spacing: 0.17px;
    padding: 4px 20px !important;
    font: normal normal normal 12px/21px Roboto;
    cursor: pointer;
    outline: none;
  }

  button {
    width: 90px !important;
    padding: 5px 20px !important;
    margin: 10px 10px 5px !important;
  }
}

.logPopup-Mobileview {
  .MuiDrawer-paperAnchorRight {
    width: 99%;

    .content {
      height: 99%;

      .checklistPopup {
        padding: 20px;
        height: calc(100vh - 100px);
        overflow: auto;
        max-height: calc(100vh - 100px);

        h3 {
          font: normal normal bold 14px/16px Roboto;
          letter-spacing: 0.2px;
          color: #303030;
          text-align: left;
          margin: 1em 0px 2em 0px;
        }

        div {
          width: 100%;
          margin-bottom: 20px;
          display: inline-block;

          input[type="checkbox"] {
            position: relative;
            cursor: pointer;
            margin: 0px;
            float: left;
          }

          input[type="checkbox"]:before {
            content: "";
            display: block;
            position: absolute;
            width: 18px;
            border-radius: 10px;
            height: 18px;
            top: -3px;
            left: -3px;
            background-color: white;
            border: 1px solid #707070;
          }

          input[type="checkbox"]:checked:before {
            content: "";
            display: block;
            position: absolute;
            width: 18px;
            border: 1px solid #0065ff;
            height: 18px;
            top: -3px;
            left: -3px;
            background-color: #e7f0ff;
          }

          input[type="checkbox"]:checked:after {
            content: "";
            display: block;
            width: 8px;
            height: 16px;
            border: solid #0065ff;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
            position: absolute;
            top: -6px;
            left: 6px;
          }

          label {
            text-align: left;
            float: left;
            margin-left: 10px;
            font: normal normal normal 12px/16px Roboto;
            letter-spacing: 0.17px;
            color: #303030;
            width: 90%;
          }
        }
      }

      .checklist-save-btn {
        background: #0065ff;
        border-radius: 8px;
        width: auto;
        margin-top: 35px;
        letter-spacing: 0.17px;
        padding: 8px 40px;
        border: none;
        color: #ffffff;
        font: normal normal normal 12px/21px Roboto;
        cursor: pointer;
        outline: none;
        margin: 20px 0;
      }

      .notesPopup {
        padding: 12px;

        textarea {
          background: #fafafa;
          border: 1px solid #eaeaea;
          border-radius: 8px;
          width: 100%;
          padding: 10px;
          height: 250px !important;
          outline: none;
        }

        button {
          background: #0065ff;
          border-radius: 8px;
          width: 90%;
          margin-top: 35px;
          letter-spacing: 0.17px;
          padding: 8px;
          border: none;
          color: #ffffff;
          font: normal normal 12px/21px Roboto;
          cursor: pointer;
          outline: none;
        }
      }

      .MuiBox-root {
        padding: 0px;
      }
    }
  }
}

.RenewalStatusPopup {
  .comboPolicyPopup {
    width: 410px;

    div {
      display: flex;
      justify-content: flex-start;
      margin-top: 25px;

      button {
        background: #0065ff 0% 0% no-repeat padding-box;
        border-radius: 4px;
        opacity: 1;
        outline: none;
        font: normal normal normal 14px/21px roboto;
        letter-spacing: 0px;
        color: #ffffff;
        border: none;
        margin-right: 20px;
        padding: 4px 30px;
        cursor: pointer;
      }
    }
  }
}

.RenewalMessageStatusPopup {
  .comboPolicyPopup {
    width: 530px;

    div {
      display: flex;
      justify-content: flex-start;
      margin-top: 25px;
    }
  }
}

.PotentialBuyerPopUp {
  h4 {
    margin-top: 10px;
  }

  div {
    display: flex;
    justify-content: center;
    margin-top: 25px;

    button {
      background: #0065ff 0% 0% no-repeat padding-box;
      border-radius: 4px;
      opacity: 1;
      outline: none;
      font: normal normal normal 14px/21px roboto;
      letter-spacing: 0px;
      color: #ffffff;
      border: none;
      margin-right: 20px;
      padding: 4px 30px;
      cursor: pointer;
    }

    .NoButton {
      border: 1px solid #0065FF;
      background-color: #fff !important;
      color: #0065FF !important;
      font-weight: 600;
    }
  }
}

.EMIPendingPopup {
  input[type="checkbox"] {
    margin: 16px 0px 21px 15px;
  }

  input[type="checkbox"]:before {
    border-radius: 2px;
  }

  input[type="checkbox"]:checked:after {
    top: 1px;
    left: 5px;
  }

  .caption {
    display: flex;
    padding: 1px 0px;
    align-items: center;
    justify-content: space-between;

    p {
      background: #f5f5f5 0% 0% no-repeat padding-box;
      font: normal normal normal 10px/16px roboto;
      letter-spacing: 0px;
      color: #9b9b9b;
      opacity: 1;
      display: flex;
      padding: 1px 10px;
      align-items: center;

      svg {
        margin-right: 8px;
        color: #9b9b9b;
        width: 18px;
      }
    }

    .switchtogle {
      // background: #f9fbfc 0% 0% no-repeat padding-box;
      // border-radius: 8px;
      // opacity: 1;
      // display: flex;

      // align-items: center;
      padding: 0px 15px;
      // span {
      //   text-align: left;
      //   font: normal normal 600 14px/12px roboto;
      //   letter-spacing: 0px;
      //   color: #000000;
      //   opacity: 0.6;
      // }

      // .active {
      //   text-align: left;
      //   font: normal normal 600 14px/12px roboto;
      //   letter-spacing: 0px;
      //   color: #0065ff;
      //   opacity: 1;
      // }
    }

    .switch {
      position: relative;
      display: inline-block;
      width: 30px;
      height: 19px;
      margin: 10px 14px;

      .toggle {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #0065ff;
        -webkit-transition: 0.4s;
        transition: 0.4s;

        &:before {
          position: absolute;
          content: "";
          height: 10px;
          width: 11px;
          left: 4px;
          bottom: 5px;
          background-color: white;
          transition: 0.4s;
        }
      }

      .toggle:checked+.slider {
        background-color: #0065ff;
      }

      .toggle:focus+.slider {
        box-shadow: 0 0 1px #0065ff;
      }

      .toggle:checked+.slider:before {
        -webkit-transform: translateX(10px);
        -ms-transform: translateX(10px);
        transform: translateX(10px);
      }

      /* Rounded sliders */
      .slider.round {
        border-radius: 34px;
      }

      .slider.round:before {
        border-radius: 50%;
      }
    }
  }

  .filterBtn {
    margin-top: 10px;
    padding: 0px 15px;

    ul {
      list-style-type: none;
      display: flex;

      li {
        border: 1px solid #174cff;
        border-radius: 15px;
        margin-right: 15px;
        padding: 4px 18px;
        text-align: center;
        font: normal normal 500 14px/16px Roboto;
        letter-spacing: 0px;
        color: #0065ff;
        opacity: 1;
        cursor: pointer;
      }

      .activeBtn {
        background-color: #0065ff;
        color: #fff;
      }

      .disabledBtn {
        background-color: #cfcece;
        color: #857f7f;
        border: 1px solid #cfcece;
        cursor: default;
      }
    }
  }

  .footerPaymentOverdue {
    position: sticky;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px;
    background-color: #f8f8f8;

    .clearAllBtn {
      margin-top: 0rem !important;
    }

    .addLeadBtn {
      margin-top: 0rem !important;
    }
  }

  .MuiDialogTitle-root {
    padding: 15px 16px 5px;
  }

  .mulitiplepolicy {
    width: 100%;
    display: contents;
    flex-wrap: wrap;
    background-color: #f9fbfc;
    position: relative;

    .comboLine {
      position: relative;

      .vLine {
        position: absolute;
        border: 1px solid #e8e8e8;
        width: 1px;
        height: 35px;
        left: 28px;
        top: -22px;
      }

      .hLine {
        position: absolute;
        border: 1px solid #e8e8e8;
        width: 22px;
        height: 1px;
        top: 11px;
        left: 28px;
      }
    }

    .comboPolicy {
      position: relative;

      p {
        color: #0065ff;
        font: normal normal 500 11px/11px Roboto;
        display: flex;
        align-items: center;
        position: absolute;
        margin-bottom: 10px;
        width: 100px;
        padding: 3px 0px 10px 10px;
        cursor: pointer;

        svg {
          width: 10px;
          height: 10px;
          margin-right: 6px;
        }
      }
    }

    tr {
      background: #f9fbfc 0% 0% no-repeat padding-box;
    }
  }

  .disabledmulitiplepolicy {
    width: 100%;
    display: contents;
    flex-wrap: wrap;
    background-color: #f9fbfc;
    position: relative;

    .comboLine {
      position: relative;

      .vLine {
        position: absolute;
        border: 1px solid #e8e8e8;
        width: 1px;
        height: 35px;
        left: 28px;
        top: -22px;
      }

      .hLine {
        position: absolute;
        border: 1px solid #e8e8e8;
        width: 22px;
        height: 1px;
        top: 11px;
        left: 28px;
      }
    }

    .comboPolicy {
      position: relative;

      p {
        color: #0065ff;
        font: normal normal 500 11px/11px Roboto;
        display: flex;
        align-items: center;
        position: absolute;
        margin-bottom: 10px;
        width: 100px;
        padding: 3px 0px 10px 10px;
        cursor: pointer;

        svg {
          width: 10px;
          height: 10px;
          margin-right: 6px;
        }
      }
    }

    tr {
      background: #e1e1e1 0% 0% no-repeat padding-box;
    }
  }

  .MuiTableRow-root td {
    font-weight: 600;
    padding: 1px 7px;
  }

  .MuiTableHead-root {
    background: #f9fbfc 0% 0% no-repeat padding-box;

    .MuiTableCell-root {
      padding: 15px 7px;
    }
  }

  .noBtn {
    width: 40px;
    background-color: #ffe0e6;
    cursor: pointer;
    color: #c35269;
    text-align: center;
    font: normal normal 500 12px/16px Roboto;
    padding: 2px 6px;
    border-radius: 3px;
  }

  .yesBtn {
    background-color: #d3f4ea;
    cursor: pointer;
    width: 40px;
    color: #01b48d;
    font: normal normal 500 12px/16px Roboto;
    padding: 2px 6px;
    border-radius: 3px;
    text-align: center;
  }

  .FaultyDueDate {
    color: #ff0000;
  }

  .NDueDate {
    color: #000000;
  }

  .PDueDate {
    color: #007600;
  }

  .iconimg {
    cursor: pointer;
  }

  .mr {
    margin-right: 15px;
  }

  .filterIcon {
    position: relative;
    cursor: pointer;

    svg {
      position: absolute;
      top: -2px;

      &:last-child {
        top: -8px;
      }
    }
  }

  .comboPolicyPopup {
    width: 410px;

    span {
      text-align: left;
      font: normal normal normal 15px/20px roboto;
      letter-spacing: 0px;
      color: #000000;
      opacity: 0.6;
    }

    h3 {
      text-align: left;
      font: normal normal 600 18px/27px roboto;
      letter-spacing: 0px;
      color: #000000;
      opacity: 1;
      margin-top: 5px;
    }

    div {
      display: flex;
      justify-content: flex-start;
      margin-top: 25px;

      button {
        background: #0065ff 0% 0% no-repeat padding-box;
        border-radius: 4px;
        opacity: 1;
        outline: none;
        font: normal normal normal 14px/21px roboto;
        letter-spacing: 0px;
        color: #ffffff;
        border: none;
        margin-right: 20px;
        padding: 4px 30px;
        cursor: pointer;

        &:last-child {
          color: #c10000 !important;
          background: #fff;
          padding: 4px 10px;
        }
      }
    }
  }
}

/* Common css of all Popup*/

.commanPopop {
  h6 {
    text-align: left;
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
    padding-left: 10px;
  }

  .MuiDialog-paperWidthSm {
    max-width: 100em;
    min-width: 300px;
    border-radius: 16px;
    height: auto;
    padding-bottom: 10px;
  }

  .MuiIconButton-root {
    background-color: #eaeaea;
    position: absolute;
    padding: 5px;
    right: 10px;
    top: 10px;
    z-index: 999;

    svg {
      font-size: 1.2rem;
    }
  }

  .transferbtn {
    background: #0065ff;
    border-radius: 8px;
    color: #fff;
    padding: 14px 42px;
    font: normal normal 12px Roboto;
    letter-spacing: 0.17px;
    border: none;
    outline: none;
    margin-top: 13px;
  }
}

/* end css*/
.viewport {
  .MuiIconButton-root {
    background-color: unset;
    position: inherit;
    padding: 5px;
    right: 0;
    top: 0;

    svg {
      font-size: 1.2rem;
    }
  }

  input {
    left: 40px;
  }

  .skipBtn {
    color: #0065ff;
    cursor: pointer;
    margin-left: 10px;
    font-weight: 600;
  }
}

/* end css*/
.AgentStories {
  .MuiDialog-paperWidthSm {
    padding-bottom: 0px;
    height: auto;

    .MuiDialogContent-root {
      width: 750px; 
      padding: 0px !important;
      background-color: #000;

      .slick-slide {
        position: static !important;

        div {
          height: 580px;
          text-align: center;

          // display: flex;
          // align-items: center;
          video {
            background-color: #000;
          }

          img {
            width: 750px !important; 
            height: 100%;
            object-fit: contain;
            background-color: #000;
          }
        }

        .TextMsgUi {
          position: relative;

          span {
            img {
              width: 200px !important;
              height: auto;
              background-color: #fff;
              border-radius: 8px 8px 8px 0px;
              padding: 10px;
            }

            .TestMSg {
              padding: 15px;
              color: #fff;
              font: normal normal 16px/24px Roboto;
              text-align: initial;
              letter-spacing: 0;
              margin: 0;
              width: 720px; 
              left: 0;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              overflow-y: scroll;
              height: 480px;

              a {
                color: #fff;
              }

              p {
                margin-bottom: 10px;
              }
            }
          }
        }

        .bgBlue {
          background-color: #0065ff;

          &~.likeDeslike {
            background-color: #0065ff;

            button {
              background-color: #253858;
            }

            .active {
              background-color: #fff;
              border: 1px solid #0065ff;
            }
          }
        }

        .cadetblueBG {
          background-color: #0f5a6a;

          &~.likeDeslike {
            background-color: #0f5a6a;

            button {
              background-color: #253858;
            }
          }
        }
      }

      .slick-next:before,
      .slick-prev:before {
        font-size: 28px;
        line-height: 1;
        opacity: 1;
        color: #ccc;
      }

      .slick-prev {
        left: 5px;
        z-index: 999;
      }

      .slick-next {
        right: 15px;
      }

      .slick-dots.slick-thumb {
        bottom: 0px !important;

        li {
          width: auto;
          height: auto;

          .non-active {
            display: block;
            width: 10px;
          }

          .active {
            display: none;
            width: 40px;
          }
        }

        li.slick-active {
          .non-active {
            display: none;
          }

          .active {
            display: block;
          }
        }
      }

      .likeDeslike {
        bottom: 12px;
        color: #0065ff;
        background-color: black;
        position: absolute;
        display: flex !important;
        height: 40px !important;
        padding-left: 18px;
        width: auto;
        text-align: left !important;

        button {
          margin-right: 10px;
          height: 38px;
          width: 38px;
          background-color: #dddddd4a;
          border-radius: 50px;
          padding-top: 2px;
          cursor: pointer;
          color: #0065ff;
          border: none;
          outline: none;
          position: relative;
          display: inline-block;

          .tooltiptext {
            visibility: hidden;
            width: 60px;
            background-color: #253858;
            color: #fff;
            text-align: center;
            border-radius: 4px;
            padding: 3px 15px;
            font: normal normal 11px/17px Roboto;
            position: absolute;
            z-index: 1;
            bottom: 116%;
            left: 50%;
            margin-left: -30px;
          }

          .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #253858 transparent transparent transparent;
          }

          &:hover .tooltiptext {
            visibility: visible;
          }
        }

        .active {
          background-color: #fff;
          border: 1px solid #0065ff;

          .unlike {
            display: none;
          }

          .like {
            display: block;
          }
        }

        .like {
          display: none;
        }

        .unlike {
          display: block;
        }
      }
    }
  }
}

.nostories {
  font: normal normal 500 20px/24px Roboto;
  padding: 20px;
  text-align: center;
  color: #fff;
  background-color: #0065ff;
}

.SoftcopySendSuccess {
  .MuiDialog-paperWidthSm {
    background: #ffffff 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000029;
    border-radius: 8px;
    opacity: 1;
    text-align: center;
    height: auto;

    .successIcon {
      color: #00bc0000;
      font-size: 3rem;
    }

    h4 {
      text-align: center;
      font: normal normal 600 18px/24px Roboto;
      letter-spacing: 0.25px;
      color: #455570;
      opacity: 1;
    }

    p {
      text-align: center;
      font: normal normal normal 18px/24px Roboto;
      letter-spacing: 0.25px;
      color: #455570;
      opacity: 1;
      margin: 15px 0px 15px 0px;
    }

    button {
      background: #0065ff 0% 0% no-repeat padding-box;
      box-shadow: 0px 0px 16px #00000014;
      border-radius: 8px;
      opacity: 1;
      font: normal normal 500 14px/17px Roboto;
      letter-spacing: 0.2px;
      color: #ffffff;
      outline: none;
      border: none;
      padding: 9px 15px;
    }
  }
}

*::-webkit-scrollbar,
*::-webkit-scrollbar-thumb {
  width: 6px;
  border-radius: 13px;
  background-clip: padding-box;
  border: 1px solid transparent;
}

*::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 0 10px;

  :hover {
    color: rgba(0, 0, 0, 0.3);
  }
}

// .MuiDialogContent-root{
//   overflow-y: initial !important;
// }

.bookingCancelMsg {
  padding: 0px 8px 8px !important;
  color: #e06666;
}

.FOSLeadChurnReason {
  padding: 0px 8px 8px !important;
  color: #e06666;
}

.HotLeadsTagging {
  padding: 0px 8px 8px !important;
  color: #87E066;
}

/* start action btn  css for mobile view*/
.topbar-scroll-mobile {
  width: 100%;
  overflow-x: auto;
  display: flex;
}

.topbar-scroll-mobile::-webkit-scrollbar,
.topbar-scroll-mobile::-webkit-scrollbar-thumb {
  width: 0px;
  border-radius: 13px;
  background-clip: padding-box;
  border: none;
  height: 2px;
}

.topbar-scroll-mobile::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 0 0px;
  color: rgba(0, 0, 0, 0.3);
}

/*end css*/
/* start  rightbar menu css for mobile view*/
.rightmenuIcon {
  float: right;
  width: 26px;
  height: 26px;
  margin-top: 12px;
  border: 1px solid #303030;
  border-radius: 15px;
  text-align: center;
}

.headerFixOnMb {
  position: fixed;
  width: 100%;
  left: 0;
  z-index: 9;
  background-color: #f0f0f0;
}

.RightBar-mobileview {
  .MuiDrawer-paperAnchorBottom {
    background: #ffffff 0% 0% no-repeat padding-box;
    box-shadow: 0px -6px 26px #00000029;
    border-radius: 24px 24px 0px 0px;
    opacity: 1;
    padding: 15px;

    .text-right {
      text-align: right;
    }
  }

  .hideOnMobile {
    display: none;
  }

  ul {
    display: flex;
    flex-wrap: wrap;

    li {
      list-style: none;
      padding: 10px 3px;
      text-align: center;
      font: normal normal normal 10px/13px Roboto;
      letter-spacing: 0.14px;
      color: #253858;
      opacity: 1;
      display: block;
      width: 60px;
      margin-left: 6px;
      float: left;

      .MuiBadge-root {
        display: block;
      }

      p {
        line-height: 5px;
        background: #fff 0% 0% no-repeat padding-box;
        box-shadow: 0px 6px 16px #0065ff29;
        border-radius: 16px;
        opacity: 1;
        padding: 14px 10px;
        margin-bottom: 14px;
      }

      &.active {
        // border-left: 2px solid #0065ff;
        color: #0065ff;
      }
    }
  }

  .handicon {
    margin-bottom: 6px;
  }
}

/*end css */
/*Appoitmentchip css */
.rightSection {
  .confirmedAppoitmentchip {
    background: transparent linear-gradient(270deg, #ffffff 0%, #c8dbf9 100%) 0% 0% no-repeat padding-box;
    border-radius: 8px;
    opacity: 1;
    margin-bottom: 12px;
    box-shadow: 0px 3px 20px #00000029;

    .matrixGoLogo {
      float: left;
      width: 70px;
      background: url("/public/images/salesview/matrixgoLogo.png") no-repeat;
      background-position: center;
      padding: 5px 13px;
    }

    .bottomBox {
      background: transparent linear-gradient(84deg, #71ad98 0%, #50a1b4 100%) 0% 0% no-repeat padding-box;
      border-radius: 0px 0px 8px 8px;
      opacity: 1;
      padding: 5px 15px 6px 15px;
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;

      span {
        text-align: left;
        font: normal normal normal 14px/13px Roboto;
        letter-spacing: 0.2px;
        color: #ffffff;
        margin-right: 15px;
      }

      svg {
        font-size: 14px;
        position: relative;
        top: 2px;
      }

      p {
        font: normal normal 500 12px/12px Roboto;
        letter-spacing: 0.17px;
        color: #ffffff;
        opacity: 1;
        cursor: pointer;

        svg {
          font-size: 10px;
          top: 1px;
        }
      }
    }

    .leftSide {
      float: left;
      padding: 10px 12px;

      h4 {
        text-align: left;
        font: normal normal bold 12px/21px Roboto;
        letter-spacing: 0.17px;
        color: #253858;
        text-transform: capitalize;
        opacity: 1;
      }

      p {
        text-align: left;
        font: normal normal 600 12px/13px Roboto;
        letter-spacing: 0.17px;
        color: #253858;
        opacity: 1;
        padding-top: 2px;
      }
    }

    img {
      padding: 11px 13px;
      float: right;
    }

    .addressbox {
      padding: 5px 15px 3px 15px;
      display: flex;
      float: left;
      color: #fff;
      font: normal normal normal 12px/15px Roboto;
      align-items: center;
      width: 100%;
      border-bottom: 1px solid #579ca5;
      background: transparent linear-gradient(84deg, #4a7a6a 0%, #50a1b4 100%) 0% 0% no-repeat padding-box;

      svg {
        font-size: 15px;
        position: relative;
        top: -1px;
        margin-right: 3px;
      }

      p {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .wrongError {
    background: transparent linear-gradient(270deg, #ffffff 0%, #ffafaf 100%) 0% 0% no-repeat padding-box;
    padding: 10px;
    font: normal normal bold 14px/21px Roboto;
    border-radius: 8px;
    opacity: 1;
    margin-bottom: 12px;
  }

  .BookedAppoitmentchip {
    background: transparent linear-gradient(75deg, #d3c6f7 0%, #ffffff 100%) 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 20px #00000029;
    border-radius: 8px;
    opacity: 1;
    margin-bottom: 12px;

    .matrixGoLogo {
      float: left;
      width: 70px;
      background: url("/public/images/salesview/matrixgoLogo.png") no-repeat;
      background-position: center;
      padding: 5px 13px;
    }

    .bottomBox {
      background: transparent linear-gradient(269deg, #a291de 0%, #a69ccb 100%) 0% 0% no-repeat padding-box;
      border-radius: 0px 0px 8px 8px;
      opacity: 1;
      padding: 5px 15px 6px 15px;
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: space-between;

      span {
        text-align: left;
        font: normal normal normal 14px/13px Roboto;
        letter-spacing: 0.2px;
        color: #ffffff;
        margin-right: 15px;
      }

      svg {
        font-size: 14px;
        position: relative;
        top: 2px;
      }

      p {
        font: normal normal 500 12px/12px Roboto;
        letter-spacing: 0.17px;
        color: #ffffff;
        opacity: 1;
        cursor: pointer;

        svg {
          font-size: 10px;
          top: 1px;
        }
      }
    }

    .leftSide {
      float: left;
      padding: 10px 12px;

      h4 {
        text-align: left;
        font: normal normal bold 12px/21px Roboto;
        letter-spacing: 0.17px;
        color: #253858;
        text-transform: capitalize;
        opacity: 1;
      }

      p {
        text-align: left;
        font: normal normal 600 12px/13px Roboto;
        letter-spacing: 0.17px;
        color: #253858;
        opacity: 1;
        padding-top: 2px;
      }
    }

    img {
      padding: 11px 13px;
      float: right;
    }

    .addressbox {
      padding: 5px 15px 3px 15px;
      display: flex;
      float: left;
      color: #fff;
      font: normal normal normal 12px/15px Roboto;
      align-items: center;
      width: 100%;
      border-bottom: 1px solid #9287c5;
      background: transparent linear-gradient(84deg, #7362b1 0%, #a59acd 100%) 0% 0% no-repeat padding-box;

      svg {
        font-size: 15px;
        position: relative;
        top: -1px;
        margin-right: 3px;
      }

      p {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .noActiveAppoitment {
    background: transparent linear-gradient(75deg, #c8dbf9 0%, #ffffff 100%) 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 20px #00000029;
    border-radius: 8px;
    opacity: 1;
    margin-bottom: 12px;

    .matrixGoLogo {
      float: left;
      width: 70px;
      background: url("/public/images/salesview/matrixgoLogo.png") no-repeat;
      background-position: center;
      padding: 5px 13px;
    }

    .bottomBox {
      background: transparent linear-gradient(269deg, #1e4ec7 0%, #306bff 100%) 0% 0% no-repeat padding-box;
      border-radius: 0px 0px 8px 8px;
      opacity: 1;
      padding: 5px 15px 6px 15px;
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: flex-end;

      p {
        font: normal normal 500 12px/12px Roboto;
        letter-spacing: 0.17px;
        color: #ffffff;
        opacity: 1;
        cursor: pointer;
        width: 100%;
        text-align: right;

        svg {
          font-size: 10px;
          top: 1px;
          position: relative;
          margin-left: 6px;
        }
      }
    }

    .leftSide {
      float: left;
      padding: 17px 0px 10px;

      h4 {
        text-align: left;
        font: normal normal bold 12px/21px Roboto;
        letter-spacing: 0.17px;
        color: #253858;
        text-transform: inherit;
        opacity: 1;
      }
    }

    img {
      padding: 15px 13px;
      float: right;
      width: 50px;
    }
  }

  // Style for Webphone Iframe
  #mtxPhone {
    box-shadow: 7px 3px 7px 0px rgba(0, 0, 0, 0.1607843137);
    margin-bottom: 10px;
  }

  #mtxPhoneIframe {
    width: 100%;
    border-radius: 10px;
  }

  #aswat {
    display: none;
    box-shadow: 7px 3px 7px 0px rgba(0, 0, 0, 0.1607843137);
    margin-bottom: 10px;
  }

  #aswatIframe {
    width: 100%;
    border-radius: 10px;
  }
}

/*end css */
/* faq css */
.faqPopUp {
  .MuiDialogTitle-root {
    background-color: #c8dbf9;
  }

  .MuiPaper-rounded {
    box-shadow: none !important;
  }

  .MuiAccordionSummary-expandIcon {
    color: #0f6eff;
  }

  .popupWrapper {
    width: 900px;
    height: 600px;
  }

  .MuiAccordionSummary-root {
    padding-left: 0px;
  }

  .FaqDescription {
    display: block;
    padding: 0px;

    .details {
      width: 100%;
      float: left;

      .bgBlue {
        margin-bottom: 18px;
        background-color: #e7f1ff;
        width: 100%;
        float: left;
        padding: 12px 20px 5px;
        border-radius: 8px;
      }

      p {
        margin: 5px 40px 5px 10px;
        width: 100%;
        text-align: left;
        font: normal normal 600 12px/18px Roboto;
        letter-spacing: 0.19px;
        color: #253858;
        opacity: 1;
        float: left;
        white-space: pre-line;
      }

      ul {
        list-style-type: none;
        width: 100%;
        float: left;
        margin-top: 15px;

        li {
          text-align: left;
          font: normal normal normal 12px/18px Roboto;
          letter-spacing: 0.19px;
          color: #253858;
          opacity: 1;
          background: url("/public/images/salesview/icon_tick_circle.svg") no-repeat;
          padding-left: 25px;
          padding-bottom: 15px;
        }
      }
    }

    .tag {
      margin: 0px 0px 10px 25px;
      font-weight: 600;
    }
  }

  .title {
    text-align: left;
    font: normal normal 600 14px/24px Roboto;
    letter-spacing: 0.22px;
    color: #808080;
    opacity: 1;
  }

  .MuiAccordionSummary-content {
    align-items: center;
  }

  .Heading {
    font: normal normal 600 16px/24px Roboto;
    margin-bottom: 15px;
    margin-left: 10px;
    margin-top: 10px;
    color: #253858;
  }
}

/* END faq css */
@media only screen and (min-width: 768px) and (max-width: 1250px) {
  .topbarRight {
    button {
      margin: 0px 7px 5px 2px !important;
    }
  }

  .rightSection {
    margin-top: 15px;

    .add-que {
      margin-right: 5px !important;

      .text {
        display: none !important;
      }
    }

    .add-car {
      margin-right: 5px !important;

      .text {
        display: none !important;
      }

      .icon-border {
        margin-right: 0px !important;
      }
    }

    .LastFiveLeads {
      margin-left: 5px !important;
    }
  }

  .AttemptsPopup {
    .MuiDialog-paperWidthSm {
      height: auto;
    }
  }

  .addDetailsPopup {
    .MuiDialog-paperWidthSm {
      height: auto;
    }

    .MuiTableContainer-root {
      height: 300px;
    }
  }

  .EditPopup {
    width: auto;
  }

  .viewAllCommentsPopup {
    width: 100%;
    height: 400px;
    overflow-y: auto;
  }

  .AddAddress {
    height: 380px !important;
  }
}

@media only screen and (min-width: 481px) and (max-width: 750px) {
  .leadCreatedSuesses {
    .MuiDialogContent-root {
      width: 100% !important;
    }
  }

  .BusinessRating {
    width: 100% !important;
    font: normal normal normal 16px/24px Roboto !important;

    button {
      font: normal normal normal 12px/19px Roboto !important;
    }
  }

  .recomendations-section {
    margin-top: 1rem !important;
  }

  .topbar-scroll-mobile {
    .MarkImportant {
      margin-bottom: 0px !important;
    }

    .LastFiveLeads {
      margin-bottom: 0px !important;
    }

    .add-car {
      margin-bottom: 0px !important;
    }
  }

  .spacehight {
    margin-top: 4em !important;
  }

  .rightmenuIcon {
    margin-right: 6px;
  }

  .rightmenu {
    overflow-y: scroll !important;
    height: 85% !important;

    .menu {
      ul:last-child {
        bottom: -200px !important;
      }
    }
  }

  .AttemptsPopup {
    .MuiDialog-paperWidthSm {
      height: auto;
    }
  }

  // .addDetailsPopup {
  //   .MuiDialog-paperWidthSm {
  //     height: auto;
  //   }
  //   .MuiTableContainer-root {
  //     height: 300px;
  //   }
  // }
  .addDetailsPopup {
    .MuiTab-textColorInherit {
      margin-right: 20px;
    }
  }

  .EditPopup {
    width: auto;
  }

  .viewAllCommentsPopup {
    width: 100%;
    height: 400px;
    overflow-y: auto;
  }

  .addnew-form-wrapper {
    width: 100%;
  }

  .rightSection {
    margin-top: 10px;
  }

  .addmoreLeadPopup {
    .MuiTooltip-tooltip {
      max-width: 550px;
      margin: 0px 15px;
    }
  }

  .rejectLeadPopop {
    width: 100%;
  }

  .AddAddress {
    height: 380px !important;
  }

  .recomend-block {
    .row {
      flex-wrap: wrap !important;
      align-items: flex-start !important;
      margin-top: 10px;

      .inner-block {
        width: 100% !important;

        &.plan {
          margin-right: 10px !important;
          width: 47% !important;
        }

        &.select {
          width: 49% !important;
        }

        &.interest {
          width: 100% !important;
          margin-top: 5px !important;
          margin-right: 0px !important;
        }

        p {
          &.heading {
            margin-top: 9px !important;
          }
        }
      }
    }
  }

  .AnalyticsPopup {
    width: 100%;
    height: 600px;
  }

  .VideoCallPopup {
    .popupLeftSection {
      width: 100% !important;
    }
  }

  .EditPopup {
    width: 100%;
  }

  .MuiSnackbarContent-root {
    padding: 2px !important;
  }

  .MuiAutocomplete-root {
    width: 100% !important;
  }

  #nextFive {
    width: 70% !important;
  }

  .DailingIcon {
    width: 100%;
  }

  #searchbar {
    width: 85%;
    margin-left: 40px !important;
    margin-top: 2px;
    float: left;
    margin-right: 0px !important;

    .searchbox {
      margin-bottom: 0.5em !important;
      background-position: 282px !important;
    }
  }

  .topbarRight {
    margin-bottom: 0px !important;

    button {
      height: 45px !important;
      padding: 10px 18px !important;
      margin: 0px 5px 5px 2px !important;
      min-width: 40px !important;
    }
  }

  .wrapper {
    width: calc(100vw - 0px) !important;
    padding: 0px 10px 100px 10px !important;

    .callBackSection {
      margin-top: 4.2em;
    }
  }

  .leftmenu {
    display: block;

    .MuiDrawer-paperAnchorDockedLeft {
      width: 0px !important;
    }

    .MuiDrawer-docked {
      width: 0px !important;
    }

    .toggleIcon {
      position: fixed;
      padding: 14px 0px;
      min-width: 40px;
      border: none;
    }
  }

  .topbarLeft {
    width: 100% !important;
    left: 0px;
    bottom: 0px;
  }

  .spacehight {
    margin-top: 0px !important;
  }

  .add-que {
    margin-left: 12px !important;
    margin-right: 12px !important;

    .text {
      display: none !important;
    }
  }

  .LastFiveLeads {
    margin-left: 12px !important;
    margin-bottom: 0px !important;
  }

  .MarkImportant {
    margin-bottom: 0px !important;
  }

  .crossSell-section {
    margin-top: 1rem !important;
  }

  .ApproveSMS {
    width: 100% !important;
  }
}

/*HDFC pasa Popup css*/
.HDFCPasaPopupDesign {
  .MuiDialog-paperWidthSm {
    width: 596px;
    height: auto;

    .imgLogoMax {
      height: 50px;
    }

    .divMaxNew {
      display: flex;
      margin-bottom: -5px;
    }

    .imgLogoNew {
      height: 40px;
      margin-left: 80px;
    }

    .imgLogoBajaj {
      height: 80px;
      margin-left: 100px;
    }

    .imgLogoTata {
      height: 100px;
      margin-left: 30px;
    }

    .SmartSecureText {
      height: 20px;
      margin-left: 80px;
      padding: 0px;
    }

    .imgLogoPreApprove {
      height: 100px;
      margin-left: 10px;
    }

    .imgLogo {
      text-align: center;
      margin-top: 15px;
    }

    .CustomerEligibleTableMax {
      ul {
        justify-content: space-evenly !important;
      }
    }

    .CustomerEligibleTable {
      background: #1e4c881a 0% 0% no-repeat padding-box;
      border-radius: 20px;
      padding: 20px;

      ul {
        display: flex;
        justify-content: space-between;
        list-style-type: none;

        li {
          text-align: center;
          font: normal normal 600 14px/19px Roboto;
          letter-spacing: 0px;
          color: #192248;
          opacity: 1;

          h1 {
            text-align: center;
            font: normal normal bold 23px/45px Roboto;
            letter-spacing: 0px;
            color: #192248;
            opacity: 1;
          }

          .green {
            color: #0D9068;
          }

          .red {
            color: #DD3939;
          }
        }
      }

      .confirmationMsg {
        text-align: center;
        font: normal normal 600 14px/19px Roboto;
        letter-spacing: 0px;
        color: #192248;
        opacity: 1;
        margin: 14px 0px 7px;
      }

      h4 {
        text-align: center;
        font: normal normal 600 21px/32px Roboto;
        letter-spacing: 0px;
        color: #192248;
        opacity: 1;
      }
    }

    .MAXPASAmsg {
      background: #FFFFFF 0% 0% no-repeat padding-box;
      margin-left: 50px;
      text-align: center;
      padding-bottom: 30px;
      padding-top: 18px;

      .h3 {
        font: normal normal 600 14px/19px Roboto;
        letter-spacing: 0px;
        color: #192248;
        opacity: 1;
      }
    }
  }

  .MuiDialogContent-root {
    padding: 2px 24px !important;
  }

  .MuiDialogTitle-root {
    h6 {
      text-align: center;
      font: normal normal 600 18px/24px Roboto;
      letter-spacing: 0px;
      color: #1E522E;
    }
  }
}

/* Ebd HDFC pasa css */

@media only screen and (min-width: 361px) and (max-width: 480px) {
  .leadCreatedSuesses {
    .MuiDialogContent-root {
      width: 100% !important;
    }
  }

  .BusinessRating {
    width: 100% !important;
    font: normal normal normal 16px/24px Roboto !important;

    button {
      font: normal normal normal 12px/19px Roboto !important;
    }
  }

  .recomendations-section {
    margin-top: 1rem !important;
  }

  .topbar-scroll-mobile {
    .MarkImportant {
      margin-bottom: 0px !important;
    }

    .LastFiveLeads {
      margin-bottom: 0px !important;
    }

    .add-car {
      margin-bottom: 0px !important;
    }
  }

  .spacehight {
    margin-top: 4em !important;
  }

  .rightmenuIcon {
    margin-right: 8px;
  }

  .topbarRight {
    margin-bottom: 0px !important;
  }

  .rightSection {
    margin-top: 10px;
  }

  .add-que {
    margin-left: 12px !important;
    margin-right: 12px !important;

    .text {
      display: none !important;
    }
  }

  .add-car {
    .text {
      display: none !important;
    }

    .icon-border {
      margin-right: 0px !important;
    }
  }

  .leftmenu {
    display: block;

    .MuiDrawer-paperAnchorDockedLeft {
      width: 0px !important;
    }

    .MuiDrawer-docked {
      width: 0px !important;
    }
  }

  .rightmenu {
    display: none !important;
  }

  .selesviewUI {
    .wrapper {
      width: calc(100vw - 0px) !important;
    }

    .toggleIcon {
      position: fixed;
      padding: 14px 0px;
      min-width: 40px;
      border: none;
    }
  }

  .AttemptsPopup {
    .MuiDialog-paperWidthSm {
      height: auto;
    }
  }

  .addDetailsPopup {
    .MuiDialog-paperWidthSm {
      height: auto;
    }

    .MuiTableContainer-root {
      height: 300px;
    }
  }

  .EditPopup {
    width: auto;
  }

  .viewAllCommentsPopup {
    width: 100%;
    height: 350px;
    overflow-y: auto;
  }

  .addnew-form-wrapper {
    width: 100%;
  }

  .addmoreLeadPopup {
    .MuiTooltip-tooltip {
      max-width: 350px;
      margin: 0px 15px;
    }
  }

  .rejectLeadPopop {
    width: 100%;
  }

  .AddAddress {
    height: 380px !important;
  }

  .recomend-block {
    .row {
      flex-wrap: wrap !important;
      align-items: flex-start !important;
      margin-top: 10px;

      .inner-block {
        width: 100% !important;

        &.plan {
          margin-right: 10px !important;
          width: 47% !important;
        }

        &.select {
          width: 49% !important;
        }

        &.interest {
          width: 100% !important;
          margin-top: 5px !important;
          margin-right: 0px !important;
        }

        p {
          &.heading {
            margin-top: 9px !important;
          }
        }
      }
    }
  }

  #searchbar {
    width: 80%;
    margin-left: 35px !important;
    margin-top: 2px;
    float: left;
    margin-right: 0px !important;

    .searchbox {
      margin-bottom: 0.5em !important;
      background-position: 280px !important;
    }
  }

  .topbarLeft {
    width: 100% !important;
    left: 0px;
    bottom: 0px;
  }

  .crossSell-section {
    margin-top: 1rem !important;
  }

  .ApproveSMS {
    width: 100% !important;
  }
}

@media only screen and (min-width: 320px) and (max-width: 360px) {
  .leadCreatedSuesses {
    .MuiDialogContent-root {
      width: 100% !important;
    }
  }

  .BusinessRating {
    width: 100% !important;
    font: normal normal normal 16px/24px Roboto !important;

    button {
      font: normal normal normal 12px/19px Roboto !important;
    }
  }

  .recomendations-section {
    margin-top: 1rem !important;
  }

  .crossSell-section {
    margin-top: 1rem !important;
  }

  .topbar-scroll-mobile {
    .MarkImportant {
      margin-bottom: 0px !important;
    }

    .LastFiveLeads {
      margin-bottom: 0px !important;
    }

    .add-car {
      margin-bottom: 0px !important;
    }
  }

  .spacehight {
    margin-top: 4em;
  }

  .rightSection {
    margin-top: 10px;
  }

  .add-que {
    margin-left: 12px !important;
    margin-right: 12px !important;

    .text {
      display: none !important;
    }
  }

  .add-car {
    .text {
      display: none !important;
    }

    .icon-border {
      margin-right: 0px !important;
    }
  }

  .leftmenu {
    display: block;

    .MuiDrawer-paperAnchorDockedLeft {
      width: 0px !important;
    }

    .MuiDrawer-docked {
      width: 0px !important;
    }
  }

  .rightmenu {
    display: none !important;
  }

  .selesviewUI {
    .wrapper {
      width: calc(100vw - 0px) !important;
      padding: 0px 10px 100px 10px;

      .callBackSection {
        margin-top: 4.2em;
      }
    }

    .toggleIcon {
      position: fixed;
      padding: 14px 0px;
      min-width: 40px;
      border: none;
    }
  }

  .AttemptsPopup {
    .MuiDialog-paperWidthSm {
      height: auto;
    }

    .AddAddress {
      height: 380px !important;
    }
  }

  .addDetailsPopup {
    .MuiDialog-paperWidthSm {
      height: auto;
    }
  }

  // .EditPopup {
  //   width: auto;
  // }
  .EditPopup {
    width: 100%;
  }

  .viewAllCommentsPopup {
    width: 100%;
    height: 350px;
    overflow-y: auto;
  }

  .addnew-form-wrapper {
    width: 100%;
  }

  .addmoreLeadPopup {
    .MuiTooltip-tooltip {
      max-width: 315px;
      margin: 0px 15px;
    }
  }

  .rejectLeadPopop {
    width: 100%;
  }

  .recomend-block {
    .row {
      flex-wrap: wrap !important;
      align-items: flex-start !important;
      margin-top: 10px;

      .inner-block {
        width: 100% !important;

        &.plan {
          margin-right: 10px !important;
          width: 47% !important;
        }

        &.select {
          width: 49% !important;
        }

        &.interest {
          width: 100% !important;
          margin-top: 5px !important;
          margin-right: 0px !important;
        }

        p {
          &.heading {
            margin-top: 9px !important;
          }
        }
      }
    }
  }

  .lastFiveLead {
    width: 100%;
    height: 468px;
  }

  .AnalyticsPopup {
    width: 100%;
    height: 468px;
  }

  .VideoCallPopup {
    .popupLeftSection {
      width: 100% !important;
    }
  }

  .viewAllCommentsPopup {
    width: 100%;
    height: 450px;
  }

  .MuiSnackbarContent-root {
    padding: 2px !important;
  }

  .MuiAutocomplete-root {
    width: 100% !important;
  }

  .addDetailsPopup {
    .MuiTab-textColorInherit {
      margin-right: 15px;
    }
  }

  #nextFive {
    width: 62% !important;
  }

  .DailingIcon {
    width: 100%;
  }

  #searchbar {
    width: 76%;
    margin-left: 40px !important;
    margin-top: 2px;
    float: left;
    margin-right: 0px !important;

    .searchbox {
      background-position: 217px !important;
      margin-bottom: 0.5em !important;
    }
  }

  .topbarRight {
    margin-bottom: 0px !important;

    button {
      height: 45px !important;
      padding: 10px 12px !important;
      margin: 0px 5px 5px 2px !important;
      min-width: 40px !important;
    }
  }

  .noLeadbox {
    min-width: 175px !important;
  }

  .footer-bar {
    display: none;
  }

  .LastFiveLeads {
    margin-left: 12px !important;
    margin-bottom: 0px !important;
  }

  .MarkImportant {
    margin-bottom: 0px !important;
  }

  .add-car {
    .text {
      display: none !important;
    }

    .icon-border {
      margin-right: 0px !important;
    }
  }

  .topbarLeft {
    width: 100% !important;
    left: 0px;
    bottom: 0px;
  }

  .spacehight {
    margin-top: 0px !important;
  }

  .rightmenuIcon {
    margin-right: 5px;
  }

  .ApproveSMS {
    width: 100% !important;
  }
}

.reassigned-table {
  max-height: 320px;
  overflow: auto;
}

.filter-bar {
  .field {
    width: 130px;
    margin-right: 10px;
    display: inline-block;

    .makeStyles-root-59 .MuiOutlinedInput-root,
    .MuiOutlinedInput-root {
      height: 28px !important;
    }

    .makeStyles-root-60 .MuiOutlinedInput-root {
      height: 28px !important;
    }

    .MuiGrid-root {
      width: 100%;
      background: #0065ff;
      max-width: 100%;
      border-radius: 30px;
    }

    &.not-selected .MuiGrid-root {
      background: #fff;
    }

    .makeStyles-root-40 {
      width: 100%;
      background: #0065ff;
    }

    .MuiSelect-outlined.MuiSelect-outlined {
      color: #fff !important;
    }

    &.not-selected .MuiSelect-outlined.MuiSelect-outlined {
      color: #000;
    }

    .MuiSelect-icon {
      color: rgba(255, 255, 255, 0.9);
    }

    &.not-selected .MuiSelect-icon {
      color: rgba(0, 0, 0, 0.54);
    }

    &.not-selected .makeStyles-root-47 .MuiOutlinedInput-root {
      height: 30px !important;
    }

    &.not-selected .MuiInputLabel-formControl {
      top: -5px;
      font-size: 13px;
    }

    .makeStyles-root-42 .MuiOutlinedInput-root {
      height: 30px !important;
    }

    .makeStyles-root-40 .MuiOutlinedInput-root,
    .makeStyles-root-45 .MuiOutlinedInput-root {
      text-align: left;
      border: 0;
      line-height: 30px;
      height: 30px !important;
    }

    .MuiOutlinedInput-notchedOutline {
      border-color: transparent !important;
    }

    .MuiInputLabel-outlined.MuiInputLabel-shrink {
      display: none;
    }

    .MuiSelect-select:focus {
      background: transparent !important;
      -webkit-box-shadow: none;
      box-shadow: none !important;
      -webkit-text-fill-color: #fff;
    }
  }
}

.activity-feed {
  .filter-bar {
    background: #f1f1f1;
    text-align: left;
    padding: 8px;
    margin-bottom: 25px;
    // overflow-x: scroll;
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;

    .text {
      font-size: 12px;
      font-weight: 600;
      color: #2e2e2e;
      align-self: center;
      padding: 5px 2px 0 0;
      float: left;
      display: block;
    }

    .textdisplay {
      font-size: 12px;
      font-weight: 600;
      color: #2e2e2e;
      align-self: center;
      padding: 5px 2px 0 0;
      float: left;
      display: none;
    }

    .filter-text {
      font-size: 16px;
      font-weight: 600;
      color: #2e2e2e;
      align-self: center;
      padding: 5px 20px 0 0;
      float: left;
    }
  }

  .blocks-data {
    height: calc(100vh - 170px);
    overflow: auto;
    width: 100%;
    text-align: left;
    position: relative;

    .row {
      background: #ffffff;
      margin: 0 0 17px 40px;
      padding: 16px 24px;
      width: calc(100% - 60px);
      position: relative;
      border-radius: 16px;
      -moz-box-shadow: 0px 6px 16px rgba(160, 160, 160, 0.29);
      -webkit-box-shadow: 0px 6px 16px rgba(160, 160, 160, 0.29);
      box-shadow: 0px 6px 16px rgba(160, 160, 160, 0.29);

      .line {
        content: "";
        position: absolute;
        width: 1px;
        height: 120%;
        background: #0065ff;
        left: -30px;
        top: 0px;
      }

      &:last-child .line {
        height: 40px;
      }

      &:first-child .line {
        top: 30px;
      }

      span.dot {
        border: 1px solid #0065ff;
        border-radius: 50%;
        background: #fff;
        width: 11px;
        height: 11px;
        position: absolute;
        left: -35px;
        top: 30px;

        &::before {
          content: "";
          width: 5px;
          height: 5px;
          background: #b3d4ff;
          border-radius: 50%;
          left: 0;
          top: 0;
          display: block;
          margin: 2px 0 0 2px;
        }
      }

      .play-recording {
        float: right;
        font-size: 12px;
        color: #0065ff;
        cursor: pointer;
        z-index: 1;
        position: relative;

        i {
          width: 16px;
          height: 16px;
          background: #0065ff;
          border-radius: 50%;
          margin-right: 8px;
          display: inline-block;
          vertical-align: middle;

          &::before {
            content: "";
            display: inline-block;
            margin: 4px 0 0 7px;
            vertical-align: top;
            width: 0;
            height: 0;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
            border-left: 4px solid #fff;
          }
        }
      }

      &::before {
        content: "";
        position: absolute;
        top: 20px;
        left: -30px;
        border: solid 15px transparent;
        border-right-color: #fff;
        z-index: 1;
      }

      .row-time {
        font-size: 12px;
        font-weight: 600;
        color: #40516d;

        &.blue {
          color: #0065ff;
        }
      }

      .row-cust-name {
        color: #40516d;
        font-size: 12px;
      }

      .mid-row {
        margin: 9px 0 0;
        display: flex;

        div {
          margin-right: 20px;

          &.communication-block:first-child {
            width: calc(20% - 20px);
          }

          &.communication-block:nth-child(2n) {
            width: calc(25% - 20px);
          }

          &.communication-block:nth-child(3n) {
            width: calc(25% - 20px);
          }

          &.communication-block:nth-child(4n) {
            width: 30%;
            margin-right: 0;
          }

          p.label {
            font-size: 12px;
            color: #808080;
            line-height: 16px;
          }

          p.data {
            font-size: 14px;
            color: #3d4e6b;
          }
        }
      }

      .mid-column {
        display: flex;

        .block {
          width: calc(50% - 15px);
          margin-right: 30px;

          &:nth-child(2n) {
            margin-right: 0;
          }

          p.name {
            color: #808080;
            font-size: 10px;
            padding-top: 10px;
          }

          p.booking {
            color: #0065ff;
            font-size: 12px;
          }

          p.sm-text {
            color: #808080;
            font-size: 10px;
            padding-top: 14px;
          }

          p.value {
            color: #0065ff;
            background: #b3d4ff;
            border-radius: 6px;
            font-size: 10px;
            padding: 3px 4px;

            &.new-value {
              background: #aefdd3;
            }
          }
        }
      }
    }
  }
}

.appoitmentLog {
  .filter-bar {
    background: #f1f1f1;
    text-align: left;
    padding: 8px;
    margin-bottom: 25px;
    // overflow-x: scroll;
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;

    .text {
      font-size: 12px;
      font-weight: 600;
      color: #2e2e2e;
      align-self: center;
      padding: 5px 20px 0 0;
      float: left;
    }
  }

  .blocks-data {
    height: calc(100vh - 170px);
    overflow: auto;
    width: 100%;
    text-align: left;
    position: relative;

    .leadIDtext {
      color: #808080;
      font-size: 12px;
    }

    .row {
      background: #ffffff;
      margin: 0 0 17px 40px;
      padding: 16px 24px;
      width: calc(100% - 60px);
      position: relative;
      border-radius: 16px;
      -moz-box-shadow: 0px 6px 16px rgba(160, 160, 160, 0.29);
      -webkit-box-shadow: 0px 6px 16px rgba(160, 160, 160, 0.29);
      box-shadow: 0px 6px 16px rgba(160, 160, 160, 0.29);

      .line {
        content: "";
        position: absolute;
        width: 1px;
        height: 120%;
        background: #0065ff;
        left: -30px;
        top: 0px;
      }

      &:last-child .line {
        height: 40px;
      }

      &:first-child .line {
        top: 30px;
      }

      span.dot {
        border: 1px solid #0065ff;
        border-radius: 50%;
        background: #fff;
        width: 11px;
        height: 11px;
        position: absolute;
        left: -35px;
        top: 30px;

        &::before {
          content: "";
          width: 5px;
          height: 5px;
          background: #b3d4ff;
          border-radius: 50%;
          left: 0;
          top: 0;
          display: block;
          margin: 2px 0 0 2px;
        }
      }

      &::before {
        content: "";
        position: absolute;
        top: 20px;
        left: -30px;
        border: solid 15px transparent;
        border-right-color: #fff;
        z-index: 1;
      }

      .row-time {
        font-size: 12px;
        font-weight: 600;
        color: #40516d;

        &.blue {
          color: #0065ff;
        }
      }

      .row-cust-name {
        color: #40516d;
        font-size: 12px;
      }

      .mid-row {
        margin: 9px 0 0;
        display: flex;

        div {
          margin-right: 0px;
          width: 22%;

          &.communication-block:first-child {
            width: calc(24% - 25px);
            margin-right: 15px;
          }

          &.communication-block:nth-child(2n) {
            width: calc(22% - 20px);
            margin-right: 15px;
          }

          &.communication-block:nth-child(3n) {
            width: calc(21% - 20px);
            margin-right: 25px;
          }

          &.communication-block:nth-child(4n) {
            width: calc(27% - 15px);
            margin-right: 20px;
          }

          p.label {
            font-size: 12px;
            color: #808080;
            line-height: 16px;
          }

          p.data {
            font-size: 14px;
            color: #3d4e6b;
            word-break: break-word;
          }
        }
      }

      .mb-3 {
        margin-bottom: 7px;
      }

      .rowAlignment {
        display: flex;
      }

      .marginRight7 {
        margin-right: 7%;
      }
    }
  }
}

.recomend {
  .addnew {
    margin: -15px 20px 0 0;
  }

  .list-items {
    max-height: 418px;
    overflow: auto;
    width: 99%;
    padding: 15px 1% 0 0;
  }

  .tabUI {
    position: relative;
    top: -10px;

    button {
      background: transparent !important;
      width: 130px !important;
      margin: 0px !important;
      padding: 1px !important;
      font: normal normal 600 12px/16px Roboto !important;
      letter-spacing: 0px;
      color: #808080 !important;
    }

    .MuiTabs-fixed {
      margin-bottom: 5px;
      border-bottom: 1px solid #dfdfdf;
    }

    .Mui-selected {
      color: #0065ff !important;
      font-weight: 600 !important;
    }

    .MuiTabs-indicator {
      background-color: #0065FF;
    }
  }
}

.recomend-block {
  display: block;

  .customer-selection {
    display: inline-block;
    background: #c1f6ff;
    display: inline-block;
    text-transform: uppercase;
    padding: 5px 10px;
    font-size: 10px;
    font-weight: 600;
    color: #00b8d9;
    border-radius: 14px;
  }

  .Other-selection {
    display: inline-block;
    background: #dedaf5;
    display: inline-block;
    text-transform: uppercase;
    padding: 5px 10px;
    font-size: 10px;
    font-weight: 600;
    color: #a79ce3;
    border-radius: 14px;
  }

  .row {
    display: flex;
    align-items: flex-end;
    border-bottom: 1px solid #d5d5d5;
    padding: 0 0 20px;
    margin-bottom: 20px;

    .inner-block {
      width: 20%;

      &.plan {
        color: #253858;
        font-size: 13px;
        line-height: 19px;
        font-weight: 600;
        margin-right: calc(18% - 125px);
      }

      &.select {
        width: 23%;
        margin-right: 2%;
        ;

        p.label {
          color: #808080;
          font-size: 12px;
        }

        p.date {
          color: #253858;
          font-size: 13px;
          font-weight: 600;
        }
      }

      &.productPageInfo {
        width: 17%;
        margin-right: 2%;

        .label {
          color: #808080;
          font-size: 12px;
        }

        .date {
          color: #253858;
          font-size: 13px;
          font-weight: 600;
        }
      }

      &.interest {
        margin-right: 3%;
        width: 35%;

        p.heading {
          color: #808080;
          font-size: 12px;
          font-weight: 600;
          text-align: center;
          padding-bottom: 5px;
        }

        .grey-block {
          background: #f4f4f4;
          border-radius: 16px;
          display: block;
          padding: 7px 0 5px;
          text-align: center;

          .icon {
            width: 38px;
            height: 30px;
            background: url("/public/images/face.svg") no-repeat;
            background-size: 100%;
            margin-right: 5px;
            display: inline-block;
            cursor: pointer;
            background-position: 0 0;

            &.one {
              background-position: 0px 0px;
              filter: grayscale(100%);

              &.active {
                filter: grayscale(0%);
              }
            }

            &.two {
              background-position: 0 -40px;
              filter: grayscale(100%);

              &.active {
                filter: grayscale(0%);
              }
            }

            &.three {
              background-position: 0 -78px;
              filter: grayscale(100%);

              &.active {
                filter: grayscale(0%);
              }
            }

            &.four {
              background-position: 0 -115px;
              filter: grayscale(100%);

              &.active {
                filter: grayscale(0%);
              }
            }

            &.five {
              background-position: 0 -153px;
              filter: grayscale(100%);

              &.active {
                filter: grayscale(0%);
              }
            }
          }
        }
      }

      &.booking {
        p.heading {
          color: #808080;
          font-size: 12px;
          text-align: center;
          padding: 0;
          margin: 0;

          span.premium {
            color: #0065ff;
            font-size: 14px;
            font-weight: 600;
            padding-left: 7px;
            display: inline-block;
            line-height: 0;
          }
        }

        a.book {
          color: #0065ff;
          display: block;
          padding: 8px;
          font-weight: 600;
          font-size: 12px;
          text-align: center;
          border: 1px solid #0065ff;
          border-radius: 8px;
          margin-top: 10px;
        }
      }
    }
  }

  .wishlist {
    display: inline-block;
    margin-left: 10px;
    position: relative;
    font-weight: 600;
    font-size: 13px;
    top: 1px;
    color: #252558;
  }
}

.notification-block {

  .MuiDialog-paperWidthSm {
    width: 766px !important;
    height: auto;
  }

  .MuiDialogTitle-root {
    padding: 15px 15px 10px;
  }

  .MuiDialogContent-root {
    padding: 0px 2px !important;
  }

  .NotificationAccordian {
    box-shadow: none;
    padding: 0px;
    border-top: 1px solid #eee;

    .MuiAccordionDetails-root {
      display: block;
      padding: 8px 0px 0px;
    }

    .MuiBadge-anchorOriginTopRightRectangle {
      top: -10px;
      right: 4px;
      transform: scale(1) translate(50%, -50%);
      transform-origin: 100% 0%;
    }

    .MuiBadge-badge {
      background-color: #e06666;
    }

    .MuiAccordionSummary-content {
      .MuiTypography-root {
        font: normal normal 600 14px/28px Roboto;
        letter-spacing: 0.22px;
        color: #253858;
      }

    }

    .MuiAccordionSummary-expandIcon {
      background-color: transparent;
      color: #0065ff;
      position: static;

      svg {
        font-size: 1.4rem;
      }
    }

    .MuiAccordionSummary-root {
      padding: 0px 15px 0px 25px;
    }

    .MuiAccordionSummary-root.Mui-expanded {
      min-height: 45px;

      .MuiAccordionSummary-content.Mui-expanded {
        margin: 8px 0px 0px;
      }
    }

    .NoNewNotification {
      font: normal normal 600 15px/28px Roboto;
      color: #253858;
      text-align: center;
    }
  }
}

.list-item {
  display: flex;
  padding: 11px 0;
  background: #e2e2e2;
  border-radius: 0px;
  margin-bottom: 4px;
  // width: 285px;
  align-items: center;

  &.active {
    background: #e5efff;
    // .icon {
    //   background: #2684ff;
    // }
  }

  .icon {
    width: 46px;
    height: 46px;
    border-radius: 8px;
    margin: 0 13px 0 18px;
    text-align: center;
    display: flex;
    align-items: center;

    span.ico {
      width: 24px;
      height: 24px;
      margin: 0 auto;
      background-size: 100%;
      display: inline-block;
      cursor: pointer;
      background-position: 0 0;
      // &.email {
      //   background-position: 0 -30px;
      // }
      // &.alert {
      //   background-position: 0 -60px;
      // }
      // &.whatsapp {
      //   background-position: 1px -92px;
      // }
    }

    &.alert {
      background-color: #ffbd6f;

      span.ico {
        background: url("/public/images/salesview/alert.svg") no-repeat;
      }
    }

    &.annoucement {
      background-color: #776fff;

      span.ico {
        background: url("/public/images/salesview/announcement.svg") no-repeat;
      }
    }

    &.notify {
      background-color: #26c777;

      span.ico {
        background: url("/public/images/salesview/notify.svg") no-repeat;
      }
    }

    &.notification {
      background-color: #ff9595;

      span.ico {
        background: url("/public/images/salesview/notifaction.svg") no-repeat;
      }
    }
  }

  .content-block {
    width: calc(100% - 74px);

    .head {
      text-align: left;
      font: normal normal 600 14px/28px Roboto;
      letter-spacing: 0.22px;
      color: #253858;
      opacity: 1;
      display: inline-block;
    }

    .text {
      text-align: left;
      font: normal normal normal 14px/18px Roboto;
      letter-spacing: 0.22px;
      color: #303030;
      opacity: 1;
    }

    .time {
      font-size: 10px;
      color: #808080;
    }

    .Notificationtime {
      text-align: right;
      font: normal normal 600 12px/16px Roboto;
      letter-spacing: 0.22px;
      color: #303030;
      opacity: 1;
      padding-right: 10px;
      display: inline-block;
      float: right;
    }
  }
}

.counter {
  background: #e06666;
  color: #fff;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  font-size: 10px;
  line-height: 20px;
  position: absolute;
  top: 4px;
  z-index: 999;
  right: 14px;
}

.NewBadge {
  span {
    background: #e06666;
    color: #fff;
    border-radius: 4px;
    font-weight: 600;
  }
}

.fosIconPayOverdueV2 {
  margin-top: 5px;
  filter: opacity(0.7);
}

.footer-bar {
  background: #e0e0e0 0% 0% no-repeat padding-box;
  box-shadow: 0px -4px 16px #00000014;
  opacity: 1;
  padding: 0;
  position: fixed;
  height: 68px;
  z-index: 99;
  bottom: 0;
  width: calc(100% - 140px);
  border-radius: 2px;

  .tabBTN {
    margin: 17px 38px 0px;
    border-radius: 20px;
    padding: 0px;
    min-height: 30px;
    background-color: #f4f4f4;
    width: 135px;
  }

  .MuiTab-root {
    min-height: 33px;
    padding: 6px 9px 6px 13px !important;
    border-radius: 20px;
  }

  .Mui-selected {
    background-color: #0065ff;
    color: #fff !important;
    padding: 5px 17px !important;
  }

  .MuiTabs-indicator {
    display: none;
  }
  .blink-dot {
    height: 10px;
    width: 10px;
    background-color: red;
    border-radius: 50%;
    display: inline-block;
    margin-left: 2px;
    animation: blink-dot 1s infinite;
    vertical-align: middle;
  } 
}

@keyframes blink-dot {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.OnlineCustomerDrawer {
  .text-right {
    text-align: right;
    padding: 2px;
    margin: 2px;
  }
}

.MuiFab-root.OnlineCustomerFabBtn {
  position: fixed;
  right: 10px;
  bottom: 60px;
  z-index: 2;
}

.footer-bar-mobile {
  background: white 0% 0% no-repeat padding-box;
  opacity: 1;
  padding: 0;

  // height: 68px;
  // z-index: 99;
  // bottom: 0;
  // border-radius: 2px;
  .tabBTN {
    // margin: 17px 38px 0px;
    // border-radius: 20px;
    // padding: 0px;
    // min-height: 30px;
    // background-color: #f4f4f4;
    // width: 135px;
    // justify-content: center;
  }

  .MuiTab-root {
    min-height: 33px;
    padding: 6px 9px 6px 13px !important;
    border-radius: 20px;
  }

  .Mui-selected {
    background-color: #0065ff;
    color: #fff !important;
    padding: 5px 17px !important;
  }

  .MuiTabs-indicator {
    display: none;
  }

  .MuiTabs-flexContainer {
    justify-content: center;
  }

  #OnlineCustomers {
    height: 300px;
    overflow-y: scroll;

    .footer-list {
      display: block;
      // position: relative;
      padding: 0;
      -webkit-user-select: text;
      -khtml-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      user-select: text;
      align-items: flex-start;

      .footer-bl {
        margin: 0 2px;
        padding: 4px 1px 4px 1px;
        background: white;
        border-right: 1px solid #d5d5d5;
        display: flex;
        align-items: center;

        .right-block {
          cursor: pointer;
          display: flex;
          justify-content: space-around;
          width: 100%;

          .name {
            width: 30%;
            font: normal normal normal 12px/16px Roboto;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .id-date {
            font: normal normal normal 12px/16px Roboto;
            width: 35%;

            span {
              color: dimgray;
              border-bottom: dotted;
            }
          }

          p:nth-last-child(1) {
            width: 30%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .bookLead {
            text-align: center;
            font: normal normal normal 10px/21px Roboto;
            letter-spacing: 0.14px;
            color: #303030;
            opacity: 1;
            width: 100%;
          }
        }
      }
    }
  }

  .noCustomerbox {
    height: auto !important;
    overflow-y: inherit !important;
  }
}

.no-customer {
  padding: 0px 0px 8px;
  font-size: 18px;
  text-align: center;
  font-weight: 500;
  background: #eff3f6 0% 0% no-repeat padding-box;
  box-shadow: 0px -4px 6px #00000014;
  font: normal normal 12px/10px Roboto;
  letter-spacing: 0px;
  color: #303030;

  img {
    margin-right: 5px;
    position: relative;
    top: 16px;
  }

  p {
    font: normal normal normal 10px/22px Roboto;
    letter-spacing: 0px;
    padding-left: 97px;
    color: #808080;
  }
}

.footer-list {
  display: flex;
  position: relative;
  padding: 8px 0 0;
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  align-items: flex-start;

  .footer-bl {
    margin: 0 20px 5px 0;
    padding: 2px 20px 6px 0;
    background: #e0e0e0;
    border-right: 1px solid #d5d5d5;
    display: flex;
    align-items: center;

    .right-block {
      cursor: pointer;

      .name {
        color: #253858;
        letter-spacing: 0.6px;
        padding: 0;
        margin: 0;
        font: normal normal normal 12px/16px Roboto;
      }

      .id-date {
        font-size: 10px;
        color: #808080;
        padding: 0;
        margin: 0;
        line-height: 12px;
        font: normal normal normal 12px/16px Roboto;
      }

      p:nth-last-child(1) {
        background: #d5ffed;
        color: #19d888;
        border-radius: 4px;
        text-align: center;
        font: normal normal 600 10px/21px Roboto;
        letter-spacing: 0.11px;
        text-transform: uppercase;
        opacity: 1;

        &.violet {
          background: #dad5ff;
          color: #8274e8;
        }

        &.orange {
          background: #ffe4c4;
          color: #edaa5d;
        }
      }

      .bookLead {
        text-align: center;
        font: normal normal normal 10px/21px Roboto;
        letter-spacing: 0.14px;
        color: #303030;
        opacity: 1;
      }
    }
  }
}

.footer-bar .arrow-prev,
.footer-bar .arrow-next {
  padding: 0px 0 0;
}

.top-row {
  margin-top: -40px;
  display: flex;
  align-items: center;
  width: 100%;

  .grid-icons {
    display: inline-block;
    margin-left: 10px;
    width: calc(100% - 485px);

    .landscape-ico {
      display: inline-block;
      border: 1px solid #2684ff;
      border-width: 1px 1px 1px 1px;
      margin-right: 1px;
      width: 24px;
      height: 21px;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      cursor: pointer;
      position: relative;
      vertical-align: super;

      span.line {
        width: 12px;
        height: 2px;
        background: #2684ff;
        float: left;
        margin: 9px 0 0 5px;
        position: relative;

        &::before {
          width: 12px;
          height: 2px;
          background: #2684ff;
          position: absolute;
          top: -4px;
          content: "";
          display: inline-block;
        }

        &::after {
          width: 12px;
          height: 2px;
          background: #2684ff;
          position: absolute;
          bottom: -4px;
          content: "";
          display: inline-block;
        }
      }
    }

    .landscape-ico-active {
      display: inline-block;
      background: #2684ff;
      border: 1px solid #2684ff;
      border-width: 1px 1px 1px 1px;
      margin-right: 1px;
      width: 24px;
      height: 21px;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      cursor: pointer;
      position: relative;
      vertical-align: super;

      span.line {
        width: 12px;
        height: 2px;
        background: #ffffff;
        float: left;
        margin: 9px 0 0 5px;
        position: relative;

        &::before {
          width: 12px;
          height: 2px;
          background: #ffffff;
          position: absolute;
          top: -4px;
          content: "";
          display: inline-block;
        }

        &::after {
          width: 12px;
          height: 2px;
          background: #ffffff;
          position: absolute;
          bottom: -4px;
          content: "";
          display: inline-block;
        }
      }
    }

    .vertical-ico-active {
      display: inline-block;
      background: #2684ff;
      width: 24px;
      height: 21px;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      cursor: pointer;
      vertical-align: super;
      margin-left: -1px;

      span.line {
        width: 2px;
        height: 12px;
        background: #ffffff;
        float: left;
        margin: 5px 0 0 11px;
        position: relative;

        &::before {
          width: 2px;
          height: 12px;
          background: #ffffff;
          position: absolute;
          left: -5px;
          content: "";
          display: inline-block;
        }

        &::after {
          width: 2px;
          height: 12px;
          background: #ffffff;
          position: absolute;
          right: -5px;
          content: "";
          display: inline-block;
        }
      }
    }

    .vertical-ico {
      display: inline-block;
      border: 1px solid #2684ff;
      width: 24px;
      height: 21px;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      cursor: pointer;
      vertical-align: super;
      margin-left: -1px;

      span.line {
        width: 2px;
        height: 12px;
        background: #2684ff;
        float: left;
        margin: 5px 0 0 11px;
        position: relative;

        &::before {
          width: 2px;
          height: 12px;
          background: #2684ff;
          position: absolute;
          left: -5px;
          content: "";
          display: inline-block;
        }

        &::after {
          width: 2px;
          height: 12px;
          background: #2684ff;
          position: absolute;
          right: -5px;
          content: "";
          display: inline-block;
        }
      }
    }
  }

  .rhs-blk {
    position: absolute;
    right: 0;
  }

  .right-block {
    width: auto;
    display: flex;
    align-items: center;
    float: right;
    margin-right: 90px;
  }

  .code-block {
    border-radius: 30px;
    background: #fff7d5;
    display: inline-block;
    width: 132px;
    height: 32px;
    margin: 0 5px 0 30px;

    .inner-border {
      width: 126px;
      height: 26px;
      border-radius: 30px;
      border: 1px dashed #aa9f6e;
      margin: 3px 0 0 3px;

      .text {
        margin: 3px 0 0 7px;
        color: #aa9f6e;
        display: inline-block;
      }

      .code {
        margin: 3px 0 0 12px;
        color: #000;
        display: inline-block;
        letter-spacing: 3px;
      }
    }
  }

  .get-code {
    margin-left: 10px;
    border-right: 1px solid #d5d5d5;
    padding: 5px 20px 5px 0px;
    margin-right: 15px;

    a {
      display: inline-block;
      color: #2684ff;
      font-size: 12px;
      font-weight: 600;
    }
  }
}

.action-buttons {
  a {
    display: flex;
    align-items: center;
    cursor: pointer;
    border-right: 1px solid #ddd;
    padding-right: 10px;
    margin-right: 0;

    .icon {
      margin: 5px;
      height: 28px;
      background-size: 100%;
      display: inline-block;
      color: #2684ff;
    }

    span.text {
      font-size: 12px;
      color: #2684ff;
      font-weight: 600;
      display: inline-block;
    }
  }
}

// .MuiPaper-elevation8 {
//   position: relative;
//   width: 280px;
//   // overflow: visible !important;
// }

.MuiPaper-elevation8:after {
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #fff;
  content: "";
  position: absolute;
  left: -8px;
  bottom: 42px;
}

a {
  cursor: pointer;
  color: #2684ff;
}

iframe {
  border: none;
}

.MuiButton-containedPrimary,
.MuiButton-containedPrimary {
  background: #0065ff;
}

.MuiButton-label {
  text-transform: capitalize !important;
}

.ContinueJourney .MuiButtonGroup-groupedContainedHorizontal:not(:last-child) {
  width: 100%;
}

.vertical .ContinueJourney .MuiButtonGroup-contained {
  width: 100%;
}

.ContinueJourney .MuiButtonGroup-groupedContainedHorizontal:not(:last-child) {
  border: 0;
}

.card-design.vertical.disable::before {
  content: "";
  width: 100%;
  height: 82%;
  position: absolute;
  top: 120px;
  left: 0;
  background: #000;
  opacity: 0.4;
  z-index: 999;
  border-radius: 0 0 16px 16px;
}

// .card-design.landscape.disable::before {
//   content: "";
//   width: 100%;
//   height: 77%;
//   position: absolute;
//   top: 78px;
//   left: 0;
//   background: #000;
//   opacity: 0.4;
//   z-index: 999;
//   border-radius: 0 0 16px 16px;
// }

.cursorPointer {
  cursor: pointer;
}

.vcBtnDisabled {
  opacity: 0.5;
  display: flex;
  cursor: wait;
}

.birthdayCakeIcon {
  vertical-align: text-bottom;
  margin-left: 10px;
  display: initial !important;
}

.circularProgressIcon {
  position: fixed;
  z-index: 200;
  top: 50%;
  left: 50%;
}

.vipCustomerImg {
  height: 39px;
  vertical-align: middle;
  // vertical-align: text-bottom;
}

.spacehight {
  margin-top: 5em;
}

.headline {
  border-radius: 5px;
  text-align: center;
  background-color: #306bff;
  padding: 6px;
  box-shadow: 0px 0px 16px #00000014;
  color: #fff;
  font-size: 13px;
  letter-spacing: 0.17px;
  max-width: 950px;
  margin: auto auto 7px;
  // font-weight: 600;
}

.selectInsurerPopup {
  button {
    background: #0065ff;
    border-radius: 8px;
    width: 100%;
    margin-top: 35px;
    letter-spacing: 0.17px;
    padding: 8px;
    border: none;
    color: #ffffff;
    font: normal normal 12px/21px Roboto;
    cursor: pointer;
    outline: none;
  }
}

.multipleCallPopup {
  top: 75px !important;
  background-color: #00b746 !important;

  div {
    background: #00b746 0% 0% no-repeat padding-box;
  }

  #client-snackbar {
    display: block;

    svg {
      display: none;
    }

    p {
      text-align: left;
      font: normal normal normal 12px/17px Roboto;
      letter-spacing: 0.17px;
      color: #ffffff;
      width: 350px;
    }

    p:last-child {
      text-align: left;
      font: normal normal bold 12px/17px Roboto;
      letter-spacing: 0.17px;
      color: #ffffff;
    }
  }

  a {
    font: normal normal bold 10px/17px Roboto;
    letter-spacing: 0.14px;
    color: #ffffff !important;
    text-transform: uppercase;
  }
}

.commentsPopup {
  top: 75px !important;
  background-color: #00b746 !important;

  div {
    background: #00b746 0% 0% no-repeat padding-box;
    font: normal normal normal 14px/17px Roboto;
  }
}

.wfhNewAgentNotConnected {
  top: 75px !important;
  background-color: #17a2b8 !important;

  div {
    background: #17a2b8 0% 0% no-repeat padding-box;
  }

  #client-snackbar {
    display: block;

    svg {
      display: none;
    }

    p {
      text-align: left;
      font: normal normal normal 12px/17px Roboto;
      letter-spacing: 0.17px;
      color: #ffffff;
      width: 350px;
    }

    p:last-child {
      text-align: left;
      font: normal normal bold 12px/17px Roboto;
      letter-spacing: 0.17px;
      color: #ffffff;
    }
  }

  a {
    font: normal normal bold 10px/17px Roboto;
    letter-spacing: 0.14px;
    color: #ffffff !important;
    text-transform: uppercase;
  }
}

.custFeedBackBtn {
  background: #fbac49 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 16px #00000014;
  border-radius: 8px;
  padding: 10px;
  border: none;
  outline: none;
  font: normal normal normal 12px/17px Roboto;
  letter-spacing: 0.17px;
  color: #ffffff;
  opacity: 1;
  margin-top: 10px;
  cursor: pointer;
}

.cusFeedbackPopup {
  .MuiPaper-rounded {
    background: #191f40 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000029;
    border-radius: 4px;
    opacity: 1;
    color: #fff;
    width: 671px;
    padding-bottom: 0px;

    img {
      margin-top: 8px;
    }

    h2 {
      font-size: 14px;
      font-family: "Roboto";
      margin-bottom: 6px;
      margin-left: 10px;
    }

    p {
      font-size: 12px;
      font-family: "Roboto";
      margin-left: 10px;
    }
  }
}

.ViewInteraction {
  width: 500px;

  em {
    font: normal normal 10px/21px Roboto;
    letter-spacing: 0.14px;
    color: rgba(0, 0, 0, 0.5);
  }

  p {
    overflow-wrap: break-word;
  }

  p:nth-child(2n) {
    margin-bottom: 10px;
  }
}

.SelectContactNumberPopup {
  .CallBtn {
    color: green;
  }

  .disabled {
    color: rgba(160, 160, 160, 0.29);
  }

  .MuiDialog-paperWidthSm {
    max-width: 600px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    height: auto;
  }

  .MuiTableCell-root {
    font: normal normal normal 12px/16px Roboto;
    padding: 7px;
    color: #808080;
    border-bottom: none;
  }

  .MuiPaper-elevation1 {
    box-shadow: none;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  h6 {
    text-align: left;
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
    padding-left: 2px;
  }

  .subTitile {
    position: relative;
    top: -16px;
  }

  .MuiTableRow-root {
    td {
      font: normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      border: none;
    }
  }

  .row:nth-child(odd) {
    background-color: #f8f8f8;
    border-radius: 4px;
  }
}

.MarkImportant {
  svg {
    color: #0065ff;
  }

  .red {
    color: #e33f3f;
  }
}

.customerEmails {
  background: #ffffff 0% 0% no-repeat padding-box;
  border-radius: 16px;
  padding: 18px 20px;
  position: relative;
  margin-top: 15px;

  h3 {
    text-align: left;
    font: normal normal 600 16px/24px Roboto;
    letter-spacing: 0.26px;
    color: #253858;
    margin-bottom: 1em;
  }

  .userName {
    text-align: left;
    font: normal normal 600 12px/21px Roboto;
    letter-spacing: 0.17px;
    color: #364765;
    opacity: 1;
    display: inline-block;
  }

  .time {
    text-align: right;
    font: normal normal normal 10px/21px Roboto;
    letter-spacing: 0.14px;
    color: #808080;
    opacity: 1;
    display: inline-block;
    float: right;
  }

  .commentMsg {
    text-align: left;
    font: normal normal normal 10px/17px Roboto;
    letter-spacing: 0.14px;
    color: #808080;
    opacity: 1;
    margin-bottom: 15px;
  }

  button {
    border: 1px solid #0065ff;
    border-radius: 8px;
    color: #0065ff;
    outline: none;
    font-weight: 600;
    font-size: 12px;
    letter-spacing: 0.17px;
  }
}

.customerImportantInfo {
  margin-bottom: 15px;
  background: #fff9e6;
  border-radius: 16px;
  padding: 15px 20px;
  position: relative;

  // margin-top: 15px;
  .expandmoreIcon {
    svg {
      color: #eb8336;
    }
  }

  h3 {
    text-align: left;
    font: normal normal bold 14px/22px Roboto;
    letter-spacing: 0px;
    color: #eb8336;
    opacity: 0.8;
  }

  ul {
    list-style-type: circle;
    margin-top: 10px;

    li {
      font: normal normal normal 14px/22px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 0.8;
    }
  }
}

.customerEmailsPopup {
  padding: 0 !important;
  margin-top: 0 !important;

  .userName {
    font: normal normal 600 14px/21px Roboto;
  }

  .commentMsg {
    font: normal normal normal 12px/17px Roboto;
    max-width: 400px;
  }
}

.readEmailPopup {
  .MuiDialog-paperWidthSm {
    max-width: 930px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 930px;
    height: auto;
  }

  .MuiPaper-root {
    height: 500px;

    iframe {
      overflow-y: hidden;
      width: 600px;
      height: 100%;

      .mailContentbox div.rightpanel {
        width: 100%;
        height: auto;
      }
    }
  }
}

.jagBannerIframe {
  position: relative;
  margin-bottom: 10px;
}

.dkdBannerIframe {
  position: relative;
  margin-bottom: 10px;
}

.VideoCallPopup {
  .MuiDialog-paperWidthSm {
    padding: 0px;
    height: auto;
  }

  .MuiDialogContent-root {
    padding: 0px !important;
  }

  .videoPopup {
    display: flex;
    max-width: 678px;
  }

  .popupLeftSection {
    width: 52%;
    background-color: #fff;
    padding: 22px 10px 0px 18px;
    border-radius: 16px 0px 0px 16px;
  }

  .popupRightSection {
    width: 48%;
    height: 100%;
    background: #739af0 0% 0% no-repeat padding-box;
    border-radius: 0px 16px 16px 0px;
    opacity: 1;
    padding: 22px 18px 20px 25px;
    text-align: center;

    .MuiIconButton-root {
      background-color: transparent;
    }
  }

  .comments {
    text-align: left;
    font: normal normal 300 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    margin-bottom: 12px;
  }

  .btnContainer {
    box-shadow: 0px 6px 16px #3469cb29;
    border-radius: 8px;
    opacity: 1;
    padding: 10px 15px;
    height: 56px;
    margin-bottom: 15px;

    img {
      float: left;
    }
  }

  .already-vc-join {
    background: white;
    border: none;
  }

  .already-vc-join-container {
    box-shadow: 0px 6px 16px #3469cb29;
    border-radius: 8px;
    opacity: 1;
    padding: 10px 15px;
    height: 56px;
    margin-bottom: 15px;
    margin-top: 10px;

    img {
      float: left;
    }
  }

  .already-running-vc-message {
    font: 600 16px "Roboto", sans-serif;
    color: red;
  }

  .shareScreen {
    text-align: left;
    font: normal normal normal 14px/21px Roboto;
    letter-spacing: 0.2px;
    color: #2e2e2e;
    opacity: 1;
    padding-top: 6px;
    padding-left: 50px;
  }

  .vcTextdisabled {
    padding-left: 21px;
  }

  .sendInvite {
    width: 100%;
    text-align: center;
    font: normal normal bold 14px/21px Roboto;
    letter-spacing: 0.2px;
    color: #ffffff;
    background: #0065ff 0% 0% no-repeat padding-box;
    border-radius: 8px;
    border: none;
    outline: none;
    padding: 16px 15px;
    margin-top: 4px;
  }

  .send-invite-disabled {
    cursor: not-allowed;
    opacity: 0.6;
    background-color: #cccccc;
    border: 1px solid #999999;
    color: #666666;
  }

  .VChighlight {
    text-align: center;
    font: normal normal 300 24px/32px Roboto;
    letter-spacing: 0px;
    color: #ffffff;
    opacity: 1;
    margin-bottom: 40px;
    margin-top: 40px;
  }

  .expireWarning {
    color: #D67E50;
    padding-top: 15px;
    font: 500 14px 'Roboto', sans-serif;

    .MuiSvgIcon-root {
      font-size: 14px !important;
    }
  }

  .copyMessageWarning {
    color: #D67E50;
    padding-top: 8px;
    font: 500 14px 'Roboto', sans-serif;

    .MuiSvgIcon-root {
      font-size: 14px !important;
    }
  }

  .leftcrossbtn {
    display: none;
  }

  .vcInsideInfo {
    color: #e65e22;
    font-weight: bold;
    font-size: 15px;

    .MuiSvgIcon-root {
      font-size: 14px !important;
    }
  }

  .copyCustUrl {
    text-align: center;
    font: normal normal bold 14px/21px Roboto;
    letter-spacing: 0.2px;
    color: #0065ff;
    // background: #32a8a8 0% 0% no-repeat padding-box;

    margin-top: 3px;
  }

}

// Below code for not on matrix call notification alert
.close-popup-button {
  background: #FC6A64 0% 0% no-repeat padding-box;
  border: 1.5px solid var(--unnamed-color-ffffff);
  border: 2px solid #FFFFFF;
  border-radius: 6px;
  margin-left: 10px;
  text-align: center;
  font: normal 18px/24px Roboto;
  letter-spacing: 0px;
  color: #FFFFFF;
  text-transform: uppercase;
  opacity: 1;
  padding: 9px 35px;
  font-weight: bold;
  cursor: pointer;
}

.popup-okay-button {
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup-info-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
}

.popup-warning-message {
  margin-left: 20px;
  width: 100%;
  color: #fff;
  font-size: 16px;
  line-height: 30px;
}

/*Start CSAT rating css */
.CsatratingPopup {
  .MuiDialog-paperWidthSm {
    padding: 0px;
    width: 348px;
    height: 530px;
    border-radius: 16px;

    .MuiTypography-h6 {
      text-align: left;
      font: normal normal bold 16px/21px Roboto;
      letter-spacing: 0.22px;
      color: #303030;
      opacity: 1;
    }

    .MuiTabs-flexContainer {
      align-content: center;
      justify-content: center;
    }

    .MuiDialogContent-root {
      padding: 0px !important;
      max-height: 600px !important;

      .MuiTableContainer-root {
        box-shadow: none;
      }

      .popupWrapper {
        height: 477px !important;
      }
    }
  }
}

/*end csat rating css */

/* force rating css*/

.rollover-panel-table {
  .MuiTableContainer-root {
    max-height: 60vh;

    .MuiTableCell-root {
      padding: 12px 16px;
    }
  }

  .MuiTableHead-root {
    .MuiTableCell-head {
      font-weight: 600;
      font-size: 14px;
      white-space: nowrap;
    }
  }

  .MuiTableBody-root {
    .MuiTableRow-root {
      &:nth-of-type(odd) {
        background-color: #f5f5f5;
      }

      .MuiTableCell-body {
        border-bottom: 1px solid #e0e0e0;
        font-size: 13px;
      }
    }
  }

  .MuiTablePagination-root {
    border-top: 1px solid #e0e0e0;
    background-color: #fff;
  }
}

.RatingContainer {
  background-color: $base-color;
  padding: 45px 30px 20px;

  .heading {
    text-align: left;
    font: normal normal 600 24px/35px Poppins;
    letter-spacing: 0px;
    color: #363636;
    opacity: 1;
    margin-top: 0em;
  }

  .subheading {
    text-align: left;
    font: normal normal 600 12px/20px Poppins;
    letter-spacing: 0px;
    color: #363636;
    opacity: 1;
    margin-top: 0em;
  }

  ul {
    text-align: left;
    font: italic normal normal 12px/18px Poppins;
    letter-spacing: 0px;
    color: #000000;
    background-color: $base-white-color;
    border: 1px dashed #c47474;
    border-radius: 8px;
    opacity: 1;
    list-style-type: none;
    padding: 10px 12px;
  }

  .RatingBox {
    display: flex;
    justify-content: space-between;

    .Box {
      background-color: $base-white-color;
      border: 1px solid #2a63f629;
      border-radius: 12px;
      opacity: 1;
      padding: 15px;
      width: 33%;
      height: 125px;
      margin: 0px 5px 15px;

      &:first-child {
        margin-left: 0px;
        background-color: $base-box-color;
        width: 445px;

        p {
          color: #fff;
        }

        h3 {
          color: #fff;
        }
      }

      &:last-child {
        margin-right: 0px;
        width: 500px;
      }

      p {
        font: normal normal 600 12px/18px Poppins;
        letter-spacing: 0px;
        color: #253858;
        opacity: 1;
        margin: 8px 0px;
      }

      h3 {
        font: normal normal 600 24px/35px Poppins;
        letter-spacing: 0px;
        color: #253858;
        opacity: 1;
      }

      .forceRatingTooltip {
        float: right;
        position: relative;
        top: -5px;
        right: -5px;
      }
    }
  }

  .calendarBox {
    background-color: $base-white-color;
    border: 1px solid #2a63f61f;
    border-radius: 12px;
    opacity: 1;
    width: 100%;
    height: auto;
    padding: 10px 0px;

    .MonthYearcalendar {
      .yearBox {
        text-align: center;
        font: normal normal 600 12px/18px Poppins;
        letter-spacing: 0px;
        color: #000000;
        opacity: 1;
        display: flex;
        justify-content: space-around;
        align-items: center;

        svg {
          cursor: pointer;
        }

        .disabledIcon {
          color: #e8e8e8;
        }
      }

      .MonthBox {
        display: flex;
        list-style-type: none;
        border: none;
        flex-wrap: wrap;

        li {
          width: 53px;
          background: #9b9b9b;
          font: normal normal 600 12px/18px Poppins;
          letter-spacing: 0px;
          color: #f5f5f5;
          text-align: center;
          margin: 7px;
          padding: 4px;
          height: 25px;
        }

        .disabled {
          background-color: #e8e8e8;
        }

        .active {
          background-color: #0065ff;
        }
      }
    }

    hr {
      margin-bottom: 12px;
      background-color: #eee;
      border: none;
      height: 1px;
    }

    .RatingList {
      p {
        display: flex;
        justify-content: space-between;
        border-left: 5px solid #797da1;
        padding: 2px 15px 2px 10px;
        font: normal normal 600 12px/18px Poppins;
        letter-spacing: 0px;
        color: #000000;
        margin-bottom: 18px;

        &:nth-child(1) {
          border-left: 5px solid #4faf3d;
        }

        &:nth-child(2) {
          border-left: 5px solid #9b9b9b;
        }

        &:nth-child(3) {
          border-left: 5px solid #e5a278;
        }

        &:last-child {
          border-left: 5px solid #a7c6ad;
        }
      }
    }
  }

  .tabledata {
    // box-shadow: none;
    overflow-y: auto;
    // height: 500px;

    // tr:nth-of-type(even) {
    //   background-Color: #F9FBFC,
    // }

    .MuiTableCell-body {
      border: none;
      font: normal normal 600 12px/16px Roboto;
      letter-spacing: 0px;
      color: #00000099;
      padding: 8px;

      svg {
        color: #0065FF;
        cursor: pointer;

        &:hover {
          background-color: #0065ff1a;
          border-radius: 20px;
        }
      }
    }

    .Red {
      color: #E6695F;
    }

    .Amber {
      color: #ffbf00;
    }

    .Green {
      color: #00B222;
    }
  }
}

.forceRatingTooltipPopup {
  .MuiTooltip-tooltip {
    background-color: #fff;
    opacity: 1;
    max-width: 230px;
    padding: 10px 15px 5px;

    p {
      font: normal normal 600 11px/18px Poppins;
      letter-spacing: 0px;
      color: #253858;
    }

    .msg {
      text-align: justify;
      font-style: italic;
      margin-top: 5px;
      font-weight: 500;

      svg {
        font-size: 19px;
        position: relative;
        color: #0065ff;
        top: 5px;
      }
    }

    .textred {
      color: #d67064;
    }

    ul {
      display: flex;
      flex-wrap: wrap;
      list-style-type: none;

      li {
        width: 70%;
        font: normal normal 600 12px/18px Poppins;
        letter-spacing: 0px;
        color: #253858;
        opacity: 1;
        margin: 8px 0px;

        &:nth-child(even) {
          text-align: right;
          width: 30%;
        }

        &:last-child {
          text-align: right;
          width: 100%;
          font-weight: 500;
        }
      }
    }

    .MuiTooltip-arrow {
      color: #fff;
    }

  }
}

.SuperProcessTooltip {
  .MuiTooltip-tooltip {
    background-color: #fff;
    opacity: 1;
    max-width: 230px;
    padding: 10px 15px 5px;

    p {
      font: normal normal 600 11px/18px Poppins;
      letter-spacing: 0px;
      color: #253858;
    }

    .msg {
      text-align: justify;
      font-style: italic;
      margin-top: 5px;
      font-weight: 500;
    }

    .textred {
      color: #d67064;
    }

    ul {
      list-style-type: none;

      li {
        width: 100%;
        font: normal normal 600 12px/18px Poppins;
        letter-spacing: 0px;
        color: #253858;
        opacity: 1;
        margin: 8px 0px;
      }
    }

    .MuiTooltip-arrow {
      color: #fff;
    }
  }

}

.infoIcon {

  font-size: 15px !important;
  position: relative;
  top: 3px;

}

.RedinfoIcon {

  font-size: 15px !important;
  position: relative;
  top: 3px;
  color: red;

}

/* force rating css*/

@-webkit-keyframes fadeInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.fadeInRight {
  animation: fadeInRight 2s infinite;
  background-color: #FFD72A;
  padding: 7px 10px;
  margin: 12px 1px;
  border-radius: 4px;
  border: 1px solid #d2c63d;
  color: #000;
}

@media only screen and (max-width:640px) {
  .BusinessRating {
    width: 100% !important;
    font: normal normal normal 16px/24px Roboto !important;

    button {
      font: normal normal normal 12px/19px Roboto !important;
    }
  }

  .popupRightSection {
    display: none;
  }

  .VideoCallPopup .popupLeftSection {
    width: 100%;
  }

  .leftcrossbtn {
    display: block;
  }

  .mid-row {
    margin: 9px 0 0;
    display: flex;
    flex-wrap: wrap;

    div {
      margin-right: 0px !important;
      margin-bottom: 15px;
      width: 100% !important;

      &.communication-block:first-child {
        width: 100% !important;
      }

      &.communication-block:nth-child(2n) {
        width: 100% !important;
      }

      &.communication-block:nth-child(3n) {
        width: 100% !important;
      }

      &.communication-block:nth-child(4n) {
        width: 100% !important;
      }
    }
  }

}

.DocListingPopUp {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 1090px;
    height: 600px !important;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      max-height: 600px;
      padding: 0px;

      .popupWrapper {
        height: 600px;
      }
    }

    iframe {
      border: none;

      body {
        background-color: #fff;
        margin: 0px !important;
      }
    }
  }
}

.viewAllSmeStatusPopup {
  .MuiDialog-paperWidthSm {
    width: auto;
    height: auto;
  }
}

.button-bg {
  background: #0065ff;
}

.button-txt {
  color: #0065ff;
  border-color: #0065ff;
}

/* Customer Research Complete Dialog Styles */
.customerResearchDialog {
  .MuiPaper-root {
    max-width: 480px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    height: auto;
    padding-bottom: 10px;
    overflow: visible;
  }

  .dialogHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: none;
  }

  .dialogTitle {
    margin: 0;
    font: normal normal 500 16px/24px Roboto;
    letter-spacing: 0.2px;
    color: #333333;
    margin-right: 10px;
  }



  .MuiDialogContent-root {
    padding: 8px 20px 20px 20px !important;
  }

  .dialogActions {
    justify-content: center;
    padding: 0;
    margin: 10px 0;
  }

  .blueButton {
    background: #0065ff;
    border-radius: 8px;
    width: auto;
    letter-spacing: 0.17px;
    padding: 10px 40px;
    border: none;
    color: #ffffff;
    font: normal normal 500 12px/21px Roboto;
    cursor: pointer;
    outline: none;
    margin: 0px 10px;
  }

  .NotAbleToConnectBtn {
    background: #fff;
    border: 1px solid #0065ff;
    border-radius: 8px;
    width: auto;
    letter-spacing: 0.17px;
    padding: 10px 20px;

    color: #0065ff;
    font: normal normal 500 12px/21px Roboto;
    cursor: pointer;
    outline: none;
    margin: 0px 10px;
  }
}

.editInfoPopup {
  .MuiDialog-paperWidthSm {
    width: 504px;
    height: auto;

    .MuiButtonBase-root {
      background-color: transparent;
      position: static;
      padding: 0px;

      svg {
        font-size: 1.6rem;
      }
    }

    .submitBtn {
      border-radius: 8px;
      letter-spacing: .17px;
      padding: 9px 21px !important;
      box-shadow: none;
      border: none;
      color: #fff;
      font: normal normal 600 12px/21px Roboto;
      cursor: pointer;
      outline: none;
      background-color: #0065ff !important;
      margin-top: 15px;
    }

    .editBtn {
      border: 1px solid #0065ff;
      border-radius: 8px;
      background-color: transparent;
      color: #0065ff;
      margin-top: 15px;
      box-shadow: none;
      letter-spacing: 0.17px;
      padding: 8px;
      font: normal normal 600 12px/21px Roboto;
      cursor: pointer;
      outline: none;
    }

  }
}


.FOSPopup {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 800px;
    // height: 600px !important;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      max-height: 550px;
      padding: 0px !important;
      overflow: hidden;

      .popupWrapper {
        background-color: #fff;
        // height: 1400px;
      }

      .MuiIconButton-root {
        background-color: transparent;
      }
    }

    iframe {
      border: none;
      height: 488px;

      body {
        background-color: #fff;
        margin: 0px !important;
      }
    }
  }

  .viewHistoryBtn {
    outline: none;
    border: none;
    font: normal normal 10px/12px Roboto;
    background: #deebff;
    border-radius: 8px;
    padding: 8px 15px;
    top: 15px;
    position: absolute;
    left: 243px;
    color: #0065ff;
  }
}

.FOSPopupligibleCity {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 800px;
    font: normal normal normal 20px/27px Roboto;
    // height: 600px !important;
    // padding-bottom: 0px !important;
    height: auto;

    .MuiDialogContent-root {
      // max-height: 600px;
      padding: 0px;
      overflow: hidden;

      .popupWrapper {
        background-color: #eff7fd;
        // height: 1400px;
      }
    }
  }
}

.SmeQuoteHistoryPopup {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    width: 800px;
    height: 100%;

    .MuiDialogContent-root {
      padding: 0px !important;

      .popupWrapper {
        background-color: #eff7fd;
        height: 100%;
      }
    }

    iframe {
      border: none;
      height: 100%;
    }
  }
}

.SmeuploadPopup {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    width: 1220px;
    height: 85%;
    max-height: 85%;
    margin: 0px;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      padding: 0px !important;

      .popupWrapper {
        background-color: #eff7fd;
        height: 100%;
      }
    }

    iframe {
      border: none;
      height: 100%;
    }
  }
}

.renewalDetails {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 900px;
    height: 600px !important;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      max-height: 600px;
      padding: 0px;

      .popupWrapper {
        background-color: #eff7fd;
        height: 600px;
      }
    }

    iframe {
      border: none;

      body {
        background-color: #fff;
        margin: 0px !important;
      }
    }
  }
}

.CallbackTrackerPopup {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 995px;
    height: 600px !important;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      max-height: 600px;
      padding: 0px;

      .popupWrapper {
        height: 600px;
      }
    }

    iframe {
      border: none;

      body {
        background-color: #fff;
        margin: 0px !important;
      }
    }
  }
}

.FOSAgentDashboardPopup,
.AgentCrossSellLead,
.HWOpportunity {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 1250px;
    height: 510px !important;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      max-height: 500px;
      padding: 0px;
      height: 500px;

      .popupWrapper {
        height: 500px;
      }
    }

    iframe {
      border: none;

      body {
        background-color: #fff;
        margin: 0px !important;
      }
    }
  }
}

.SmeLeadStatus {
  margin-top: 1.5em;
  background-color: #fff;
  border-radius: 16px;
  padding: 18px;
  letter-spacing: 0.17px;
  position: relative;

  h3 {
    color: #253858;
    font: normal normal 600 16px/24px Roboto;
    margin-bottom: 20px;
    letter-spacing: 0.26px;
  }
}

.SmeLeadStatusExternal {
  margin-top: 0;
}

.internalEmailPopup {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 900px;
    height: 620px !important;
    max-height: 100% !important;

    .MuiDialogContent-root {
      padding: 0px;
    }

    iframe {
      border: none;

      body {
        background-color: #fff !important;
        margin: 0px !important;
      }
    }
  }
}

.PODBtn {
  margin-left: 13px !important;
  float: left;
  height: 44px !important;
}

.UnassignBtn {
  margin-left: 13px !important;
  float: left;
  height: 44px !important;
  color: #FFFFFF;
  background-color: #0065ff !important;

  img {
    width: 27px;
  }
}

.PodClass {
  .MuiDialog-paperWidthSm {
    width: 900px;

    max-width: 900px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
  }
}

.MyPodLeads {
  .MuiDialog-paperWidthSm {
    width: 900px;

    max-width: 900px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
  }
}

.MyAppointments {
  .MuiDialog-paperWidthSm {
    width: 900px;

    max-width: 900px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
  }
}

.SMEQuoteLeads {
  .MuiDialog-paperWidthSm {
    width: 900px;

    max-width: 900px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
  }
}

.AppointmentHistory {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 100%;
    height: 100% !important;
    padding-bottom: 0px !important;

    .MuiDialogContent-root {
      max-height: 600px;
      padding: 0px;

      .popupWrapper {
        background-color: #eff7fd;
        height: 600px;
      }
    }

    iframe {
      border: none;

      body {
        background-color: #fff;
        margin: 0px !important;
      }
    }
  }
}

.upsell {
  .MuiDialog-paperWidthSm {
    width: 900px;

    .MuiDialogContent-root {
      overflow-y: hidden;
    }
  }
}

.ticketNeedAttentionPopup {
  .MuiDialog-paperWidthSm {
    max-width: 300px;
    width: 300px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
  }
}

.ScheduleCTCpopup {
  .MuiDialog-paperWidthSm {
    max-width: 300px;
    width: 300px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    height: auto;
  }
}

.rdt_TableHead div {
  font-size: 13px;
  color: #2e2e2e;
  font-weight: 600;
  background-color: #e0e0e0;
}

.fosCardBox {
  background: linear-gradient(90deg, #307855 0%, #28CD7C 100%);
  padding: 3px;
  margin-bottom: 15px;
  border-radius: 16px;

  .FosCenter {
    border: 1px solid #56af97;
    border-radius: 16px;
    opacity: 1;
    background: #E5EAFA url("/public/images/rescheduled/Isolation_Mode.png") no-repeat;
    background-position: top right;
    height: 130px;

    .locationTime {
      mix-blend-mode: darken;
      width: 80px;
      margin-top: 16px;
      margin-right: 30px;
    }

    div {
      display: inline-block;
      padding: 8px 1px 0px 10px;
      width: 150px;

      h4 {
        font-family: Roboto;
        font-size: 13px;
        font-weight: 800;
        line-height: 21px;
        text-align: left;
        margin-bottom: 0px;
        color: #2A3D5B;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;


      }

      span {
        font-weight: 400;
        font-family: Roboto;
        font-size: 13px;
        color: #2A3D5B;
      }

      p {
        text-align: left;
        font: normal normal 500 10px/16px Roboto;
        letter-spacing: 0px;
        text-transform: uppercase;
        color: #2A3D5B;
        margin: 0px 0px 4px 0px;
      }

      h3 {
        text-align: left;
        font: normal normal 700 15px/21px Roboto;
        letter-spacing: 0px;
        opacity: 1;
        margin-bottom: 5px;
        background: linear-gradient(90deg, #307855 0%, #28CD7C 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      p {
        a {
          text-align: left;
          font: normal normal 600 12px/16px Roboto;
          letter-spacing: 0px;
          color: #0075ff;
          opacity: 1;
          text-transform: capitalize;
          display: flex;

          svg {
            width: 16px;
            position: relative;
            top: -3px;
          }
        }
      }
    }

    img {
      float: right;
      width: 120px;
    }
  }
}

.upsell {
  .MuiTabs-indicator {
    display: none;
  }

  h6 {
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
  }

  .MuiTab-textColorInherit {
    min-width: auto;
    text-transform: capitalize;
    margin-right: 35px;
    font: normal normal normal 14px/19px Roboto;
    color: #2e2e2e;
    font-weight: 600;
  }

  .MuiTab-textColorInherit.Mui-selected {
    opacity: 1;
    border-bottom: 3px solid #0065ff;
    color: #0065ff;
    border-radius: 2px;
    font-weight: 600;
  }
}

// .upsellModalpopUp{
//   width:900px;
// }

.upsellModalpopUp {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 1100px;
    // height: 600px !important;
    // padding-bottom: 0px !important;
    height: auto;

    .MuiDialogContent-root {
      // max-height: 600px;
      padding: 0px;
      overflow: hidden;

      .popupWrapper {
        background-color: #eff7fd;
        // height: 1400px;
      }
    }

    iframe {
      border: none;
      height: 500px;

      body {
        background-color: #fff;
        margin: 0px !important;
      }
    }
  }
}

.customerImportantInfo {
  margin-bottom: 15px;
  background: #fff9e6;
  border-radius: 16px;
  padding: 15px 20px;
  position: relative;

  // margin-top: 15px;
  .expandmoreIcon {
    svg {
      color: #eb8336;
    }
  }

  h3 {
    text-align: left;
    font: normal normal bold 14px/22px Roboto;
    letter-spacing: 0px;
    color: #eb8336;
    opacity: 0.8;
  }

  ul {
    list-style-type: circle;
    margin-top: 10px;

    li {
      font: normal normal normal 14px/22px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 0.8;
    }
  }
}

.Rmdetails {
  margin-bottom: 15px;
  background: #fff9e6;
  border-radius: 8px;
  border: 1px solid #eddeae;
  padding: 15px 20px;
  position: relative;

  // margin-top: 15px;
  .expandmoreIcon {
    svg {
      color: #eb8336;
    }
  }

  h3 {
    text-align: left;
    font: normal normal bold 14px/22px Roboto;
    letter-spacing: 0px;
    margin-bottom: 5px;
    color: #cf6c23;
    opacity: 0.8;
  }

  p {
    font-family: "calibri";
    font-size: 16px;
    color: #253858;
  }

  .AssignRMBtn {
    color: #fff;
    background-color: #d88849 !important;
    box-shadow: none;
    padding: 2px;
    font: normal normal 600 12px/24px Roboto;
    width: 160px;
    margin-top: 10px;

    &:hover {
      box-shadow: none;
      background-color: #b46426;
    }
  }

  .AssignRMBtnDisble {
    box-shadow: none;
    padding: 2px;
    font: normal normal 600 12px/24px Roboto;
    width: 160px;
    margin-top: 10px;
  }
}

.custPitched {
  margin-bottom: 15px;
  background: #fff2d4;
  border-radius: 8px;
  padding: 15px 20px;
  position: relative;
  border: 1px solid #f1daa4;

  .expandmoreIcon {
    svg {
      color: #eb8336;
    }
  }

  h3 {
    text-align: left;
    font: normal normal bold 14px/22px Roboto;
    letter-spacing: 0px;
    margin-bottom: 5px;
    color: #eb8336;
    opacity: 0.8;
  }

  p {
    font-family: "calibri";
    font-size: 16px;
    color: #253858;
  }

  ul {
    margin-left: 15px;
    font: normal normal normal 14px/22px Roboto;
    color: #253858;
    letter-spacing: 0px;
  }
}

.upsellstatus {
  margin-bottom: 15px;
  background: #fcf0db;
  border-radius: 8px;
  padding: 15px 20px;
  position: relative;
  border: 1px solid #feac1d;

  .expandmoreIcon {
    svg {
      color: #eb8336;
    }
  }

  h3 {
    text-align: left;
    font: normal normal bold 14px/22px Roboto;
    letter-spacing: 0px;
    margin-bottom: 5px;
    color: #feac1d;
    opacity: 0.8;
  }

  p {
    font-family: "calibri";
    font-size: 16px;
    color: #253858;
  }

  ul {
    margin-left: 15px;
    font: normal normal bold 14px/22px Roboto;
    color: #253858;
    letter-spacing: 0px;
  }

  li {
    margin-left: 25px;
    font: normal normal normal 14px/22px Roboto;
  }
}

.pbadvantage {
  margin-bottom: 15px;
  background: #F8EACC;
  border-radius: 8px;
  padding: 15px 20px;
  position: relative;
  border: 1px solid #F8E2B3;

  .expandmoreIcon {
    svg {
      color: #eb8336;
    }
  }

  h3 {
    text-align: left;
    font: normal normal bold 14px/22px Roboto;
    letter-spacing: 0px;
    margin-bottom: 5px;
    color: #D98F0F;
    opacity: 0.8;
  }

  p {
    font-family: "calibri";
    font-size: 16px;
    color: #253858;
  }

  .Additional-details {
    justify-content: space-between;
    padding: 10px 0 0;
    font-size: 12px;
    display: flex;
    list-style-type: none;
    flex-wrap: wrap;
    width: 100%;
    letter-spacing: 0.17px;

    div:nth-child(odd) {
      width: 50%;
      font: normal normal normal 12px/37px Roboto;
      color: #303030;
    }

    div:nth-child(even) {
      color: #253858;
      white-space: nowrap;
      font: normal normal 600 12px/21px Roboto;
      letter-spacing: 0.17px;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 50%;
      text-align: right;
    }

    .MuiSwitch-track {
      background: #eaeaea !important;
    }

    .MuiSwitch-thumb {
      background-color: #808080;
    }

    .Mui-checked {
      .MuiSwitch-thumb {
        background-color: #0065ff;
      }
    }
  }

  .submitBtn {
    border: none;
    background: #0065ff;
    border-radius: 8px;
    padding: 6px 10px;
    text-align: center;
    font: normal normal 12px/21px Roboto;
    letter-spacing: 0.17px;
    color: #fff;
    font-weight: 600;
    width: 100%;
    outline: none;
  }
}

.NoAuthorized {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 50px;
  text-align: center;
  top: 0;
  font: normal normal 600 18px/24px Roboto;
  margin: auto;
  color: #253858;
}

.AuthPage {
  .backToMatrixGoHomme {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 50px;
    margin: auto;
    font: normal normal 500 16px/21px Roboto;
    letter-spacing: 0;
    opacity: 1;
    text-align: center !important;
    text-decoration: underline !important;
    text-transform: capitalize;
    background-color: rgba(0, 0, 0, 0);
    border: none;
    color: #0065ff;
    cursor: pointer;
  }
}

.fileUploadAndViewBtnHide {
  display: none;
}

.fileUploadAndViewBtn {
  border: 1px solid #808080 !important;
  height: 42px;
  font-weight: normal;
  text-transform: capitalize;
}

.LogoutMsgAlert {
  margin-bottom: 80px;
}

.nc-refresh-69::before {
  content: "\ea44";
}

.red {
  color: #e33f3f;
}

.pd-0 {
  padding: 0px !important;
}

.pd-top {
  padding-top: 15px !important;
}

.pdtop5 {
  padding-top: 5px !important;
}

.pdtopBt-0 {
  padding: 0px 12px !important;
}

.pdTop-0 {
  padding-top: 0px !important;
}

.PlayPausePopup {
  p {
    text-align: center;
    width: 100%;
    font: normal normal 600 20px/25px Roboto;
    letter-spacing: 0.17px;
    color: #303030;
    margin-top: 10px;
  }

  .msg {
    text-align: center;
    float: left;
    width: 100%;
    font: normal normal normal 15px/25px Roboto;
    letter-spacing: 0.17px;
    color: #303030;
    margin-top: 10px;
  }

  .btnbox {
    text-align: center;
    float: left;
    width: 100%;

    .PlayPausecontinue {
      background: #0065ff;
      border-radius: 8px;
      margin-top: 20px;
      letter-spacing: 0.17px;
      padding: 9px 35px;
      border: none;
      color: #ffffff;
      font: normal normal 14px/21px Roboto;
      cursor: pointer;
      outline: none;
      margin-right: 20px;
    }

    .PlayPausecancel {
      text-align: center;
      border: 1px solid #0065ff;
      border-radius: 8px;
      color: #0065ff;
      font: normal normal 14px/21px Roboto;
      padding: 8px 40px;
      background-color: #fff;
      outline: none;
    }
  }
}

.AgentAvailabilityWarningPopUp {
  p {
    text-align: center;
    width: 100%;
    font: normal normal 600 20px/25px Roboto;
    letter-spacing: 0.17px;
    color: #303030;
    margin-top: 10px;
  }

  .msg {
    text-align: center;
    float: left;
    width: 100%;
    font: normal normal normal 15px/25px Roboto;
    letter-spacing: 0.17px;
    color: #303030;
    margin-top: 10px;
  }

  .uppername {
    font-size: 16px;
  }

  .btnbox {
    text-align: center;
    float: left;
    width: 100%;

    .AgentAvailabilityWarningPopUpcontinue {
      background: #0065ff;
      border-radius: 8px;
      margin-top: 20px;
      letter-spacing: 0.17px;
      padding: 9px 35px;
      border: none;
      color: #ffffff;
      font: normal normal 14px/21px Roboto;
      cursor: pointer;
      outline: none;
      margin-right: 20px;
    }

    .AgentAvailabilityWarningPopUpcancel {
      text-align: center;
      border: 1px solid #0065ff;
      border-radius: 8px;
      color: #0065ff;
      font: normal normal 14px/21px Roboto;
      padding: 8px 40px;
      background-color: #fff;
      outline: none;
    }
  }
}


.referralLeadPopup {
  h3 {
    font: normal normal 600 16px/21px Roboto;
    margin-bottom: 10px;
  }

  .MuiDialogTitle-root {
    padding: 16px 24px 6px 15px;

    h6 {
      color: #c12525;
    }
  }
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.BookingPopup {
  position: fixed;
  z-index: 1012;
  background: #00000099;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .popUpInner {
    background: white;
    padding: 22px;
    border-radius: 10px;
    position: relative;
    // background-image: url(assets/bgPop.png);
    background-image: url("/public/images/bgPop.png");
    background-size: cover;
    max-width: 1280px;
  }

  button.closeBtn {
    position: absolute;
    right: 12px;
    top: 12px;
    border: none;
    background: transparent;
    width: 30px;
    cursor: pointer;
  }

  .popUpInner {
    display: flex;
    align-items: center;
    gap: 80px;
  }

  .bgMainLogo {
    width: 200px;
    // background: url(assets/bg.gif);
    background: url("/public/images/bg.gif");
    /* padding: 30px; */
    background-size: 300px;
    background-repeat: no-repeat;
    background-position: center;
  }

  .contentSide img {
    width: 300px;
    display: block;
    margin: auto;
    margin-bottom: 20px;
  }

  p.nameMain {
    margin-bottom: 10px;
    font-size: 30px;
    color: #1b2a53;
    line-height: 45px;
  }

  .contentSide h1 {
    text-align: center;
    font-size: 60px;
    color: #1b2a53;
    margin-bottom: 0px;
    line-height: 60px;
  }

  .contentSide {
    text-align: center;
  }

  p.subPara {
    font-size: 32px;
    margin-top: 1px;
    color: #1b2a53;
    line-height: 32px;
    margin-top: 10px;
  }

  .contentSide h2 {
    font-size: 60px;
    color: #1b2a53;
    line-height: 60px;
    margin-top: 14px;
  }

  .yellowBg p {
    font-size: 24px;
    color: #1b2a53;
  }

  .yellowBg span {
    font-weight: 600;
  }

  .popUpInner {
    padding: 22px 80px;
  }

  .yellowBg {
    margin-top: 10px;
    // background: url(assets/yellowBG.svg);
    background: url("/public/images/yellowBG.svg");
    background-size: cover;
    background-repeat: no-repeat;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.SecondaryBookingPopup {
  position: fixed;
  z-index: 1012;
  background: #00000099;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;

  .popUpInner {
    background: white;
    padding: 30px 0px;
    border-radius: 10px;
    position: relative;
    // background-image: url(assets/bgPop.png);
    background-image: url("/public/images/BookingBGimg.png");
    background-size: cover;
    max-width: 1200px;
    display: flex;
    align-items: center;
    gap: 90px;
  }

  button.closeBtn {
    position: absolute;
    right: 12px;
    top: 12px;
    border: none;
    background: transparent;
    width: 30px;
    cursor: pointer;
  }

  .bgMainLogo {
    width: 190px;
    padding-left: 50px;
  }


  p.nameMain {
    text-align: center;
    font: normal normal bold 30px/49px Roboto;
    letter-spacing: 0px;
    color: #1B2A53;
    opacity: 1;
  }

  .contentSide {
    text-align: center;
    padding-right: 40px;

    h2 {
      font-size: 60px;
      color: #1b2a53;
      line-height: 60px;
      margin-top: 14px;
      margin-bottom: 10px;
    }

    img {
      width: 280px;
      display: block;
      margin: auto;
      margin-bottom: 8px;
    }

  }

  p.subPara {
    text-align: center;
    font: normal normal normal 32px/49px Roboto;
    letter-spacing: 0px;
    color: #1B2A53;
    opacity: 1
  }

  .subPara1 {
    text-align: center;
    font: normal normal normal 25px/49px Roboto;
    letter-spacing: 0px;
    color: #1B2A53;
    opacity: 1
  }

  .yellowBg {
    margin-top: 15px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    p {
      text-align: center;
      font: normal normal 600 26px/30px Roboto;
      letter-spacing: 0px;
      color: #1B2A53;
      opacity: 1;
      position: absolute;
    }

    img {
      width: 570px;

    }
  }
}

@media (max-width: 767px) {
  .BookingPopup {
    .popUpInner {
      padding: 20px 30px;
      gap: 30px;
    }

    p.nameMain {
      font-size: 18px;
    }

    .contentSide h1 {
      font-size: 32px;
      margin: 5px 0;
    }

    p.subPara {
      font-size: 18px;
    }

    .contentSide h2 {
      font-size: 32px;
    }

    .yellowBg p {
      font-size: 16px;
    }

    .yellowBg {
      min-height: unset;
      padding: 10px 0;
    }

    .bgMainLogo {
      width: 120px;
    }

    button.closeBtn {
      width: 18px;
    }
  }

  .HDFCPasaPopupDesign {
    h6 {
      font: normal normal 600 16px/24px Roboto !important;
    }

    .CustomerEligibleTable {
      h1 {
        font: normal normal bold 18px/42px Roboto !important;
      }

      h4 {
        font: normal normal 600 20px/25px Roboto !important;
      }
    }

    .MuiDialogContent-root {
      padding: 2px 15px !important;
    }
  }

  .FOSPopup {
    .MuiDialog-paperWidthSm {
      height: 80%;

      .MuiDialogContent-root {
        max-height: 100%;

        .popupWrapper {
          height: 100%;
        }
      }

      iframe {
        height: 100%;
      }
    }
  }
}

.GPAGMCPopup {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 480px;
    height: 600px !important;
    padding-bottom: 0px !important;
    max-height: calc(100% - 25px);

    .MuiDialogContent-root {
      max-height: 600px;
      padding: 0px;

      .popupWrapper {
        height: 500px;
      }
    }

    iframe {
      border: none;

      body {
        background-color: #fff;
        margin: 0px !important;
      }
    }
  }
}


.PaymentAttemptLeads {
  .Heading {
    margin-top: 1em;
    font: normal normal 600 24px/35px Poppins;
    letter-spacing: 0px;
    color: #363636;
    opacity: 1;
    margin-bottom: 0.5em;
  }

  .comment {
    font: normal normal 600 15px/16px Poppins;
    letter-spacing: 0px;
    color: #c10000c2;
    margin-bottom: 1.7em;
    font-style: italic;
  }

  .MuiTableContainer-root {
    box-shadow: none;
  }

  .dataTable {
    margin-top: 1.5em;

    .MuiTableHead-root {
      background: #f9fbfc 0% 0% no-repeat padding-box;
      opacity: 1;

      .MuiTableCell-head {
        text-align: center;
        font: normal normal 600 12px/16px Roboto;
        letter-spacing: 0px;
        color: #000000;
        opacity: 0.89;
        border-bottom: none;

        &:first-child {
          text-align: center;
          padding: 0px 5px;
          width: 50px;
        }
      }
    }

    .MuiTableCell-body {
      font: normal normal 600 12px/16px Roboto;
      letter-spacing: 0px;
      color: #00000099;
      padding: 7px 5px;
      text-align: center;
      border-bottom: none;
    }

    .dualTable {
      border-left: 2px solid #817f7f31;
      margin-left: 10px;

      .MuiTableHead-root {
        background-color: #fff;

        .MuiTableRow-head {
          th {
            border-bottom: none;
            padding-left: 15px;
          }
        }
      }

      .MuiTableBody-root {
        th {
          padding-left: 15px;
        }
      }

      .MuiTableRow-root {
        td {
          border-bottom: 1px solid #7070701f;

          &:first-child {
            border-bottom: none;
          }
        }
      }

      .RaiseTicket {
        display: flex;
        align-items: center;
        color: #e6af2e;
        cursor: pointer;

        svg {
          width: 14px;
          margin-right: 8px;
        }
      }

      .TicketSuccess {
        color: #2684ff;
        cursor: pointer;

        svg {
          width: 14px;
          margin-right: 4px;
        }
      }
    }

    .MuiIconButton-colorPrimary {
      color: #0065ff;
    }

    .ParentLead {
      color: #0065ff;
      text-decoration: underline;
      cursor: pointer;
    }

    .payment_Failed {
      color: #c10000;
      opacity: 0.6;
    }

    .payment_Pending {
      color: #e6af2e;
      opacity: 0.6;
    }

    .payment_Success {
      color: #206f13;
      opacity: 0.6;
    }
  }
}

.LeadMismatchTooltip {
  background-color: #eb8336 !important;
  color: white;
  font-weight: 600 !important;
  padding: 5px !important;
  width: 230px;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .MuiTooltip-arrow {
    color: #eb8336;
  }

  span {
    cursor: pointer;
  }

  svg {
    height: 19px;
    width: 19px;
    position: relative;
    right: -3px;
    background-color: #c5641c;
    cursor: pointer;
    border-radius: 10px;
    padding: 2px;
  }
}

.discountPopup {
  .MuiDialog-paperWidthSm {
    width: 475px;
    padding-bottom: 0px;
    max-height: calc(100% - 27px);

    .MuiDialogContent-root {
      padding-bottom: 0px !important;

      h6 {
        display: inline-block;
      }


      .leadId {
        font: normal normal 600 16px/19px Roboto;
        letter-spacing: 0px;
        color: #253858;
        opacity: 1;
        display: inline-block;
        background-color: rgba(0, 101, 255, 0.1);
        padding: 8px;
        border-radius: 8px;
        margin-left: 20px;
        margin-bottom: 10px;
      }

      .MuiAccordion-rounded {
        border-radius: 8px;
        margin: 11px 0;
        border-top: none;
        background: rgba(0, 101, 255, 0.05);
        box-shadow: none;
        padding: 3px 15px 3px 0px;

        &::before {
          display: none;
        }

        .MuiTypography-root {
          font-family: Roboto;
          font-size: 16px;
          font-weight: 600;
          line-height: 19px;
          letter-spacing: 0em;
          text-align: left;
          color: #253858;
          position: relative;

          .bulletPoint {
            top: 2px;
            background-color: red;
            position: absolute;
            padding: 3px;
            width: 3px;
            margin: 0px;
            border-radius: 10px;
            height: 3px;
            right: -7px;

          }
        }

        .mt15 {
          margin-top: 15px;
        }

        .MuiButtonBase-root {
          background-color: transparent;
          color: #253858;
        }

      }

      .MuiAccordionSummary-root.Mui-expanded {
        min-height: 40px;
      }

      .MuiAccordionSummary-content.Mui-expanded {
        margin: 12px 0 5px;
      }

      .planGrid {
        background-color: #fff;
        border-radius: 16px;
        padding: 16px;
        width: 100%;
        margin-bottom: 15px;
      }

      .GridRow {
        padding: 12px 5px 12px 16px;
        display: inline-flex;
        flex-wrap: wrap;
        background: #0065ff0d;
        border-radius: 8px;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        margin-top: 14px;

        h4 {
          text-align: left;
          font: normal normal 500 16px/19px Roboto;
          letter-spacing: 0px;
          color: #000000;
          width: 77%;
        }

        .switchtogle {
          float: right;

          .MuiSwitch-root {
            width: auto;
            height: auto;
            padding: 3px;
          }

          .MuiButtonBase-root {
            background-color: transparent;
            color: #253858;
            padding: 3px;
          }

          .MuiIconButton-root {
            background-color: transparent;
            position: absolute;
            top: 3px;
            padding: 0px;
            left: -10px;
          }

          .Mui-checked+.MuiSwitch-track {
            background-color: #0065FF !important;
          }

          .MuiSwitch-thumb {
            background-color: #fff;
            width: 26px;
            height: 26px;
            box-shadow: none;
            border: 1px solid #C1C1C1;
          }

          .MuiSwitch-track {
            border-radius: 40px;
            height: 26px;
            width: 48px;
            background-color: #e0dede;
            opacity: 1;
          }

        }

        .MultiplePlan {
          display: inline-flex;
          align-items: center;
          width: 100%;
          margin: 5px 0px;
        }

        .mt-1 {
          margin-top: 0.8rem;
        }
      }

      .infoMsg {
        color: #cd9e45;
        background-color: #fff9e6;
        padding: 0px 10px;
        border-radius: 3px;
        font-weight: 500;
        font-size: 11px;
        margin-top: 1px;
        letter-spacing: 0.3px;
        word-wrap: break-word;
      }

      .ErrorMsg {
        color: #f12424;
        word-wrap: break-word;
        padding: 0px 10px;
        border-radius: 3px;
        font-weight: 500;
        font-size: 12px;
        margin-top: 3px;
        font-family: 'Roboto';
        letter-spacing: 0.3px;
      }

      .disableAccrodian {
        background-color: #e0e3e6 !important;
        cursor: default;

        .MuiButtonBase-root {
          cursor: default;
        }
      }
    }

    h3 {
      font: normal normal 600 16px/18px Roboto;
      letter-spacing: 0px;
      color: #0065FF;
      opacity: 1;
      display: flex;
      align-items: center;

      img {
        margin-right: 10px;

      }
    }

    footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: sticky;
      width: 100%;
      bottom: 0px;
      background-color: #fff;
      left: 0px;
      padding: 8px 0px;
      z-index: 999;


      svg {
        color: #0065FF;
        cursor: pointer;
      }

      .bottomIcon {
        display: flex;
        width: 112px;
        justify-content: space-between;
      }

      .ApplyBtn {
        background-color: #0065FF;
        border-radius: 8px;
        opacity: 1;
        outline: none;
        border: none;
        font: normal normal 500 16px/21px Roboto;
        letter-spacing: 0px;
        color: #fff;
        padding: 10px 40px;
        cursor: pointer;
      }
    }
  }

  .MuiAccordionDetails-root {
    flex-wrap: wrap;
    padding-bottom: 5px;
  }

  .disablebtn {
    background: #e0e3e6;
    border-radius: 8px;
    color: #fff;
    padding: 14px 65px;
    font: normal normal 12px Roboto;
    letter-spacing: 0.17px;
    border: none;
    outline: none;
    cursor: default;
  }
}

.discountAppliedPopup {
  .MuiDialog-paperWidthSm {
    width: 328px;
    text-align: center;
    height: auto;

    h3 {
      font: normal normal 600 20px/26px Roboto;
      letter-spacing: 0px;
      color: #0065FF;
      opacity: 1;
      margin-top: 10px;

      p {
        margin-top: 8px;
        font: normal normal 600 16px/21px Roboto;
        letter-spacing: 0px;
        color: #9B9B9B;
        opacity: 1;
      }

    }

    .notApplied {
      color: #d90e0e;
    }

    img {
      margin-top: 15px;
      width: 60px;
    }

    button {
      background: #0065FF 0% 0% no-repeat padding-box;
      border-radius: 4px;
      opacity: 1;
      font: normal normal 600 16px/21px Roboto;
      letter-spacing: 0px;
      color: #FFFFFF;
      padding: 7px 40px;
      margin-top: 30px;
      border: none;
      outline: none;
      cursor: pointer;
    }

    .TryAgainBtn {
      border: 1px solid #0165FF;
      border-radius: 4px;
      opacity: 1;
      outline: none;
      background-color: #fff;
      font: normal normal 600 16px/21px Roboto;
      letter-spacing: 0px;
      color: #0065FF;
      padding: 5px 25px;
      margin-top: 15px;
      cursor: pointer;
    }
  }
}

.AppointmentSummaryPopup {
  .MuiDialog-paperWidthSm {
    width: 1200px;
    text-align: center;
    height: auto;

    h3 {
      font: normal normal 600 20px/26px Roboto;
      letter-spacing: 0px;
      color: #0065FF;
      opacity: 1;
      margin-top: 10px;

      p {
        margin-top: 8px;
        font: normal normal 600 16px/21px Roboto;
        letter-spacing: 0px;
        color: #9B9B9B;
        opacity: 1;
      }

    }
  }
}

.crossIcon {
  top: 0 !important;
  position: absolute !important;
  border: none !important;
  color: #7c7878 !important;
  right: 20px !important;
  padding: 0px !important;
  background-color: transparent !important;
  margin-top: 15px !important;
}


.AppointMentView {
  background-color: #ffff;
  width: 100%;
  padding: 15px 8px 8px 8px;
  border-radius: 7px;
  margin: 5px 5px 0px 5px;
  box-shadow: 0px 3px 8px #0065ff29;
  display: flex;

  .ApptSummary {
    border: none;
    border-radius: 8px;
    height: 40px;
    width: 100%;
    color: #0065ff;
    box-shadow: 0px 6px 16px #3469cb29;
    background-color: #fff;
    margin-top: 1px;
    cursor: pointer;
    border: 1px solid;
    outline: none;
    font-weight: 600;
    letter-spacing: 0.17px;
  }

  .callBtn {
    background: #0065ff 0% 0% no-repeat padding-box;
    box-shadow: 0px 6px 16px #3469cb29;
    border-radius: 8px;
    text-align: center;
    font: normal normal 600 12px/19px Roboto;
    letter-spacing: 0.17px;
    color: #fff;
    padding: 10px 20px;
    margin: 2px 7px 5px 2px;
    height: 39px !important;
    width: 90px;
  }
}

.FirstReportingName {
  border: none;
  background-color: transparent;
  display: flex;
  align-items: center;
  cursor: pointer;

  .calltransferBtn {
    background-color: #fff;
    padding: 0px 5px;
    text-align: center;
    margin-left: 2px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    box-shadow: 0px 6px 16px #3469cb29;
    opacity: 1;
    color: #0165FF;

    svg {
      width: 20px;
    }
  }

}

/* Health Renewal POpup Css */
.HealthRenewalDetailsPopup {
  .MuiDialog-paperWidthSm {
    padding-bottom: 0px;
    height: auto;
  }

  .MuiDialogContent-root {
    background-color: #eff3f6;
    width: 1200px;
  }

  .HealthRenewalDetails {
    margin-bottom: 10px;
    box-shadow: none;
    background-color: #fff;
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;

    .MuiAccordionSummary-root.Mui-expanded {
      min-height: 40px;
      height: 48px;
      box-shadow: 0px 0px 16px #00000014;

      p {
        color: #0065ff;
      }
    }

    .MuiAccordionSummary-expandIcon {
      color: #0065ff;
      right: 20px;
      background-color: transparent;
      top: 7px;

      svg {
        font-size: 25px;
      }
    }

    .MuiTypography-root {
      color: #253858;
      font: normal normal 600 14px/24px Roboto;
      margin-bottom: 0px;
      letter-spacing: 0.26px;
      width: 100%;

      span {
        float: right;
        font: normal normal 500 12px/24px Roboto;
        margin-right: 35px;
      }
    }

    .MuiAccordionDetails-root {
      display: block;

      .heading {
        border-bottom: 1px solid #e0e0e0;
        color: #8d9397;
        display: block;
        font-size: 17px;
        margin: 0 0 20px;
        padding: 10px 0 5px;
      }

      .container {
        display: flex;
        flex-wrap: wrap;
        list-style-type: none;
        padding: 0px 20px;
      }

      .item {
        label {
          color: #444444;
          // display: inline-block;
          font-size: 12px;
          text-align: left;
          vertical-align: middle;
          width: 160px;
          box-sizing: border-box;
          padding: 0 0 0 10px;
          font-weight: bold;
        }

        span {
          letter-spacing: 0.17px;
          color: #444444;
          font-size: 12px;
          padding: 0 5px;
          vertical-align: middle;
          float: right;
          width: 50%;
          text-align: right;
        }
      }

      .viewBtn {
        background: #0065ff;
        border-radius: 8px;
        margin: 0px 0px 15px;
        letter-spacing: 0.17px;
        padding: 8px 21px;
        width: auto;
        border: none;
        margin-left: 10px;
        color: #ffffff;
        font: normal normal normal 12px/21px Roboto;
        cursor: pointer;
        outline: none;
      }

      .save_button_expected {
        margin: 0px 0px 0px 10px;
      }

      .button_fixed {
        width: 125px;
      }

      .save {
        background: rgb(0, 161, 204);
        border-radius: 4px;
        box-shadow: rgb(160, 160, 160) 1px 2px 5px -1px;
        color: rgb(255, 255, 255);
        font-size: 13px;
        padding: 8px 10px;
        position: relative;
        text-align: center;
        border: 0px;
        min-width: 73px;
      }

      .cancel {
        background: #bdc3c7;
        border-radius: 4px;
        color: #ffffff;
        font-size: 13px;
        margin: 0 7px 0 15px;
        min-width: 70px;
        padding: 8px 10px;
        position: relative;
        text-align: center;
        box-shadow: 1px 2px 5px -1px #a0a0a0;
        border: 0;
      }
    }
  }

  .MemberDetails {
    border-collapse: collapse;
    width: 97%;
    margin: 0px 17px 15px;

    th {
      font: normal normal 600 12px/24px Roboto;
      text-align: left;
      padding: 5px 15px;
      color: #253858;
    }

    td {
      font: normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      border: none;
      padding: 5px 15px;
    }

    tbody {
      tr:nth-child(odd) {
        background-color: #f8f8f8;
      }
    }
  }
}

.SmartInvestmentPopup {
  .MuiDialog-paperWidthSm {
    padding-bottom: 0px;
    height: auto;
  }

  .MuiDialogContent-root {
    background-color: #eff3f6;
    width: 1200px;
  }

  .headerclass {
    border-bottom: 1px solid #e0e0e0;
    color: #8d9397;
    display: block;
    font-size: 17px;
    margin: 0 0 20px;
    padding: 10px 0 5px;
  }

  .container {
    display: flex;
    flex-wrap: wrap;
    list-style-type: none;
    padding: 0px 20px;
  }

  .item {
    label {
      color: #444444;
      display: inline-block;
      font-size: 12px;
      text-align: left;
      vertical-align: middle;
      width: 160px;
      box-sizing: border-box;
      padding: 0 0 0 10px;
      font-weight: bold;
    }

    span {
      letter-spacing: 0.17px;
      color: #444444;
      font-size: 12px;
      padding: 0 5px;
      vertical-align: middle;
    }
  }

  .InvestmentDetails {
    border-collapse: collapse;
    width: 97%;
    margin: 0px 17px 15px;

    th {
      font: normal normal 600 12px/24px Roboto;
      text-align: left;
      padding: 5px 15px;
      color: #253858;
    }

    td {
      font: normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      border: none;
      padding: 5px 15px;
    }

    tbody {
      tr:nth-child(odd) {
        background-color: #f8f8f8;
      }
    }
  }

  .Warning {
    color: red;
    font-weight: bold;
    font-size: 14px;
    padding-top: 6px;
    text-align: center;
  }
}

.MarginTopLeft {
  margin: 32px 8px 0px 8px !important;
}

.CanterText {
  margin: auto;
}

.CallTransferCustomerAccess {
  border: none;
  background-color: transparent;
  display: flex;
  align-items: center;
  cursor: pointer;
  justify-content: right;

  .calltransferBtn {
    background-color: #fff;
    padding: 0px 5px;
    text-align: center;
    margin-left: 2px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    box-shadow: 0px 6px 16px #3469cb29;
    opacity: 1;
    color: #0165FF;

    svg {
      width: 20px;
    }
  }

}

.BookingCreditLog {
  .MuiDialog-paperWidthSm {
    max-width: 950px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 950px;
  }

  .MuiTableCell-root {
    font: normal normal normal 12px/16px Roboto;
    padding: 7px;
    color: #808080;
    border-bottom: none;

  }

  .MuiPaper-elevation1 {
    box-shadow: none;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  h6 {
    text-align: left;
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
    padding-left: 10px;
  }

  .MuiTableRow-root {
    td {
      font: normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      border: none;

      .ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 180px;
      }
    }
  }

  .moreDeatilsData:nth-child(odd) {
    background-color: #f8f8f8;
    border-radius: 4px;
  }
}

.RejectBookedLeadPopup {
  .MuiDialog-paperWidthSm {
    max-width: 868px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 868px;

    .MuiDialogContent-root {
      padding: 0px 24px !important;
    }

  }


  .MuiTableCell-root {
    font: normal normal normal 12px/16px Roboto;
    padding: 7px;
    color: #808080;
    border-bottom: none;
  }

  .MuiPaper-elevation1 {
    box-shadow: none;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  h6 {
    text-align: left;
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
    padding-left: 10px;
  }

  .addnew {
    margin: auto;
    width: 150px;
    margin-bottom: 0px;
    cursor: pointer;
    text-align: center;
    border-radius: 8px;
    border: 1px solid #0065FF;
    padding: 8px 16px;

    span {
      font: normal normal normal 14px/normal Roboto;
      letter-spacing: 0.2px;
      color: #0065ff;
    }
  }

  .MuiTableRow-root {
    td {
      font: normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      border: none;
    }
  }

  .moreDeatilsData:nth-child(odd) {
    background-color: #f8f8f8;
    border-radius: 4px;
  }

  .MuiIconButton-root {
    background-color: #eaeaea;
    position: absolute;
    padding: 5px;

    svg {
      font-size: 1.2rem;
    }
  }

}

.SuggestedPlanPopup {
  .Title {
    text-align: left;
    font: normal normal 400 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    padding-left: 10px;
    margin-top: 15px;
    position: absolute;
    margin-bottom: 10px;
    display: flex;
    top: 0px;
    left: 14px;
  }

  .MuiDialog-paperWidthSm {
    max-width: 1000px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 1000px;
    height: auto;

    .MuiDialogContent-root {
      padding: 0px 24px !important;
    }

  }

  table {
    width: 100%;
    display: table;
    border-collapse: separate;
    border-spacing: 0 1em;
  }

  .MuiTableCell-root {
    word-break: break-word;
    height: 60px;
    font: normal normal 400 15px/23px Roboto;
    letter-spacing: 0.224px;
    color: #505F79;
    border: none;
    border-top: 1px solid rgba(37, 56, 88, 0.1607843137);
    border-bottom: 1px solid rgba(37, 56, 88, 0.1607843137);
    padding: 2% 1.5%;
    width: 18%;
    vertical-align: baseline;

    &:first-child {
      border-left: 1px solid #25385829;
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
      width: 23%;
    }

    &:last-child {
      border-right: 1px solid #25385829;
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
    }

    &:nth-child(3) {
      width: 34%;
    }

    p {
      font: normal normal 12px/23px Roboto;
    }
  }

  .MuiPaper-elevation1 {
    box-shadow: none;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  h6 {
    text-align: left;
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
    padding-left: 10px;
  }

  .MuiTableRow-root {
    background: #FFF;
    box-shadow: 0px 3px 12px 0px rgba(0, 101, 255, 0.16);

  }

  .MuiIconButton-root {
    background-color: #eaeaea;
    position: absolute;
    padding: 5px;

    svg {
      font-size: 1.2rem;
    }
  }
}

/* Port POpup Css */
.PortPopup {
  .MuiDialog-paperWidthSm {
    padding-bottom: 0px;
    height: auto;
  }

  .MuiDialogContent-root {
    background-color: #fff;
    width: 900px;
  }

  .heading {
    border-bottom: 1px solid #e0e0e0;
    color: #8d9397;
    display: block;
    font-size: 17px;
    margin: 0 0 20px;
    padding: 10px 0;
  }

  .container {
    display: flex;
    flex-wrap: wrap;
    list-style-type: none;
    padding: 0px;
  }

  .item {
    label {
      color: #444444;
      display: inline-block;
      font-size: 12px;
      text-align: left;
      vertical-align: middle;
      width: 50%;
      box-sizing: border-box;
      padding: 0 0 0 10px;
      font-weight: bold;
    }

    span {
      letter-spacing: 0.17px;
      color: #444444;
      font-size: 12px;
      padding: 0 5px;
      vertical-align: middle;
    }
  }

}

.CreateTermReferralLeadPopop {
  .MuiCollapse-entered {
    min-height: 0px;
    position: relative;
    top: 118px;
  }

  .MuiDialog-paperWidthSm {
    padding-bottom: 0px;
    max-height: 100%;
    height: auto;

    .MuiDialogTitle-root {
      padding-bottom: 7px;

      h6 {
        font: normal normal 600 24px/24px Roboto;
        letter-spacing: 0px;
        color: #2e2e2e;
        padding-left: 10px;
        text-align: center;
      }
    }
  }

  .MuiDialogContent-root {
    background-color: #fff;
    width: 500px;
    padding: 5px 10px 10px !important;

    .MuiIconButton-root {
      position: sticky;
      padding: 8px;
      background-color: transparent;
    }
  }
}

.SavingReferralLeadPopop {
  .MuiDialog-paperWidthSm {
    padding-bottom: 0px;
    max-height: 100%;
    height: auto;
    overflow-x: hidden;

    .MuiDialogTitle-root {
      h6 {
        font: normal normal 600 24px/24px Roboto;
        letter-spacing: 0px;
        color: #2e2e2e;
        padding-left: 10px;
        text-align: center;
        margin-top: 5px;
      }
    }
  }

  .MuiDialogContent-root {
    background-color: #fff;
    width: 782px;
    padding: 5px 10px 10px !important;
    overflow: hidden;

    .MuiIconButton-root {
      position: sticky;
      padding: 6px;
      background-color: transparent;
    }
  }

  .referelIframe {
    height: 410px;
    width: 100%;
  }
}

@media only screen and (max-width: 800px) {
  .CreateTermReferralLeadPopop {
    .MuiDialogContent-root {
      width: 100%;
      padding: 5px 18px 10px !important;
    }

    .MuiDialog-paperWidthSm {
      height: auto;
      border-bottom-right-radius: 0px;
      border-bottom-left-radius: 0;
    }

    .MuiDialog-scrollPaper {
      align-items: flex-end;
    }
  }

  .SavingReferralLeadPopop {
    .MuiDialogContent-root {
      width: 100%;
      padding: 20px 18px 20px !important;

      .btn {
        position: static;
      }
    }

    .MuiDialog-paperWidthSm {
      height: auto;
      border-bottom-right-radius: 0px;
      border-bottom-left-radius: 0;
    }

    .MuiDialog-scrollPaper {
      align-items: flex-end;
    }
  }
}

.LastYearPolicyDetails {
  .MuiDialog-paperWidthSm {
    width: 350px;
    text-align: center;
    height: auto;

    .MuiDialogContent-root {
      padding-top: 0px !important;
    }

    h6 {
      padding-left: 0px;
    }

    ul {
      text-align: left;
      font: normal normal normal 13px/24px Roboto;
      color: #808080;

      li {
        display: flex;
        justify-content: space-between;

        label {
          font-weight: 600;
        }
      }
    }


  }
}

.ManualLinkPopup {
  .MuiDialog-paperWidthSm {
    padding-bottom: 0px;
    height: auto;
  }

  .mt-3 {
    margin-top: 8px;
  }

  .MuiDialogContent-root {
    width: 730px;

    .GenerateBtn {
      background: #0065ff;
      border-radius: 8px;
      letter-spacing: 0.17px;
      padding: 8px;
      width: 150px;
      border: none;
      color: #ffffff;
      font: normal normal 600 12px/21px Roboto;
      cursor: pointer;
      outline: none;
      margin-top: 10px;
    }

    .Note {
      color: #df1f1f;
      font: normal normal 600 12px/15px Roboto;
      margin: 15px 0px;

    }

    .copyLink {
      border: 1px solid #808080;
      border-radius: 12px;
      position: relative;
      display: flex;
      align-items: center;
      margin-top: 20px;
      width: 680px;
      padding: 0px 9px;
      justify-content: space-between;

      svg {
        color: #808080;

      }

      .copyBtn {
        color: #0065ff;
        cursor: pointer;
      }

      p {
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 12px 15px;
        overflow: hidden;
        font: normal normal 500 12px/15px Roboto;
        color: #808080;
      }
    }
  }
}

.callsummaryPopup {
  h6 {
    text-align: left;
    font: normal normal 22px/24px Roboto;
    letter-spacing: 0px;
    color: white;
    font-weight: 600;
    padding-left: 0px;
    background: linear-gradient(to right, #007bff, #6a0dad);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .MuiDialog-paperWidthSm {
    color: white;
    line-height: 1.4rem;
    height: 80vh;
    //background: transparent linear-gradient(128deg, #4E54C8 0%, #8F94FB 100%) 0% 0% no-repeat padding-box;
    background: linear-gradient(180deg, #BCD7FF 0%, #FBFCFF 100%);
    border-radius: 20px;
    box-shadow: none;
    width: 704px;
  }

  .MuiDialogContent-root {
    padding-top: 0px !important;
  }

  .MuiAccordion-rounded {
    color: white;
    box-shadow: none;
    background: transparent;

    &::before {
      background-color: transparent;
    }

    .MuiIconButton-root {
      background-color: transparent;

      svg {
        font-size: 1.4rem;
      }
    }

    .MuiAccordionSummary-content {
      text-align: left;
      font: normal normal bold 12px/28px Roboto;
      letter-spacing: 0px;
      color: #FFFFFF;
      opacity: 1;
      margin: 0px;
    }

    .MuiAccordionSummary-root {
      padding: 0px;
    }

    .MuiAccordionSummary-root.Mui-expanded {
      padding: 0px;
      min-height: 45px;
    }

  }

  .expendIcon {
    position: absolute;
    right: 20px;
    top: 18px;
    cursor: pointer;
  }

  .Heading {
    font-family: Roboto;
    font-weight: 700;
    font-size: 17px;
    line-height: 100%;
    letter-spacing: 0px;
    color: #000;


  }

  .MuiAccordionDetails-root {
    display: block;
    padding: 0px;

    p {
      text-align: left;
      font: italic normal 300 13px/28px Roboto;
      letter-spacing: 0px;
      color: #000000CC;
      opacity: 1;
    }

    li {
      text-align: left;
      font: normal normal 500 13px/17px Roboto;
      letter-spacing: 0px;
      color: #000;
      opacity: 1;
      margin-bottom: 10px;
      display: flex;

      svg {
        margin-right: 6px;
        font-size: 10px;
        margin-top: 3px;
      }
    }
  }
}

.leadCreatedSuesses {
  .MuiDialogContent-root {
    width: 450px;

  }

  h4 {
    color: #253858;
    font: normal normal 600 18px/24px Roboto;
    letter-spacing: .14px;
    margin-top: 20px;
    margin-bottom: 10px;
    opacity: 1;
    text-align: center;
    text-transform: capitalize;
  }

  p {
    color: #253858;
    font: normal normal 600 16px/24px Roboto;
    letter-spacing: .12px;
    opacity: 1;
    text-align: center;
  }

  .ReferalID {
    color: #757575;
    font: normal normal 600 15px/19px Roboto;
    letter-spacing: 0;
    opacity: 1;
    text-align: center;
  }


  .AppointmentBTN {
    border-radius: 4px !important;
    color: #fff !important;
    font: normal normal 500 16px/21px Roboto;
    height: 50px;
    letter-spacing: 0;
    margin: 15px auto 5px;
    opacity: 1;
    outline: none;
    text-align: center !important;
    text-transform: capitalize;
    width: 208px;

    background-color: #0065ff !important;
    border: none !important;

  }

  .ReferalBTN {
    background-color: #fff !important;
    border: 1px solid #0065ff !important;
    border-radius: 4px !important;
    color: #0065ff !important;
    display: block !important;
    font: normal normal 500 16px/21px Roboto;
    height: 50px;
    letter-spacing: 0;
    margin: 15px auto 15px;
    opacity: 1;
    text-align: center !important;
    text-transform: capitalize;
    width: 208px;
  }

  .GoHomeBtn {
    font: normal normal 500 16px/21px Roboto;
    letter-spacing: 0;
    opacity: 1;
    text-align: center !important;
    text-decoration: underline !important;
    text-transform: capitalize;
    background-color: transparent;
    border: none;
    color: #0065ff;
    cursor: pointer;
  }
}

.DocUploadPopup {
  .MuiDialog-paperWidthSm {
    padding-bottom: 0px;
    max-height: 100%;
    height: auto;
    overflow-x: hidden;
    width: 600px;
    padding-bottom: 25px;

    .MuiDialogTitle-root {
      h6 {
        font: normal normal 600 24px/24px Roboto;
        letter-spacing: 0px;
        color: #2e2e2e;
        padding-left: 10px;
        text-align: center;
        margin-top: 5px;
      }
    }

    .saveButton {
      background: #0065ff;
      border-radius: 8px;
      color: #fff;
      padding: 14px;
      font: normal normal 12px Roboto;
      letter-spacing: 0.17px;
      border: none;
      outline: none;
      width: 130px;
      margin-top: 20px;
    }

    .UploadBtn {
      border: 1px solid #0065ff;
      border-radius: 4px;
      background-color: transparent;
      color: #0065ff;
      letter-spacing: 0.17px;
      width: 70%;
      text-transform: capitalize;
      padding: 8px;
      height: 42px;
      margin-bottom: 12px;
      font: normal normal 600 12px/21px Roboto;
      cursor: pointer;
      outline: none;
    }
  }

  .fileUploadBtnText {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    border: 1px solid #808080;
    height: 42px;
    font-weight: normal;
    white-space: nowrap;
    text-transform: capitalize;
    color: #808080;
  }

  .fileUploadBtnFlex {
    display: flex !important;
  }

  .ClearIcon {
    justify-content: left;
    background-color: transparent !important;
  }

  .ClearIconDisabled {
    justify-content: left;
    filter: grayscale(1);
  }
}

.VerifPopupRadio {

  .MuiIconButton-root {
    background-color: #eaeaea;
    padding: 5px;
    right: 10px;
    z-index: 999;
    position: unset;
  }

  .MuiIconButton-root svg {
    font-size: 0.8rem;
  }
}

.appVerfiPopup {
  #responsive-dialog-title {
    background: transparent linear-gradient(109deg, #c8dbf9 0%, #ffffff 100%) 0% 0% no-repeat padding-box;
    margin-bottom: 1rem;
  }

  .MuiFormLabel-root {
    margin-bottom: 9px;
  }

  .MuiFormControlLabel-root {
    margin-right: 4.2rem;
    margin-left: 1px;
  }

  .MuiFormGroup-root {
    margin-bottom: 1rem;
  }

  button {
    margin-top: 1rem;
  }
}

.feedbackbtns {
  position: absolute;
  right: 5%;
}

.feedback-done-btn {
  background-color: #0065ff !important;
  color: #fff !important;
}

.feedback-done-btn:disabled {
  background-color: #e1dede !important;
  color: white !important;
  pointer-events: auto !important
}

.feedbackdropdown {

  .MuiGrid-grid-md-6,
  .MuiGrid-grid-sm-6 {
    max-width: none;
  }
}

.apptSearchbox {
  border: 1px solid grey;
  width: 35%;
  border-radius: 4px;
}

.dflex {
  display: flex !important;
}

.Pre-ExistingDiseasesPopup {
  h6 {
    color: rgba(37, 56, 88, 0.89);
    font-family: Roboto;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    text-transform: capitalize;
  }

  .MuiDialog-paperScrollPaper {
    max-height: calc(100% - 35px);
  }

  .MuiDialogContent-root {
    width: 527px;
    padding-top: 0px !important;

    div {
      border-radius: 8px;
      border: 1px dashed #0065FF;
      background: rgba(0, 101, 255, 0.05);
      padding: 20px;

      .caption {
        color: rgba(37, 56, 88, 0.60);
        font-family: Roboto;
        font-size: 10px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        text-align: center;
        text-transform: uppercase
      }

      h2 {
        color: #0065FF;
        font-family: Roboto;
        font-size: 24px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        text-transform: capitalize;
        margin: 7px 0px 12px 0px;
        text-align: center;
      }

      h3 {
        border-radius: 24px;
        background: #FFF;
        box-shadow: 0px 3px 12px 0px rgba(0, 101, 255, 0.08);
        color: rgba(37, 56, 88, 0.89);
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        width: 100%;
        line-height: normal;
        margin: auto auto 15px;
        text-align: center;
        padding: 9px;
      }

      ul {
        list-style-type: none;
        display: flex;
        padding-left: 0px;
        flex-wrap: wrap;
        justify-content: space-between;

        li {
          color: rgba(37, 56, 88, 0.89);
          font-family: Roboto;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
          width: 60%;
          margin-bottom: 23px;
          text-align: left;

          &:nth-child(2n+2) {
            color: #0065FF;
            width: 40%;
            text-align: right;
          }
        }
      }
    }
  }
}

.callbackDetailsPopup {

  .MuiDialog-paperWidthSm {
    max-width: 631px;
    width: 631px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
  }

  .MuiDialogTitle-root {
    animation: blinking 1s infinite;

    h6 {
      color: #fff;
    }
  }

  @keyframes blinking {
    0% {
      background-color: #77aeff;
    }

    100% {
      background-color: #0065ff;
    }
  }

}

.dialpad {
  background-color: whitesmoke !important;
  max-width: 150px;

  .dialpadContainer {

    .dialpadKey {
      min-width: 100%;
      outline: none;
      font-weight: bold !important;
      box-shadow: 4px 4px 8px rgba(13, 39, 80, 0.25), -4px -4px 8px white;

    }
  }
}

.DialpadTooltip {
  background-color: whitesmoke !important;
  border-radius: 5px;
}

.DialpadTooltipArrow {
  color: whitesmoke !important;
}

// Below classes for Inbound VC Incoming Call POPUP


.IBVCcallAlertSection {
  background: #232323;
  box-shadow: 0px 3px 6px #00000029;
  opacity: 1;
  position: fixed;
  margin: auto;
  z-index: 99;
  height: 383px;
  width: 666px;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;

  .deatils {
    font-family: Roboto;
    font-size: 20px;
    font-weight: 400;
    line-height: 28px;
    color: #fff;
    text-align: left;
    margin-right: 2px;

    h3 {
      font-family: Roboto;
      font-size: 26px;
      font-weight: 700;
      line-height: 28px;
      text-align: left;
      display: inline-block;
    }
  }

  .auto-redirect-msg {
    font-family: Roboto;
    font-size: 20px;
    font-weight: 400;
    line-height: 28px;
    color: #fff;
    text-align: left;
    margin-top: 24px;
    font-style: italic;
  }

  .IBVCcallBgImage {
    justify-content: center;
    text-align: center;
    vertical-align: middle;
    display: flex;
    align-items: center;
    margin: auto;
    height: 383px;
    background: #235F81;
  }


  p {
    align-items: center;
    font-family: Roboto;
    font-size: 16px;
    font-weight: 700;
    line-height: 18.75px;
    letter-spacing: 0.19px;
    margin-top: 20px;
    color: #8AB8FF;
    justify-content: space-evenly;
    display: flex;
    border-radius: 28px;
    margin-bottom: 18px;
    width: 246px;
    padding: 0px 15px;
    height: 40px;
    background: #b7d3ff0d;

    img {
      width: 20px;
    }
  }

  button {
    background: #18C200;
    color: #fff;
    padding: 10px;
    height: 45px;
    width: 200px;
    font-family: Roboto;
    font-size: 14px;
    border-radius: 4px;
    font-weight: 700;
    line-height: 16.41px;
    border: none;
    text-align: center;
    margin-top: 30px;
    text-transform: uppercase;
    cursor: pointer;
  }

  .minimizeBtn {
    font-family: Roboto;
    font-size: 14px;
    font-weight: 700;
    line-height: 16.41px;
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 3rem;
    color: #fff;
    cursor: pointer;

    img {
      margin-right: 5px;
    }
  }

  .LinearProgess {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    width: 100%;

    .MuiLinearProgress-root {
      width: 77%;
      margin-right: 10px;
      background: #383838;
      border: 4px solid #383838;
    }

    span {
      font-family: Roboto;
      font-size: 14px;
      font-weight: 700;
      line-height: 16.41px;
      text-align: center;
      color: #D43030;

    }
  }
}

// .MuiBackdrop-root {
//   background-color: rgba(0, 0, 0, 0.6);
// }

/*end popup css */
.OrangeBorderSME {
  border-top: 2px solid #f8a038;
}

.inboundIcon {
  width: 16px;
  position: absolute;
  right: 30px;
  top: 71px;
}

.inboundCallingIcon {
  width: 30px;
  position: absolute;
  right: 30px;
  top: 71px;
}

.repeatBuyer {
  font-weight: 400;
  background-color: #f8a038;
  position: absolute;
  z-index: 999;
  top: -5px;
  height: 16px;
  font-size: 10px;
  width: auto;
  padding: 2px 2px;
  left: 20px;
  color: #fff;
  border-radius: 4px 4px 0px 0px;
  text-align: center;
  line-height: 14px;
}

.slick-slide {
  position: relative;
}

.SmeAgentAssist {
  position: relative;
  border: 1px solid #0065ff;
  border-radius: 8px;
  padding: 7px 15px;
  width: 100%;
  color: #0065ff;
  background-color: #fff;
  cursor: pointer;

  button {
    font-family: 'Roboto';
    outline: none;
    color: #0065ff;
    font-weight: 600;
    letter-spacing: 0.17px;
    background-color: transparent;
    border: none;
    width: 100%;
    font-size: 14px;
  }

  .activeBlack {
    color: #253858;
    width: auto;
  }

  .expandmoreIconAgentAsist {
    top: 5px;
    position: absolute;
    right: 17px;
  }

  p {
    font-size: 12px;
    font-weight: 600;
    font-family: 'Roboto';
    margin-top: 10px;

    a {
      color: #0065ff;
    }
  }
}

.unreadChatNotif {
  pointer-events: visible !important;

  .MuiTooltip-tooltip {
    height: auto;
    background-color: #E7FED8;
    padding: 10px 15px;
    letter-spacing: 0.2px;
    box-shadow: 0px 3px 12px 0px #00000014;

    .whatsappIcon {
      font-family: Roboto;
      font-size: 12px;
      font-weight: 600;
      line-height: 14.06px;
      text-align: left;
      display: flex;
      align-items: center;
      color: #28CF17;

      svg {
        margin-right: 5px;
        font-size: 15px;
      }
    }

    h4 {
      font-family: Roboto;
      font-size: 16px;
      font-weight: 500;
      line-height: 18.75px;
      text-align: left;
      color: #253858E3;
      margin: 5px 0px;
    }

    span {
      font-family: Roboto;
      font-size: 12px;
      font-weight: 500;
      line-height: 14.06px;
      text-align: left;
      color: #25385899;

      b {
        color: #253858E3;
      }
    }

    .message {
      font-family: Roboto;
      font-size: 14px;
      font-weight: 400;
      line-height: 16.41px;
      text-align: left;
      color: rgba(37, 56, 88, 0.6);
      padding-top: 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

    }

    button {
      border: 1px solid #28CF17;
      outline: none;
      font-family: Roboto;
      border-radius: 24px;
      margin-top: 10px;
      width: 130px;
      padding: 6px 17px;
      margin-right: 10px;
      font-size: 13px;
      font-weight: 500;
      line-height: 18.75px;
      text-align: center;
      color: rgba(37, 56, 88, 0.8901960784);
      background-color: transparent;
      cursor: pointer;
    }

    .closeButton {
      border: 1px solid rgba(37, 56, 88, 0.6);
      color: rgba(37, 56, 88, 0.6);
      border-radius: 24px;
      width: 56px;
      height: 30px;
      padding: 3px;
      position: relative;
      top: 11px;
      cursor: pointer;
    }

    .MuiTooltip-arrow {
      &::before {
        background-color: #E7FED8;
      }
    }
  }
}

.CustReqCallBackPopup {
  .TextCenter {
    text-align: center;
  }

  .MuiDialog-paperWidthSm {
    width: 527px;
    padding-bottom: 0px;

    h6 {
      font-family: Roboto;
      font-size: 32px;
      font-weight: 600;
      line-height: 37.5px;
      text-align: center;
      color: #ED765E;
    }

    p {
      font-family: Roboto;
      font-size: 23px;
      font-weight: 500;
      line-height: 31px;
      text-align: center;
    }
  }

  .MuiDialogContent-root {
    padding: 0px !important;
  }

  .CallBackDataDetails {
    width: 92%;
    height: auto;
    border-radius: 8px;
    border: 1px dashed #0065FF;
    margin: 20px;
    background: #0065FF0D;
    box-sizing: border-box;
    padding: 15px 20px;

    label {
      font-family: Roboto;
      font-size: 16px;
      font-weight: 400;
      line-height: 33px;
      text-align: center;
      color: #253858E3;
    }

    h4 {
      font-family: Roboto;
      font-size: 18px;
      font-weight: 600;
      line-height: 33px;
      text-align: left;
      color: #253858E3;

      span {
        font-family: Roboto;
        font-size: 14px;
        font-weight: 400;
        line-height: 33px;
        text-align: left;
      }
    }

    .Category {
      font-family: Roboto;
      font-size: 16px;
      font-weight: 500;
      height: 28px;
      position: relative;
      border-radius: 8px;
      line-height: 27.75px;
      text-align: center;
      margin-bottom: 17px;
      top: 6px;
    }

    .CusReq {
      background: #FF8787;
      color: #531111;
      width: 172px;
    }

    .CusAgreed {
      background: #CAFFD0;
      color: #208A2C;
      width: 145px;
    }

    .PymntCallBack {
      background: #BBC9FA;
      color: #1644BB;
      width: 145px;
    }
  }

  .loginBtn {
    background: #0065ff;
    border-radius: 8px;
    color: #fff;
    padding: 10px;
    letter-spacing: 0.17px;
    border: none;
    font-family: Roboto;
    width: 262px;
    height: 50px;
    font-size: 16px;
    font-weight: 500;
    line-height: 18.75px;
    text-align: left;
    outline: none;
    margin: 20px 0px 10px 0px;
    box-shadow: none;
  }

  .LogoutBtn {
    background: #fff;
    border-radius: 8px;
    color: #0065ff;
    border: 1px solid #0065ff;
    padding: 10px;
    letter-spacing: 0.17px;
    font-family: Roboto;
    width: 262px;
    height: 50px;
    font-size: 16px;
    font-weight: 500;
    line-height: 18.75px;
    text-align: left;
    outline: none;
    margin: 10px 0px 20px 0px;
    box-shadow: none;
  }

  .Footer {
    background: #FFDDDA;
    font-family: Roboto;
    width: 100%;

    p {
      font-size: 15px;
      font-weight: 500;
      line-height: 22px;
      text-align: center;
      color: #E90000;
      padding: 15px 20px;
    }
  }
}

.PotentLeadsPanel {
  .MuiDialog-paperWidthSm {
    height: auto;
    overflow-x: hidden;
    width: 400px;

    .MuiDialogTitle-root {
      padding-bottom: 0px;

      h6 {
        color: #2e2e2e;
        text-align: center;
      }
    }

    ul {
      list-style-type: none;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      li {
        font: normal normal normal 14px/24px Roboto;
        letter-spacing: 0.22px;
        color: #253858;
        padding: 2px 0px;
        text-align: center;
        width: 60%;

        &:nth-child(2n+1) {
          text-align: left;
          width: 40%;
        }
      }
    }

    p {
      font: normal normal 500 13px/17px Roboto;
      letter-spacing: 0.22px;
      color: #253858;
      margin-bottom: 14px;
    }
  }
}

.PotentLeadsPanelIcon {
  width: 35px;
  border: 1.5px solid #E06666;
  margin-left: 15px;
  box-shadow: 0px 0px 0px 4px #FFDCDC;
  height: 35px;
  border-radius: 25px;
}

.alartMsg {
  color: #0065FF;
  margin-left: 18px;
  margin-top: 9px;
  cursor: pointer;
  -webkit-animation: shake 0.9s ease-in-out 0.9s infinite alternate;
}

@-webkit-keyframes shake {
  0% {
    transform: translate(1px, 1px) rotate(0deg);
  }

  10% {
    transform: translate(-1px, -2px) rotate(-1deg);
  }

  20% {
    transform: translate(-3px, 0px) rotate(1deg);
  }

  30% {
    transform: translate(3px, 2px) rotate(0deg);
  }

  40% {
    transform: translate(1px, -1px) rotate(1deg);
  }

  50% {
    transform: translate(-1px, 2px) rotate(-1deg);
  }

  60% {
    transform: translate(-3px, 1px) rotate(0deg);
  }

  70% {
    transform: translate(3px, 1px) rotate(-1deg);
  }

  80% {
    transform: translate(-1px, -1px) rotate(1deg);
  }

  90% {
    transform: translate(1px, 2px) rotate(0deg);
  }

  100% {
    transform: translate(1px, -2px) rotate(-1deg);
  }
}

@keyframes shake {
  0% {
    transform: translate(1px, 1px) rotate(0deg);
  }

  10% {
    transform: translate(-1px, -2px) rotate(-1deg);
  }

  20% {
    transform: translate(-3px, 0px) rotate(1deg);
  }

  30% {
    transform: translate(3px, 2px) rotate(0deg);
  }

  40% {
    transform: translate(1px, -1px) rotate(1deg);
  }

  50% {
    transform: translate(-1px, 2px) rotate(-1deg);
  }

  60% {
    transform: translate(-3px, 1px) rotate(0deg);
  }

  70% {
    transform: translate(3px, 1px) rotate(-1deg);
  }

  80% {
    transform: translate(-1px, -1px) rotate(1deg);
  }

  90% {
    transform: translate(1px, 2px) rotate(0deg);
  }

  100% {
    transform: translate(1px, -2px) rotate(-1deg);
  }
}

.callIntentPopup {
  margin: 10px auto !important;
  max-width: 560px;

  .item {
    cursor: pointer;
    text-align: center;
    padding: 10px;
    height: 100%;
    border-radius: 5px;

    &.positive {
      background: #d0ffd0;

      ul {
        margin-top: 18px;
      }
    }

    &.neutral {
      background: #e5f2fd;

      ul {
        margin-top: 18px;
      }
    }

    &.negative {
      background: #ffc10761;

      ul {
        margin-top: 18px;
      }
    }

    &.extNegative {
      background: #ffd2c1;
    }

    b {
      font-weight: 1000;
    }

    ul {
      margin-left: 14px;
      text-align: left;
    }

    .icon {
      width: 38px;
      height: 30px;
      background: url("/public/images/face.svg") no-repeat;
      background-size: 100%;
      display: inline-block;

      &.one {
        background-position: 0px 0px;
      }

      &.two {
        background-position: 0 -40px;
      }

      &.three {
        background-position: 0 -78px;
      }

      &.four {
        background-position: 0 -115px;
      }

    }
  }
}


.PlanPortDetailsPopup {
  .MuiDialog-paperWidthSm {
    max-width: 900px;
    width: 900px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    height: auto;

    .item {
      label {
        color: #253858;
        display: block;
        font: 600 14px / 24px Roboto;
        text-align: left;
        vertical-align: middle;
        width: 100%;
        box-sizing: border-box;
        padding: 0px 0px 2px;
      }

      img {
        width: 100%;
      }

      span {
        letter-spacing: 0.17px;
        color: #808080;
        font-size: 12px;
        font-weight: 500;
        padding: 0px;
        vertical-align: middle;
      }
    }

    hr {
      width: 100%;
      height: 1px;
      border: 1px solid #e2e2e280;
      margin-bottom: 10px;
    }

    .cjbutton {
      border: 1px solid #0065ff;
      border-radius: 8px;
      background-color: transparent;
      color: #0065ff;
      margin: 0px 0px 15px;
      letter-spacing: 0.17px;
      width: auto;
      padding: 8px 11px;
      font: normal normal normal 12px/21px Roboto;
      cursor: pointer;
      outline: none;
      text-transform: capitalize;
    }
  }

  .MuiTypography-h6 {
    padding-left: 16px;
  }
}

.MuiPickersPopper-paper {

  &::after {
    display: none !important;
  }

  // .MuiDateCalendar-root {
  //   width: auto;
  // }
}

.MuiFormControl-root {
  width: 100%;
}

.LastTenLeadPopup {
  .MuiDialog-paperWidthSm {
    max-width: 500px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 500px;
    height: auto;
  }

  .MuiTableCell-root {
    font: normal normal normal 12px/16px Roboto;
    padding: 7px;
    color: #808080;
    border-bottom: none;
  }

  .MuiPaper-elevation1 {
    box-shadow: none;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  h6 {
    text-align: left;
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
    padding-left: 10px;
  }

  .MuiTableRow-root {
    td {
      font: normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      border: none;
    }
  }

  .moreDeatilsData:nth-child(odd) {
    background-color: #f8f8f8;
    border-radius: 4px;
  }
}

.paddingTopLeft {
  padding: 20px 13px !important;
}

.IncomingServiceCall {
  .PredictiveCallAlertSection {
    height: 200px;
    width: 235px;

    img {
      height: 100%;
    }
  }
}

.hwOpportunityPopUp {
  .MuiDialog-paperWidthSm {
    max-width: 1250px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 1250px;
    height: auto;
  }

  .MuiTableCell-root {
    font: normal normal normal 12px/16px Roboto;
    padding: 7px;
    color: #808080;
    border-bottom: none;
  }

  .MuiPaper-elevation1 {
    box-shadow: none;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  .MuiTableRow-root {
    td {
      font: normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      border: none;
    }
  }

  .moreDeatilsData:nth-child(odd) {
    background-color: #f8f8f8;
    border-radius: 4px;
  }

  .addLeadQueueBtn {
    border: none;
    background: #0065ff;
    border-radius: 8px;
    padding: 6px 14px;
    text-align: center;
    font: normal normal 12px/21px Roboto;
    letter-spacing: 0.17px;
    color: #fff;
    font-weight: 600;
    outline: none;
  }

  .content {
    margin-top: 20px;
  }
}


.AnualOpenLeadsPanel {
  .MuiDialog-paperWidthSm {
    height: auto;
    overflow-x: hidden;
    width: 850px;

    .MuiTableCell-root {
      font: normal normal normal 12px/16px Roboto;
      padding: 7px;
      color: #808080;
      border-bottom: none;
    }

    .MuiPaper-elevation1 {
      box-shadow: none;
    }

    .MuiTableHead-root {
      background-color: transparent;
    }

    h6 {
      text-align: left;
      font: normal normal 18px/24px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      font-weight: 600;
      padding-left: 10px;
    }

    .MuiTableRow-root {
      td {
        font: normal normal 12px/16px Roboto;
        letter-spacing: 0px;
        color: #2e2e2e;
        border: none;
      }
    }

    .moreDeatilsData:nth-child(odd) {
      background-color: #f8f8f8;
      border-radius: 4px;
    }

    .MuiIconButton-root {
      background-color: #eaeaea;
      position: absolute;
      padding: 5px;

      svg {
        font-size: 1.2rem;
      }
    }

    p {
      font: normal normal 500 14px/17px Roboto;
      letter-spacing: 0.22px;
      color: #253858;
      margin: 14px 0px;
    }
  }
}

.anualOpenLeadTag {
  line-height: 14px;
  top: 1px;
  font-weight: 500;
  background-color: #ffccaf;
  position: absolute;
  left: 185px;
  z-index: 999;
  height: 18px;
  font-size: 10px;
  width: auto;
  padding: 3px 5px;
  color: #c55e23;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
}


.AnualOpenCommentsPanel {
  h6 {
    text-align: center;
    padding: 10px;
  }

  .MuiDialog-paperWidthSm {
    height: auto;
    overflow-x: hidden;
    width: 700px;

    p {
      font: normal normal 500 14px/17px Roboto;
      letter-spacing: 0.22px;
      color: #253858;
      margin-top: 10px;
    }

    textarea {
      border: 1px solid #808080;
      border-radius: 4px;
      width: 100%;
      font-size: 14px;
      padding: 10px 75px 10px 10px;
      outline: none;
      font-family: "Roboto";
      letter-spacing: 0.22px;
      margin: 25px 0px;
    }

    button {
      background: #0065ff;
      border-radius: 8px;
      width: 90px;
      letter-spacing: 0.17px;
      padding: 8px;
      border: none;
      color: #ffffff;
      font: normal normal 12px/21px Roboto;
      cursor: pointer;
      outline: none;
    }

    .Text-Center {
      text-align: center;
    }

    .counting {
      position: relative;

      span {
        position: absolute;
        top: 30px;
        right: 12px;
        color: #0ea90e;
        font-weight: 500;
        font-size: 11px;
      }
    }
  }
}

.ReferalPopup {
  .MuiDialog-paperWidthSm {
    width: 400px;
    font-size: 13px;
    font-family: roboto;

    label {
      font-weight: 600;
    }
  }
}

.HomeVisitPopup {
  .MuiDialog-paperWidthSm {
    width: 610px;
    padding-bottom: 0px;
    margin-bottom: 0px;


    img {
      width: 100%;
      border-radius: 8px;
    }

    .MuiDialogContent-root {
      padding: 5px !important;
    }
  }
}

.StoriesTooltip {
  .MuiTooltip-tooltip {
    background: linear-gradient(148.86deg, #84FFAE -31.97%, #336EDF 125.12%);
    border-radius: 0px 10px 10px 10px;
    margin-left: 0px !important;
    position: relative;
    left: -10px;
    top: 50px;
    width: 181px;
    padding: 12px;

    h4 {
      font-family: Roboto;
      font-size: 15px;
      font-weight: 700;
      line-height: 17.58px;
      text-align: left;
      color: #fff;
      letter-spacing: 0.3px;
      margin-bottom: 4px;
    }

    p {
      font-family: Roboto;
      font-size: 13px;
      font-weight: 400;
      line-height: 15.23px;
      letter-spacing: 0.3px;
      color: #fff;
      text-align: left;

    }
  }
}

.pauseCallingPopup {
  h6 {
    font-family: Roboto;
    font-size: 18px;
    font-weight: 600;
    line-height: 21.09px;
    text-align: left;
    color: #253858E3;
    padding-left: 0px;
  }

  .MuiDialog-paperWidthSm {
    border-radius: 4px;
    width: 297px;
  }

  p {
    margin-bottom: 2.5rem;
    font-family: Roboto;
    font-size: 12px;
    font-weight: 400;
    line-height: 14.06px;
    text-align: left;
    color: #25385899;
  }

  .pauseCallingBtn {
    width: 100%;
    box-shadow: none;
    border-radius: 4px;
    background-color: #0065FF;
    font-family: Roboto;
    font-size: 12px;
    font-weight: 600;
    line-height: 14.06px;
    text-align: center;
    height: 38px;
  }
}

.PauseFOSCallTooltip {
  letter-spacing: 0.3px;
  background-color: #0065FF;
  border-radius: 8px;
  font-family: Roboto;
  font-size: 12px;
  padding: 15px 20px;
  font-weight: 600;
  line-height: 18px;
  text-align: left;

  .MuiTooltip-arrow {
    color: #0065FF;
  }

  h3 {
    margin-bottom: 15px;
  }
}

.ResumeFOSCallTooltip {
  background-color: #fff;
  padding: 15px;
  font-size: 12px;

  .heading {
    display: flex;
    margin-bottom: 15px;

    h3 {
      color: #253858E3;
      font-family: Roboto;
      font-size: 18px;
      font-weight: 600;
      line-height: 21.09px;
      text-align: left;

    }

    span {
      cursor: pointer;
    }

    svg {
      height: 19px;
      width: 19px;
      position: absolute;
      right: 5px;
      color: #000;
      cursor: pointer;
    }
  }

  p {

    font-family: Roboto;
    font-size: 12px;
    font-weight: 500;
    line-height: 14.06px;
    text-align: left;
    color: #25385899;
  }

  .MuiTooltip-arrow {
    color: #fff;
  }
}

.SearchLeadPopUp {
  .MuiDialog-paperWidthSm {
    max-width: 600px;
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 600px;
    height: auto;
  }

  .MuiTableCell-root {
    font: normal normal normal 12px/16px Roboto;
    padding: 7px;
    color: #808080;
    border-bottom: none;
  }

  .MuiPaper-elevation1 {
    box-shadow: none;
  }

  .MuiTableHead-root {
    background-color: transparent;
  }

  .MuiTableRow-root {
    td {
      font: normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      border: none;
    }
  }

  .content {
    margin-top: 20px;
  }
}

.VirtualNumberPopup {
  table {
    .MuiTableCell-root {
      font: normal normal 500 12px/16px Roboto;
      padding: 7px 10px;
      color: #808080;
      border-bottom: none;
      background-color: transparent;
    }

    .MuiPaper-elevation1 {
      box-shadow: none;
    }

    thead {
      .MuiTableRow-root {
        background-color: transparent !important;
      }
    }

    .MuiTableRow-root {
      background-color: #FFF;

      td {
        font: normal normal 12px/16px Roboto;
        letter-spacing: 0px;
        color: #2e2e2e;
        border: none;
        padding: 0px 10px;
      }
    }



    .MuiIconButton-root {
      background-color: #eaeaea;
      position: absolute;
      padding: 5px;

      svg {
        font-size: 1.2rem;
      }
    }

    .MuiTableRow-root:nth-child(odd) {
      background-color: #f8f8f8;
      border-radius: 4px;
    }
  }

  .MuiRadio-root {
    color: #808080;
    padding: 5px;
  }

  .Mui-checked {
    color: #0065FF !important;
  }

  .callableBtn {
    background: #fff;
    border-radius: 8px;
    padding: 5px 5px;
    cursor: pointer;
    text-align: center;
    font: normal normal 12px/21px Roboto;
    letter-spacing: 0.17px;
    color: #0065ff;
    border: 1px solid #0065ff;
    font-weight: 600;
    margin-right: 12px;
    margin-top: 1rem;
    outline: none;
  }

  .clearAllBtn {
    border: none;
    cursor: pointer;
    background: #0065ff;
    border-radius: 8px;
    padding: 6px 45px;
    text-align: center;
    font: normal normal 12px/21px Roboto;
    letter-spacing: 0.17px;
    color: #fff;
    font-weight: 600;
    outline: none;
    margin-top: 1rem;
  }

}

.AddToQueueErrorPopup {
  .MuiDialog-paperWidthSm {
    max-width: 500px;
    box-shadow: 0px 0px 16px rgba(0, 0, 0, 0.**********);
    border-radius: 16px;
    width: 500px;
    height: auto;
    min-height: 220px;
    padding-bottom: 0px;

    .MuiDialogContent-root {

      p {
        font-family: Roboto;
        font-size: 15px;
        color: #253858E3;
        font-weight: 400;
        line-height: 18.75px;
        padding: 20px 5px 10px;
        text-align: left;
      }

      h2 {
        font-family: Roboto;
        font-size: 24px;
        color: #253858E3;
        font-weight: 700;
        line-height: 28.13px;
        padding: 20px 5px 0px;
        text-align: left;
      }

      .LeadId {
        padding-top: 0px;
        font-size: 13px;
      }

      img {
        position: absolute;
        bottom: 0px;
        width: 175px;
        right: 10px;
      }

    }
  }

  @media (max-width: 600px) and (min-width: 320px) {

    img {
      display: none;
    }

    .MuiDialog-paperWidthSm {
      margin: 10px;
    }
  }
}

.SMEAssociationPopup {
  .MuiDialog-paperWidthSm {
    max-width: 720px;
    box-shadow: 0px 0px 16px rgba(0, 0, 0, 0.**********);
    border-radius: 16px;
    width: 720px;
    height: auto;
    min-height: 220px;
    padding-bottom: 0px;

    th {
      &:first-child {
        width: 200px;
      }
    }
  }
}


.HealthHNICustPolicesPopup {
  .MuiDialog-paperWidthSm {
    width: 1136px;
    height: 650px;

    h6 {
      font-family: Roboto;
      font-size: 22px;
      font-weight: 600;
      line-height: 25.78px;
      text-align: left;
      color: #253858E3;

    }

    .borderBottom {
      border-bottom: 1px solid #ddd;
    }

    .nodataFound {
      font-weight: 600;
      text-align: center;
      margin-top: 5rem;
      font-size: 20px;
    }

    // .MuiTableContainer-root {
    //   max-height: 500px;
    // }

    .userIcon {
      position: relative;
      top: 7px;
      cursor: pointer;
      left: 8px;
    }

    th {
      background-color: #253858;
      color: #fff;
      padding: 12px;
      font-family: Roboto;
      font-size: 13px;
      font-weight: 500;
      line-height: 15.23px;
      text-align: left;

    }

    td {
      font-family: Roboto;
      font-size: 13px;
      font-weight: 500;
      line-height: 15.23px;
      text-align: left;
      color: #253858E3;
      padding: 12px;
      max-width: 120px;
      word-wrap: break-word;

      &:nth-child(3) {
        max-width: 200px;
      }

      &:first-child {
        min-width: 50px;
      }

    }

    .tabUI {
      button {
        font-family: Roboto;
        font-size: 13px;
        font-weight: 600;
        line-height: 15.23px;
        text-align: left;
        background: #2538581A;
        border-radius: 22px;
        min-height: 30px;
        margin-right: 15px;
        padding: 10px 18px;
        color: #253858E3;
      }

      .MuiTabs-indicator {
        display: none;
      }

      .Mui-selected {
        background: #253858;
        color: #fff;
      }
    }

    .MuiPaper-elevation1 {
      box-shadow: none;
      border-radius: 0px;
    }

    .PeopleLinkedDetails {
      background: #F9F9F9;
      padding: 10px 25px 10px 40px;

      .heading {
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        font-family: Roboto;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 25px;
        line-height: 16.41px;
        text-align: left;
        color: #253858E3;
      }

      svg {
        margin-bottom: 5px;
        color: #0065FF;
      }
    }

    .deatils {
      background-color: #fff;
      padding: 12px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: end;
      flex-wrap: wrap;

      p {
        width: 50%;
        font-family: Roboto;
        font-size: 14px;
        font-weight: 400;
        line-height: 23px;
        text-align: left;
        color: #253858E3;

        &:nth-child(2n+2) {
          text-align: right;
          font-weight: 500;
        }
      }
    }

    .nodataFound {
      margin-top: 0px;
    }
  }
}

.ReleaseLeadPopup {
  .CallReleaseCriteria {
    border: 1px solid #b4dbed;
    width: auto;
    position: absolute;
    height: auto;
    right: 42px;
    float: right;
    top: 10px;
    padding: 10px 20px;
    border-radius: 20px;
    background-color: rgba(200, 219, 249, 0.3607843137);

    ul {
      li {
        font: normal normal 500 12px/24px Roboto;
        color: #253858;
        margin-left: 14px;
      }
    }

    p {
      color: #252558;
      margin-top: 3px;
      margin-left: 1px;
    }
  }

  .MuiDialog-paperWidthSm {
    min-height: 300px;

    .nodataFound {
      margin-top: 5rem;
    }
  }
}

.CovidPeriodPopup {
  .MuiDialog-paperWidthSm {
    width: 753px;
    background-image: url("/public/images/salesview/covidBg.svg");
    height: 416px;
    background-position: bottom left;
    background-repeat: no-repeat;

    .MuiTypography-root {
      padding: 0px;

      h6 {
        color: #fff;

      }
    }

    h5 {
      font-family: Poppins;
      font-weight: 700;
      font-size: 28px;
      line-height: 33px;
      letter-spacing: 0%;
      text-align: center;
      color: #1148A0;
    }

    p {
      font-family: Poppins;
      font-weight: 700;
      font-size: 22px;
      line-height: 33px;
      letter-spacing: 0px;
      text-align: center;
      color: #1148A0;

    }

    .ReturnPercentage {
      background-color: rgba(45, 125, 85, 0.1019607843);
      font-family: Poppins;
      font-weight: 700;
      font-size: 52px;
      line-height: 67px;
      letter-spacing: 0px;
      border-radius: 20px;
      text-align: center;
      color: #2D7D55;
      height: 65px;
      width: 197px;
      margin: 1rem auto;

    }

    .PitchBtn {
      background: #E3E9FD;
      font-family: Roboto;
      font-weight: 600;
      font-size: 26px;
      line-height: 52.47px;
      letter-spacing: 0px;
      width: 584px;
      height: 51px;
      text-align: center;
      color: #274A7E;
      border-radius: 10px;
      margin: 4.3rem auto 0rem;

    }
  }
}

.UpcomingEvent {
  width: 100%;
  box-shadow: none;
  border: 1px solid #0066ED66;
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 10px;

  .MuiAccordionSummary-root {
    background-color: #fff;
  }

  .MuiCardContent-root {
    background: #0066ED12;
    border-radius: 10px;
    margin: 10px;
    padding: 12px;

    &:first-child {
      margin-top: 0px;
    }

    p {
      font-family: Roboto;
      font-weight: 400;
      font-size: 13px;
      line-height: 20px;
      color: #253858E5;
    }

    h6 {
      font-family: Roboto;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      letter-spacing: 0%;
      color: #253858;

    }
  }

  .MuiAccordionDetails-root {
    overflow-y: auto;
    max-height: 180px;
    padding: 0px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #0065FF;
      border-radius: 10px;
      border: none;
      box-shadow: none;
    }


  }

  .MuiAccordionSummary-content {
    align-items: center;
    margin: 10px 0px;
    height: 38px;

    p {
      font-family: Roboto;
      font-weight: 600;
      font-size: 18px;
      line-height: 21.09px;
      letter-spacing: 0%;
      color: #0E4EA4;
    }

    img {
      margin-right: 10px;
    }
  }

}





.RolloverLeadPopup {
  .MuiDialogContent-root {
    .MuiIconButton-root {
      background-color: transparent !important;
      position: static !important;
    }

    .MuiTableCell-root {
      font: normal normal normal 12px/16px Roboto;
      padding: 7px;
      color: #808080;
      border-bottom: none;
      background-color: transparent;
    }

    .MuiPaper-elevation1 {
      box-shadow: none;
    }

    .MuiTableHead-root {
      background-color: transparent;

      .MuiTableRow-root {
        background-color: transparent !important;
      }
    }

    h6 {
      text-align: left;
      font: normal normal 18px/24px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      font-weight: 600;
      padding-left: 10px;
    }

    .MuiTableRow-root {
      td {
        font: normal normal 12px/16px Roboto;
        letter-spacing: 0px;
        color: #2e2e2e;
        border: none;
      }

      &:nth-child(odd) {
        background-color: #f8f8f8;
        border-radius: 4px;
      }
    }



    .MuiIconButton-root {
      background-color: #eaeaea;
      position: absolute;
      padding: 5px;

      svg {
        font-size: 1.2rem;
      }
    }

    .ViewBtn {
      border: none;
      background: #0065ff;
      border-radius: 8px;
      padding: 10px 25px;
      text-align: center;
      font: normal normal 14px/21px Roboto;
      letter-spacing: 0.17px;
      color: #fff;
      font-weight: 500;
      margin-right: 12px;
      outline: none;
    }
  }
}

.AdditionaInfoPopup {
  .MuiDialog-paperWidthSm {
    width: 410px;
    text-align: center;
    height: auto;

    .MuiDialogContent-root {
      padding-top: 0px !important;
    }

    /* QnA Container */
    .qna-container {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    /* QnA Item */
    .qna-item {
      background: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    /* Question */
    .question {
      font-size: 1rem;
      font-weight: bold;
      color: #555;
      margin-bottom: 5px;
    }

    h6 {
      padding-left: 0px;
    }

    p {
      padding-left: 2px;
    }

    ul {
      text-align: left;
      font: normal normal normal 13px/24px Roboto;
      color: #808080;

      li {
        display: flex;
        justify-content: space-between;

        label {
          font-weight: 600;
        }
      }
    }


  }
}

// CrossSellModal styling to match RejectLeadPopUp
.CrossSellModal {
  .MuiDialog-paperWidthSm {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 16px;
    width: 700px;
    height: 380px;
    padding-bottom: 10px;
  }

  .MuiDialogContent-root {
    padding: 20px 30px !important;
    height: auto;

    p {
      font: normal normal bold 14px/19px Roboto;
      letter-spacing: 0px;
      color: #2e2e2e;
      margin-top: 20px;
      margin-left: 44px;
    }

    button {
      border: none;
      background: #0065ff;
      border-radius: 8px;
      padding: 8px 45px;
      text-align: center;
      font: normal normal 12px/21px Roboto;
      letter-spacing: 0.17px;
      color: #fff;
      font-weight: 600;
      outline: none;
      margin-top: 2rem;
    }

    .info {
      position: relative;
      top: 34px;
      left: 4px;
      color: #808080;
      font-size: 20px !important;
    }

    textarea {
      border: 1px solid #808080;
      border-radius: 4px;
      width: 100%;
      font-size: 14px;
      padding: 10px;
      outline: none;
      resize: none;
      font-family: "Roboto";
      letter-spacing: 0.22px;
      margin-top: 25px;
    }
  }
}

.perliferater {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%; // Adjust width as needed
  height: 80%; // Adjust height as needed
  background-color: #fff;
  box-shadow: 0px 0px 16px rgba(0, 0, 0, 0.**********);
  border-radius: 16px;
  padding: 20px;
}

// Lead Rejection Prompt Styles
.lead-rejection-prompt {
  .MuiDialog-paper {
    min-height: auto;
    border-radius: 16px;
  }
h2{
  p {
    text-align: left;
    font: normal normal 600 12px/13px Roboto;
    letter-spacing: 0.17px;
    color: #808080;
    opacity: 1;
    padding: 8px 10px 0px;
  } 
}
  h6 {
    text-align: left;
    font: normal normal 18px/24px Roboto;
    letter-spacing: 0px;
    color: #2e2e2e;
    font-weight: 600;
    padding-left: 10px;
  }


  .loading-container {
    display: flex;
    justify-content: center;
    padding: 20px;
  }

  .success-banner {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 6px 12px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
  }

  .success-text {
    font-weight: bold;
    color: #155724;
    font-size: 13px;
  }

  .table-container {
    margin-bottom: 16px;
    border-radius: 8px;
    overflow: hidden;

    &.table-container-scrollable {
      overflow-x: auto;
      overflow-y: auto;
      max-height: 450px; // Limits height to show ~10 rows before scrolling
      position: relative;
      box-shadow: 0px 0px 16px #00000014;
      // Fixed/Sticky headers
      .MuiTableHead-root {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #f5f5f5 !important;
        
        .MuiTableRow-head {
          background-color: #f5f5f5 !important;
        }
        
        .MuiTableCell-head {
          background-color: #f5f5f5 !important;
          border-bottom: 1px solid #e0e0e0 !important;
        }
      }

    }
  }

  .table {
    table-layout: fixed;

    .MuiTableHead-root {
      background-color: #f5f5f5;
    }

    .MuiTableBody-root {
      .MuiTableRow-root {
        &:nth-child(even) {
          background-color: #f5f5f5;
        }
        
        &:hover {
          background-color: #f0f0f0;
        }
      }
    }

    .MuiTableCell-root {
      // border-bottom: 1px solid #e0e0e0;
      vertical-align: middle;
    }

    &.table-responsive {
      min-width: 800px;
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
    }
  }

  // Prevent text wrapping in cells and set minimum widths
  .cell-nowrap {
    white-space: nowrap;

    &.select-all-cell {
      min-width: 120px;
    }

    &.lead-id-cell {
      min-width: 100px;
    }

    &.body-cell-company-name,
    &.header-cell {
      min-width: 120px;
    }

    &.date-cell {
      min-width: 100px;
    }

    &.status-cell {
      min-width: 80px;
    }
  }

  .header-cell {
    padding: 8px 12px;
    width: 160px;
    font-weight: 600;
    font-size: 13px;
    color: #424242;
  }

  .select-all-cell {
    padding: 8px 12px;
    width: 130px;
    font-weight: 600;
    font-size: 13px;
    color: #424242;
  }

  .lead-id-cell {
    padding: 8px 12px;
    width: 110px;
    font-weight: 600;
    font-size: 13px;
    color: #424242;
  }

  .date-cell {
    padding: 8px 12px;
    width: 110px;
    font-weight: 600;
    font-size: 13px;
    color: #424242;
  }

  .status-cell {
    padding: 8px 12px;
    width: 100px;
    font-weight: 600;
    font-size: 13px;
    color: #424242;
  }

  .select-all-container {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .select-all-text {
    font-size: 13px;
    font-weight: 600;
    color: #424242;
  }

  .body-cell {
    padding: 4px 12px;
    height: 36px;
  }

  .body-cell-text {
    padding: 4px 12px;
    font-size: 13px;
    color: #616161;
    height: 36px;
  }

  // Specific widths for body cells to match headers
  .body-cell-select {
    width: 130px;
  }

  .body-cell-lead-id {
    width: 110px;
  }

  .body-cell-company-name {
    width: 120px;
  }

  .body-cell-date {
    width: 110px;
  }

  .body-cell-product {
    width: 160px;
  }

  .body-cell-status {
    width: 100px;
  }

  .dialog-actions {
    padding: 8px 24px 16px 24px;
    gap: 12px;
  }

  .MuiTextField-root {
    .MuiOutlinedInput-root {
      height: auto;
      min-height: 60px;
    }

    .MuiInputLabel-root {
      font-size: 14px;
    }

    .MuiOutlinedInput-input {
      padding: 8px 12px;
      font-size: 13px;
    }
  }

  .MuiCheckbox-root {
    padding: 4px;
  }

  .MuiButton-root {
    text-transform: none;
    font-weight: 500;
    padding: 8px 16px;
    font-size: 13px;
  }
}


.tip-container {
    background: linear-gradient(90deg, rgb(***********) 0%, rgb(***********) 49%, rgba(255, 255, 255, 1) 100%);
    border: 2px solid #f0c14b;
    border-radius: 8px;
    padding: 7px 16px;
    margin: 16px 0;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(240, 193, 75, 0.3);
    position: relative;
    overflow: hidden;
}

.tip-accent-bar {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #f0c14b 0%, #ff9800 100%);
}

.tip-icon {
    font-size: 18px;
    margin-right: 12px;
    color: #ff6b35;
    svg{
      top:2px;
      position: relative; 
    }
}

.tip-text {
    font-weight: 500;
    color: #2e3b4e;
    font-size: 13px;
    line-height: 1.4;
}

.tip-label {
    color: #ff6b35;
}

.tip-emphasis {
    font-style: italic;
    font-weight: 700;
}
.primeLogo{
  margin: 2px 5px 0px 5px;
}




/* BHR Icon Container */
.bhr-icon-container {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  min-width: 24px;
  

}

/* BHR Badge Indicator */
.bhr-bullet-badge {
  min-width: 37px;
  height: 18px;
  position: absolute;
  top: -18px;
  right: -12px;
  z-index: 2;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: bold;
  color: white;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  border: 2px solid white;
  text-align: center;
  line-height: 1;
  white-space: nowrap;
  flex-shrink: 0;
}

/* BHR Enhanced Percentage Display */
.bhr-percentage-enhanced {
  margin-left: 8px;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  white-space: nowrap;
  min-width: 40px;
  height: 20px;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

/* Material-UI Compatibility */
.MuiListItemIcon-root .bhr-icon-container {
  overflow: visible !important;
  
  img {
    margin: 0 !important;
  }
}