.selectProductField {
  .selectProductLabel {
    display: inline-block;
    margin-right: 16px;
    font-size: 14px;
    font-weight: 500;
    color: #253858;
    font-family: Roboto;
  }

  .selectProductOptions {
    display: inline-block;

    .MuiFormControlLabel-root {
      margin-right: 16px;
    }

    .RadioBtn {
      color: #0065ff;

      &.Mui-checked {
        color: #0065ff;
      }
    }
  }
}

.CreateTermReferralLead {
  padding: 15px 20px;
  width: 100%;

  .differentCustomer {
    border-radius: 8px;
    background: rgba(24, 194, 0, 0.05);
    width: 100%;
    padding: 16px;
    margin-bottom: 15px;
    margin-top: 15px;

    p {
      display: flex;
      align-items: center;
      color: #253858;
      font-family: Roboto;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;

      img {
        margin-right: 5px;
      }
    }
  }

  .SameCustomer {
    border-radius: 8px;
    background: rgba(0, 101, 255, 0.05);
    width: 100%;
    padding: 16px;
    margin-bottom: 15px;

    p {
      display: flex;
      align-items: center;
      color: #253858;
      font-family: <PERSON>o;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;

      img {
        margin-right: 5px;
      }
    }
  }

  .useExitMobileNo {

    .MuiFormControlLabel-label {
      color: #253858;
      font-family: Roboto;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }

    svg {
      font-size: 24px;
      color: #BBBBBB;
    }

    .Mui-checked {
      svg {
        color: #0065FF;
      }
    }
  }

  .useExitMobileNoGrey {
    // position: relative;
    // top: -16px;

    .MuiFormControlLabel-label {
      color: #BBBBBB;
      font-family: Roboto;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }

    svg {
      font-size: 24px;
      color: #BBBBBB;
    }

    .Mui-checked {
      svg {
        color: #BBBBBB;
      }
    }
  }

  .CreateLeadBtn {
    color: #FFF;
    font-family: Roboto;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    text-transform: uppercase;
    border-radius: 4px;
    background: #0065FF;
    box-shadow: 0px 3px 12px 0px rgba(78, 78, 78, 0.25);
    padding: 16px 48px;
    width: 290px;
    border: none;
    cursor: pointer;
  }


  .GenderDetails {
    .MuiIconButton-root {
      svg {
        font-size: 24px;

      }
    }

    .MuiFormControlLabel-label {
      color: #253858;
      font-family: Roboto;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }

    .RadioBtn {
      color: #BBBBBB;
    }

    .Mui-checked {
      color: #0065FF !important;

      svg {
        font-size: 24px;

        &:last-child {
          transform: scale(1.3);
        }
      }
    }
  }

  .ReferralLeadNotAllowed {
    background-color: #e31111;  
    color: white;           
    padding: 10px;        
    border-radius: 5px;   
    font-weight: bold;     
    text-align: center;    
    width: fit-content;    
    margin-top: 10px;      
}

}

.createLeadAutocomplete {
  width: 100% !important;

  .MuiAutocomplete-endAdornment {

    .MuiAutocomplete-clearIndicator,
    .MuiAutocomplete-popupIndicator {
      visibility: visible;

      svg {
        color: #252528;
        display: block !important;
      }
    }

  }

  fieldset {
    // border-right: none;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
}

// .MuiAutocomplete-popper {
//   width: 250px !important;
// }

.mobileNumber {
  width: 100%;

  fieldset {

    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}

.disableBtn {
  background-color: #ddd !important;
  cursor: auto !important;
}

.SavingReferralLead {
  padding: 15px 16px;
  width: 100%;

  .differentCustomer {
    border-radius: 8px;
    background: rgba(24, 194, 0, 0.05);
    width: 100%;
    padding: 16px;
    margin-bottom: 15px;
    margin-top: 7px;

    p {
      display: flex;
      align-items: center;
      color: #253858;
      font-family: Roboto;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;

      img {
        margin-right: 5px;
      }
    }
  }

  .SameCustomer {
    border-radius: 8px;
    background: rgba(0, 101, 255, 0.05);
    width: 100%;
    padding: 16px;
    margin-bottom: 15px;
    margin-top: 12px;

    p {
      display: flex;
      align-items: center;
      color: #253858;
      font-family: Roboto;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;

      img {
        margin-right: 5px;
      }
    }
  }

  .useExitMobileNo {
    .MuiFormControlLabel-label {
      color: #253858;
      font-family: Roboto;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }

    svg {
      font-size: 24px;
      color: #BBBBBB;
    }

    .Mui-checked {
      svg {
        color: #0065FF;
      }
    }
  }

  .investmentCategories {
    h3 {
      color: #253858;
      font-family: Roboto;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      margin-bottom: 8px;
    }

    border-radius: 16px;
    background: #F5F5F5;
    padding: 14px 22px 8px;
    display: flex;
    flex-direction: column;

    .MuiIconButton-root {
      svg {
        font-size: 24px;

      }
    }

    .MuiFormControlLabel-label {
      color: #253858;
      font-family: Roboto;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }

    .RadioBtn {
      color: #BBBBBB;
    }

    .Mui-checked {
      color: #0065FF !important;

      svg {
        font-size: 24px;

        &:last-child {
          transform: scale(1.3);
        }
      }
    }
  }

  .btn {
    text-align: center;
    width: 100%;
    position: relative;
    top: 18px;

    .CreateLeadBtn {
      color: #FFF;
      font-family: Roboto;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      text-transform: uppercase;
      border-radius: 4px;
      background: #0065FF;
      box-shadow: 0px 3px 12px 0px rgba(78, 78, 78, 0.25);
      padding: 16px 48px;
      width: 290px;
      border: none;
      cursor: pointer;
    }
  }

  .savingmobileNumber {
    width: 100%;

    fieldset {
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
    }
  }

  .ReferralLeadNotAllowed {
    //background-color: #e31111;  
    color: red;           
    padding: 10px;        
    border-radius: 5px;   
    font-weight: bold;     
    text-align: center;    
    width: fit-content;    
    margin-top: -6px;  
    margin-bottom: -2px;    
  }
}

.pdLeft-0 {
  padding-left: 0px !important;
}

.pdRight-0 {
  padding-right: 0px !important;
}

.pdTop {
  padding-top: 10px;
}

.MuiCollapse-entered {
  min-height: 0px;
  // position: relative;
  // top: 46px;

  .MuiSnackbarContent-root {
    color: #fff;
    height: auto;
    padding: 0px 13px !important;
    line-height: 15px;
  }
}