import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import { useSnackbar } from 'notistack';
import DialogTitle from '@mui/material/DialogTitle';
import { Grid, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { TextInput } from '../../../../components';
import Textarea from '@mui/material/TextareaAutosize';
import { OpenReadOnlyViewMatrix,OpenLeadContentOnClick } from "../../../../services/Common";
import BookingDetails from './BookingDetails';
import BookingHistory from './BookingHistory';
import CircleIcon from '@mui/icons-material/Circle';
import User from '../../../../../src/services/user.service';
import { SetCreditChangeRequest, GetCreditChangeActivityLogs, CreateCreditChangeRequest } from '../../../../../src/layouts/SV/components/Sidebar/helper/sidebarHelper';
import HistoryToggleOffOutlinedIcon from '@mui/icons-material/HistoryToggleOffOutlined';
export default function BookingRequestedPopup(props) {

    const { open, onClose, bookingdata, allData, IsAuthorisedCreator,ProductSelect } = props;
    const { enqueueSnackbar } = useSnackbar();
    const [BookingData, setBookingData] = React.useState([]);
    const [RowWiseData, setRowWiseData] = React.useState([]);
    const [IsFirstLevel, setIsFirstLevel] = React.useState(false);
    const [IsSecondLevel, setIsSecondLevel] = React.useState(false);
    const [CurrentStatusID, setCurrentStatusID] = React.useState(0);
    const [Comments, setComments] = React.useState('');
    let [NewRequestData, setNewRequestData] = React.useState({});
    const [isValidatedRequest, setisValidatedRequest] = React.useState(false);
    const [ActivityLogs, setActivityLogs] = React.useState([]);
    const [BookingHistoryShow, setBookingHistoryShow] = React.useState(false);
    const [IsApproveButtonClick, setIsApproveButtonClick] = React.useState(false);
    const [IsValidReRequest, setIsValidReRequest] = React.useState(false);

    const RequestData = {
        RequestID: "",
        BookingID: "",
        NewAgentID: "",
        AgentTypeID: "",
        ReasonID: "",
        RequestorRemarks: "",
        ReferenceId: ""
    };

    React.useEffect(() => {
        if (bookingdata && Array.isArray(bookingdata) && bookingdata.length > 0) {
            setBookingData(bookingdata);
        }
        if (allData && Array.isArray(allData) && allData.length > 0) {
            setRowWiseData(allData);
            let RequestID = allData[0].RequestID;
            let BookingID = allData[0].BookingId;
            let AgentTypeID = allData[0].AgentTypeID;
            let StatusID = allData[0].StatusID;

            let CurrentRequestData = RequestData;
            CurrentRequestData.RequestID = RequestID;
            CurrentRequestData.BookingID = BookingID;
            CurrentRequestData.NewAgentID = allData[0].NewAdvisorUserID;
            CurrentRequestData.AgentTypeID = AgentTypeID;
            CurrentRequestData.ReasonID = allData[0].ReasonID;
            setNewRequestData(CurrentRequestData);

            setCurrentStatusID(StatusID);

            if (allData[0].FirstLevelApproverUserID == User.UserId && allData[0].StatusID == 1) {
                setIsFirstLevel(true);
                setIsSecondLevel(false);
            }
            else {
                if (IsAuthorisedCreator == 2) {
                    setIsSecondLevel(true);
                    setIsFirstLevel(false);
                }
                else {
                    setIsFirstLevel(false);
                    setIsSecondLevel(false);
                }
            }

            GetCreditChangeActivityLogs(BookingID, AgentTypeID, RequestID).then((result) => {
                if (result && Array.isArray(result) && result.length > 0) {
                    setActivityLogs(result);
                }
            });

            //re-Raise TAT
            let ThresholdDays = 10;
            let givenDate = '';
            if(bookingdata && Array.isArray(bookingdata) && bookingdata.length > 0 && bookingdata[0].OfferCreatedON){
                givenDate = new Date(bookingdata[0].OfferCreatedON);
            }
            else{
                setIsValidReRequest(true);
            }

            if (!isNaN(givenDate.getTime())) {
                const currentDate = new Date();
                const diffInMs = currentDate - givenDate;
                
                const diffInDays = diffInMs / (1000 * 60 * 60 * 24); // milliseconds to days
                setIsValidReRequest(diffInDays > ThresholdDays);
            }
            
        }
    }, [props]);

    const handleChange = (event) => {
        const value = event.target.value;
        const name = event.target.name;
        switch (name) {
            case 'FirstLevelRemarks':
                setComments(value);
                setNewRequestData({ ...NewRequestData, RequestorRemarks: value });
            case 'SecondLevelRemarks':
                setComments(value);
                setNewRequestData({ ...NewRequestData, RequestorRemarks: value });
            default:
                break;
        }
        ValidateSubmitRequest();
    }
    const ValidateSubmitRequest = () => {
        const isValid = NewRequestData.RequestID !== '' && NewRequestData.BookingID !== 0 && NewRequestData.NewAgentID !== 0 && NewRequestData.AgentTypeID !== '' && NewRequestData.ReasonID !== 0 && Comments !== '' && Comments.trim().length >= 4 && Comments.trim().length <= 400;
        setNewRequestData({ ...NewRequestData, RequestorRemarks: Comments.trim() });
        setisValidatedRequest(isValid);
    }

    const SubmitRequest = (Action) => {
        setIsApproveButtonClick(true);
        if (isValidatedRequest) {
            NewRequestData = { ...NewRequestData, Action: Action, CurrentStatus: CurrentStatusID };
            var Data = NewRequestData;
            SetCreditChangeRequest(Data).then((res) => {
                if (res && res.status == true) {
                    enqueueSnackbar(res.message, { variant: 'success', autoHideDuration: 2000, });
                    onClose();
                }
                else if (res && res.status != true) {
                    enqueueSnackbar(res.message, { variant: 'error', autoHideDuration: 2000, });
                    onClose();
                }
                else {
                    enqueueSnackbar("Issue updating Credit Change Request, please try again", { variant: 'error', autoHideDuration: 2000, });
                    onClose();
                }
            }).catch((error) => {
                console.log("error in SaveCoreAddressUsageService");
                enqueueSnackbar("There is some error occured while creating thr request", { variant: 'error', autoHideDuration: 2000, });
                onClose();
            })
        }
    }
    const getStatusClassName = (statusID) => {
        if (statusID === 1) {
            return "created";
        } else if (statusID === 3 || statusID === 4) {
            return "approved";
        } else if (statusID === 2 || statusID === 5) {
            return "rejected";
        } else {
            return "created"; // Default class name if no other condition is met
        }
    };
    const getStatusActionName = (statusID) => {
        if (statusID === 1) {
            return "Created";
        } else if (statusID === 3 || statusID === 4) {
            return "Approved";
        } else if (statusID === 2 || statusID === 5) {
            return "Rejected";
        } else {
            return "Created"; // Default class name if no other condition is met
        }
    };

    const handleBookingHistoryShowOpen = () => {
        setBookingHistoryShow(true);
    }
    const handleCreditChangeRequestClose = () => {
        setBookingHistoryShow(false);
    }

    const UpdateAgentType = (AgentTypeID) => {
        if (AgentTypeID == 1)
            return "Primary - Call Center";
        else if (AgentTypeID == 2)
            return "Secondary - Call Center";
        else if (AgentTypeID == 3)
            return "Primary - FOS";
    }

    const ReRaiseRequest = () => {
        if(bookingdata[0].BookingAgentUserID == RowWiseData[0].NewAdvisorUserID && [1,3].includes(RowWiseData[0].AgentTypeID)){
            enqueueSnackbar("Booking is already mapped to this advisor.", { variant: 'error', autoHideDuration: 2000, });
            return;
        }
        else if(bookingdata[0].SecondaryAgentUserID == RowWiseData[0].NewAdvisorUserID && [2].includes(RowWiseData[0].AgentTypeID)){
            enqueueSnackbar("Secondary booking is already mapped to this advisor.", { variant: 'error', autoHideDuration: 2000, });
            return;
        }
        let reRaiseRequest = {
            BookingID : RowWiseData[0].BookingId,
            AgentTypeID : RowWiseData[0].AgentTypeID,
            NewAgentID : RowWiseData[0].NewAdvisorUserID,
            ReasonID: RowWiseData[0].ReasonID,
            RequestorRemarks: "Re-Raised Request : " + RowWiseData[0].RequestorComment,
            ReferenceId : RowWiseData[0].ReferenceId > 0 ? RowWiseData[0].ReferenceId : 0
        }
        CreateCreditChangeRequest(reRaiseRequest).then((res) => {
            if (res && res.status == true) {
                enqueueSnackbar(res.message, { variant: 'success', autoHideDuration: 2000, });
                onClose();
            }
            else if (res && res.status != true) {
                enqueueSnackbar(res.message, { variant: 'error', autoHideDuration: 2000, });
                onClose();
            }
            else {
                enqueueSnackbar("Issue updating Credit Change Request, please try again", { variant: 'error', autoHideDuration: 2000, });
                onClose();
            }
        }).catch((error) => {
            console.log("error in SaveCoreAddressUsageService");
            enqueueSnackbar("There is some error occured while creating thr request", { variant: 'error', autoHideDuration: 2000, });
            onClose();
        })
    }


    return (
        <>
            {BookingData && Array.isArray(BookingData) && BookingData.length > 0
                && RowWiseData && Array.isArray(RowWiseData) && RowWiseData.length > 0 &&
                <>
                    <Dialog
                        open={open}
                        onClose={onClose}
                        className="CreateCreditChangeRequestPopup"
                    >
                        <DialogTitle>BookingID  - <span>{RowWiseData[0].BookingId}</span>
                            {/* <Button onClick={() => {handleBookingHistoryShowOpen()}} className="BookingHistoryBtn"><HistoryToggleOffOutlinedIcon /> Booking History</Button></DialogTitle> */}
                            <Button onClick={() => { handleBookingHistoryShowOpen() }} className="BookingHistoryBtn"><HistoryToggleOffOutlinedIcon /> View History</Button></DialogTitle>

                        <IconButton
                            aria-label="close"
                            onClick={onClose}
                            sx={{
                                position: 'absolute',
                                right: 8,
                                top: 8,
                                color: (theme) => theme.palette.grey[500],
                            }}
                        >
                            <CloseIcon />
                        </IconButton>
                        <DialogContent>
                            <Grid container spacing={2}>
                                <Grid item sm={5} md={5} xs={12} >
                                <label>Booking ID</label>
                                    <div className="SeperateLine">                                        
                                        {IsAuthorisedCreator == 2 ? 
                                            <a onClick={() => { OpenReadOnlyViewMatrix(RowWiseData[0].BookingId, RowWiseData[0].CustomerID, RowWiseData[0].ProductId) }}>{RowWiseData[0].BookingId !== undefined ? RowWiseData[0].BookingId : ""}</a>
                                        :
                                            <a onClick={() => { OpenLeadContentOnClick(RowWiseData[0].BookingId, RowWiseData[0].CustomerID, RowWiseData[0].ProductId) }}>{RowWiseData[0].BookingId !== undefined ? RowWiseData[0].BookingId : ""}</a>
                                        }
                                    </div>
                                   
                                    <label>Reference ID (Optional)</label>
                                    <div className="SeperateLine">
                                    {RowWiseData[0].ReferenceId > 0 && RowWiseData[0].ReferenceCustId > 0 && RowWiseData[0].ReferenceProdId > 0 ?
                                        (IsAuthorisedCreator == 2 ? 
                                            <a onClick={() => { OpenReadOnlyViewMatrix(RowWiseData[0].ReferenceId, RowWiseData[0].ReferenceCustId, RowWiseData[0].ReferenceProdId) }}>{RowWiseData[0].ReferenceId !== undefined ? RowWiseData[0].ReferenceId : ""}</a>
                                        :
                                            <a onClick={() => { OpenLeadContentOnClick(RowWiseData[0].ReferenceId, RowWiseData[0].ReferenceCustId, RowWiseData[0].ReferenceProdId) }}>{RowWiseData[0].ReferenceId !== undefined ? RowWiseData[0].ReferenceId : ""}</a>
                                        )
                                        : "-"}
                                       </div> 
                                    <label>New advisor</label>
                                    <TextInput
                                        name="NewAdvisor"
                                        sm={12} md={12} xs={12}
                                        value={`${RowWiseData[0].NewAdvisorUserName} - ${RowWiseData[0].NewAdvisorEmpCode}`}
                                        disabled={true}
                                    />
                                    <Grid container spacing={2}>
                                        <Grid item sm={6} md={6} xs={12} >
                                            <label>Agent Type</label>
                                            <TextInput
                                                name="AgentType"
                                                sm={12} md={12} xs={12}
                                                value={UpdateAgentType(RowWiseData[0].AgentTypeID)}
                                                disabled={true}
                                            />
                                        </Grid>
                                        <Grid item sm={6} md={6} xs={12} >
                                            <label>Reason for change</label>
                                            <TextInput
                                                name="ReasonforChange"
                                                sm={12} md={12} xs={12}
                                                value={RowWiseData[0].Reason}
                                                disabled={true}
                                            />
                                        </Grid>
                                    </Grid>
                                    <label>Remarks</label>
                                    <Textarea
                                        name="RequestorRemarks"
                                        minRows={3}
                                        value={RowWiseData[0].RequestorComment}
                                        disabled={true}
                                    />
                                    <Grid container spacing={2}>
                                        <Grid item sm={6} md={6} xs={12} >
                                            <label>1st Approver Remarks</label>
                                            <Textarea
                                                name="FirstLevelRemarks"
                                                minRows={3}
                                                placeholder="Enter Remarks here(4-400 characters)…"
                                                onChange={handleChange}
                                                value={(IsFirstLevel && CurrentStatusID == 1 && !(RowWiseData[0].FirstLevelComment && RowWiseData[0].FirstLevelComment.length > 0)) ? Comments : RowWiseData[0].FirstLevelComment}
                                                disabled={!(IsFirstLevel && CurrentStatusID == 1)}
                                                className={!(IsFirstLevel && CurrentStatusID === 1) ? "" : "highlight-textarea"}
                                            />
                                        </Grid>
                                        <Grid item sm={6} md={6} xs={12} >
                                            {[2,117].indexOf(ProductSelect) > -1 ? <label>BU Team Remarks</label> : <label>MIS Team Remarks</label>}
                                            <Textarea
                                                name="SecondLevelRemarks"
                                                minRows={3}
                                                placeholder="Enter Remarks here(4-400 characters)…"
                                                onChange={handleChange}
                                                value={(IsSecondLevel && CurrentStatusID == 3 && !(RowWiseData[0].SecondLevelComment && RowWiseData[0].SecondLevelComment.length > 0)) ? Comments : RowWiseData[0].SecondLevelComment}
                                                disabled={!(IsSecondLevel && CurrentStatusID == 3)}
                                                className={!(IsSecondLevel && CurrentStatusID === 3) ? "" : "highlight-textarea"}
                                            />
                                        </Grid>
                                    </Grid>
                                </Grid>
                                <Grid item sm={7} md={7} xs={12}>
                                    <BookingDetails data={BookingData} />
                                    {ActivityLogs && ActivityLogs.length > 0 &&
                                        <>
                                            <h4>Activity log</h4>
                                            <ul className="ActivityLog">
                                                {ActivityLogs.map((entry, index) => (
                                                    <React.Fragment key={index}>
                                                        <li>
                                                            <CircleIcon className={getStatusClassName(entry.StatusID)} />
                                                            Request {getStatusActionName(entry.StatusID)} By &nbsp;
                                                            <strong>{entry.UserName} - {entry.EmployeeCode}</strong>
                                                        </li>
                                                        <p className="dateTime">
                                                            {new Date(entry.Createdon).toLocaleDateString()} - {new Date(entry.Createdon).toLocaleTimeString()}
                                                        </p>
                                                    </React.Fragment>
                                                ))}
                                            </ul>
                                        </>
                                    }
                                </Grid>
                            </Grid>

                        </DialogContent>
                        {((IsFirstLevel && CurrentStatusID == 1) || (IsSecondLevel && CurrentStatusID == 3)) &&
                            <DialogActions>
                                {!(isValidatedRequest && (Comments && Comments != '' && Comments.trim().length > 3 && Comments.trim().length < 400)) ? <p>Ticket action disabled, add approver Remarks first</p> : <p></p>}
                                <div>
                                    <Button className={isValidatedRequest && (Comments && Comments != '' && Comments.trim().length > 3 && Comments.trim().length < 400 && !IsApproveButtonClick) ? "CreateBtn decLineBtn" : "CreateBtn DisabledecLineBtn"} onClick={() => { SubmitRequest(2) }}
                                        disabled={!(isValidatedRequest && (Comments && Comments != '' && Comments.trim().length > 3 && Comments.trim().length < 400))}>Decline</Button>
                                    <Button className={isValidatedRequest && (Comments && Comments != '' && Comments.trim().length > 3 && Comments.trim().length < 400 && !IsApproveButtonClick) ? "CreateBtn approvedBtn" : "CreateBtn DisableapprovedBtn"} onClick={() => { SubmitRequest(1) }}
                                        disabled={!(isValidatedRequest && (Comments && Comments != '' && Comments.trim().length > 3 && Comments.trim().length < 400))}>Approve</Button>
                                </div>
                            </DialogActions>
                        }
                        {
                            (CurrentStatusID == 2 && RowWiseData[0].RequestedByUserID == User.UserId) && 
                            <DialogActions>
                                {!IsValidReRequest ? <p>You can re-raise the request</p> : <p>A re-raise request is allowed within 10 days of the booking date.</p>}
                                <div>
                                    <Button className={!IsValidReRequest ? "CreateBtn approvedBtn" : "CreateBtn DisabledecLineBtn"} onClick={() => { ReRaiseRequest() }}
                                        disabled={IsValidReRequest}>Re-Raise Request</Button>
                                </div>
                            </DialogActions>
                        }
                    </Dialog>

                    <BookingHistory open={BookingHistoryShow} onClose={handleCreditChangeRequestClose} bookingData={NewRequestData} />
                </>

            }

        </>
    );
}