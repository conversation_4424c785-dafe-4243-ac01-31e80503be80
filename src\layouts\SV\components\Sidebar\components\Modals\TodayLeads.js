import { Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import ModalPopup from "../../../../../../components/Dialogs/ModalPopup";
import { JsonToNormalDate } from "../../../../../../utils/utility";
import dayjs from "dayjs";
import { useSnackbar } from "notistack";
import { connect } from "react-redux";
import { AddLeadToPriorityQueueService} from "../../../../../../services/Common";
import User from "../../../../../../../src/services/user.service";
import rootScopeService from "../../../../../../../src/services/rootScopeService";
import TipsAndUpdatesIcon from "@mui/icons-material/TipsAndUpdates";
import { setRefreshAgentStats } from "../../../../../../store/actions/SalesView/SalesView";


const TodayLeads = (props) => {
    let [rows, setRows] = useState([...(props.data !== undefined ? props.data : [])]);
    const { enqueueSnackbar } = useSnackbar();

    useEffect(() => {
        setRows([...(props.data !== undefined ? props.data : [])])
    }, [JSON.stringify(props.data)]);

    const clearAll = () => {
        setRows(
            rows.map((row) => ({ ...row, IsChecked: false }))
        );
    }
    const close = () => {
        clearAll();
        props.handleClose();
    }
    const handleChange = (e, LeadId) => {
        let checked = e.target.checked;
        let selectedLeads = rows.filter((lead) => lead.IsChecked);
        if (selectedLeads.length >= 5 && checked) {
            alert("You can not add more then 5 leads.");
            return false;
        }
        setRows(
            rows.map((row, Index) => {
                if (row.LeadID === LeadId) {
                    row.IsChecked = checked;
                }
                return row;
            }));
    }

    const AddLeadsToQueue = () => {
        let IscheckedLeads = rows.filter((lead) => lead.IsChecked);
        if (IscheckedLeads.length == 0) {
            enqueueSnackbar("Please Select Leads.", { variant: 'error', autoHideDuration: 3000, });
            return;
        }

        IscheckedLeads.forEach((items) => {

            var priorityLead = {
                "LeadId": items.LeadID,
                "Name": items.CustName,
                "CustomerId": items.CustID,
                "UserID": User.UserId,
                "Priority": 1,
                "ProductId": items.ProductID || rootScopeService.getProductId(),
                "Reason": 'Manual added',
                "ReasonId": items.IsNBTSorted ? 80 : 33,
                "CallStatus": "",
                "IsAddLeadtoQueue": 1,
                "IsNeedToValidate": 1
            }

            AddLeadToPriorityQueueService(priorityLead).then((resultData) => {
                if (resultData != null) {
                    if (resultData && resultData.message && resultData.message == "Success") {
                        enqueueSnackbar("Lead " + resultData.LeadID + " Added successfully", {
                            variant: 'success',
                            autoHideDuration: 3000,
                        });
                    }
                    else {
                        let error = (resultData && resultData.message && resultData.message !== '')
                            ? (resultData.LeadID + " : " + resultData.message)
                            : "Error while adding lead: " + resultData.LeadID;
                        enqueueSnackbar(error, { variant: 'error', autoHideDuration: 2000, });
                    }
                }
            })
        });
        props.setRefreshAgentStatsToRedux(true);
        close();
    };

    return (
        <ModalPopup open={props.open} title='Todays Lead' handleClose={close} className="addmoreLeadPopup">
            {rows.some(row => row.IsNBTSorted === true) && (
                <div className="tip-container">
                    <div className="tip-accent-bar"></div>
                    <div className="tip-icon">
                        <TipsAndUpdatesIcon />
                    </div>
                    <div>
                        <Typography variant="body1" className="tip-text">
                            <span className="tip-label">Tip:</span> Leads highlighted in yellow have a <em className="tip-emphasis">higher chance</em> of picking up your call right now
                        </Typography>
                    </div>
                </div>
            )}
            {
                rows.length === 0 ? <h3 className="nodataFound">No Data Found</h3>  :
                <>
                <div className="content">
                    <TableContainer component={Paper} className="reassigned-table">
                        <Table aria-label="simple table">
                            <TableHead>
                                <TableRow>
                                    <TableCell align="left">Sno</TableCell>
                                    <TableCell align="left">Lead Id</TableCell>
                                    <TableCell align="left">Name</TableCell>
                                    <TableCell align="left">AssignedAt</TableCell>
                                    <TableCell align="left">Status</TableCell>
                                    <TableCell align="left">Add Lead</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {rows.map((row, i) => (
                                    <TableRow 
                                        key={i} 
                                        className="moreDeatilsData"
                                        style={row.IsNBTSorted === true ? {
                                            background: 'linear-gradient(90deg, rgb(255 249 180) 0%, rgb(251 245 188) 49%, rgba(255, 255, 255, 1) 100%)'
                                        } : {}}
                                    >
                                        <TableCell align="left">{i + 1}</TableCell>
                                        <TableCell align="left">{row.LeadID !== undefined ? row.LeadID : ""}</TableCell>
                                        <TableCell align="left">{row.CustName !== undefined ? row.CustName : ""}</TableCell>
                                        <TableCell align="left">
                                        {row.User.AssignedOn !== undefined ?
                                            dayjs(row.User.AssignedOn).format('DD/MM/YYYY h:mm:ss a') : ""}
                                        </TableCell>
                                        <TableCell align="left">
                                        {row.LeadStatus && row.LeadStatus.Status !== undefined ?
                                            row.LeadStatus.Status : 'N.A'}
									    </TableCell>
                                        <TableCell align="left" >
                                            <input
                                                type="checkbox"
                                                name={row.LeadID}
                                                value={row.IsChecked || false}
                                                checked={row.IsChecked || false}
                                                onChange={(event) => { handleChange(event, row.LeadID) }}
                                            />
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                    </div>
                    <div className="text-center">
                            <button onClick={clearAll} className="clearAllBtn">Clear All</button>
                            <button onClick={() => { AddLeadsToQueue() }} className="addLeadBtn">Add Leads</button>
                        </div>
                    </>
            }
        </ModalPopup>
    )
};
const mapDispatchToProps = dispatch => {
    return {
        setRefreshAgentStatsToRedux: (value) => dispatch(setRefreshAgentStats({ RefreshAgentStats: value })),
    };
};
export default connect(() => ({}), mapDispatchToProps)(TodayLeads);
